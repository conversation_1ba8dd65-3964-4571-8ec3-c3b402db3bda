import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:provider/provider.dart';

class WidgetUtil {
  /// 页面底部的按钮
  static Widget getBottomButton({
    required BuildContext context,
    required String title,
    required VoidCallback onSubmit,
    Widget? content,
    Color? backgroundColor,
    Color? color,
    double? height,
    Color? wrapperColor,
    EdgeInsetsGeometry? padding,
    Decoration? decoration,
  }) {
    return getBottomWrapper(
      context: context,
      padding: padding,
      decoration: decoration,
      color: wrapperColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (content != null) content,
          PrimaryButton(
            title: title,
            onPress: onSubmit,
            backgroundColor: backgroundColor,
            color: color,
            height: height,
          ),
        ],
      ),
    );
  }

  /// 获取页面底部的容器
  static Widget getBottomWrapper({
    required BuildContext context,
    required Widget child,
    Color? color,
    EdgeInsetsGeometry? padding,
    Decoration? decoration,
  }) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    return Container(
      padding: padding ?? EdgeInsets.symmetric(vertical: 7.w, horizontal: 15.w),
      decoration:
          decoration ??
          BoxDecoration(
            color: color ?? customTheme.inputBgColor,
            border: Border(
              top: BorderSide(color: customTheme.dividerColor, width: 0.5),
            ),
          ),
      child: SafeArea(top: false, child: child),
    );
  }

  static ClassicFooter getClassicFooter({
    IndicatorPosition position = IndicatorPosition.above,
    double? infiniteOffset = 70,
    double triggerOffset = 70,
  }) {
    return ClassicFooter(
      position: position,
      dragText: 'pull_to_load'.tr,
      armedText: 'release_ready'.tr,
      readyText: 'loading'.tr,
      processingText: 'loading'.tr,
      processedText: 'succeeded'.tr,
      noMoreText: 'no_more'.tr,
      failedText: 'failed'.tr,
      messageText: 'last_updated_at'.tr,
      infiniteOffset: infiniteOffset,
      triggerOffset: triggerOffset,
    );
  }

  static ClassicHeader getClassicHeader({
    IndicatorPosition position = IndicatorPosition.above,
    double? infiniteOffset,
    double triggerOffset = 70,
  }) {
    return ClassicHeader(
      position: position,
      dragText: 'pull_to_refresh'.tr,
      armedText: 'release_ready'.tr,
      readyText: 'refreshing'.tr,
      processingText: 'refreshing'.tr,
      processedText: 'succeeded'.tr,
      noMoreText: 'no_more'.tr,
      failedText: 'failed'.tr,
      messageText: 'last_updated_at'.tr,
      infiniteOffset: infiniteOffset,
      triggerOffset: triggerOffset,
    );
  }

  /// easy_refresh 第一次加载的logo
  static BuilderHeader getRefreshOnStartHeader(BuildContext context) {
    return BuilderHeader(
      triggerOffset: 70,
      clamping: true,
      position: IndicatorPosition.above,
      processedDuration: Duration.zero,
      builder: (ctx, state) {
        if (state.mode == IndicatorMode.inactive ||
            state.mode == IndicatorMode.done) {
          return const SizedBox();
        }
        return Container(
          padding: const EdgeInsets.only(bottom: 100),
          width: MediaQuery.of(context).size.width,
          height: state.viewportDimension,
          alignment: Alignment.center,
          child: getLoadingIcon(context),
        );
      },
    );
  }

  static Widget getLoadingIcon(
    BuildContext context, {
    Color? color,
    double? size,
  }) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    return LoadingAnimationWidget.fourRotatingDots(
      color: color ?? customTheme.textColor,
      size: size ?? 50,
    );
  }
}
