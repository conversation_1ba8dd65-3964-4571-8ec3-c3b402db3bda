import 'package:aurenixai_app/utils/http_util.dart' show isSuccess, getErrorMsg;
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:aurenixai_app/config/index.dart';

typedef OnProgressCallback = void Function(int count, int data);

/// 阿里OSS文件上传
class AwsUploadUtil {
  AwsUploadUtil._internal();

  static final AwsUploadUtil _singleton = AwsUploadUtil._internal();

  factory AwsUploadUtil() => _singleton;

  /// 获取上传参数
  Future<FormData> getUploadParams(XFile xfile) async {
    MultipartFile file;
    if (kIsWeb) {
      file = MultipartFile.fromBytes(
        await xfile.readAsBytes(),
        filename: xfile.name,
      );
    } else {
      file = await MultipartFile.fromFile(xfile.path, filename: xfile.name);
    }

    // 请求参数的form对象
    FormData data = FormData.fromMap({
      'contentType': 'multipart/form-data',
      'file': file,
    });
    return data;
  }

  /// 图片上传
  Future<String> upload({
    required XFile file,
    OnProgressCallback? onSendProgress,
  }) async {
    FormData data = await getUploadParams(file);

    BaseOptions options = BaseOptions();
    Dio dio = Dio(options);

    try {
      String baseUrl = AppConfig.apiUrl;

      final response = await dio.post(
        '$baseUrl/core/v1/file/public/upload',
        data: data,
        onSendProgress: (int count, int data) {
          if (onSendProgress != null) {
            onSendProgress(count, data);
          }
        },
      );
      if (isSuccess(response)) {
        return response.data["data"]["imageUrl"];
      }
      String errorMsg = getErrorMsg(response);
      throw DioException(
        requestOptions: response.requestOptions,
        error: errorMsg,
      );
    } catch (e) {
      throw Exception(e);
    }
  }

  /// 获取上传参数
  Future<FormData> getUploadByUint8ListParams(Uint8List bytes) async {
    MultipartFile file;
    file = MultipartFile.fromBytes(bytes, filename: 'signature.png');

    // 请求参数的form对象
    FormData data = FormData.fromMap({
      'contentType': 'multipart/form-data',
      'file': file,
    });
    return data;
  }

  /// bytes图片上传
  Future<String> uploadByUint8List({
    required Uint8List bytes,
    OnProgressCallback? onSendProgress,
  }) async {
    FormData data = await getUploadByUint8ListParams(bytes);

    BaseOptions options = BaseOptions();
    Dio dio = Dio(options);

    try {
      String baseUrl = AppConfig.apiUrl;

      final response = await dio.post(
        '$baseUrl/core/v1/file/public/upload',
        data: data,
        onSendProgress: (int count, int data) {
          if (onSendProgress != null) {
            onSendProgress(count, data);
          }
        },
      );
      if (isSuccess(response)) {
        return response.data["data"]["imageUrl"];
      }
      String errorMsg = getErrorMsg(response);
      throw DioException(
        requestOptions: response.requestOptions,
        error: errorMsg,
      );
    } catch (e) {
      throw Exception(e);
    }
  }
}

final awsUploadUtil = AwsUploadUtil();
