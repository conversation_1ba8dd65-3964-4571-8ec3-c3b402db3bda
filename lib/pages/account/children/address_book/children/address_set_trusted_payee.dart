import 'package:aurenixai_app/api/address_api.dart';
import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddressSetTrustedPayeePage extends StatefulWidget {
  const AddressSetTrustedPayeePage({super.key});

  @override
  _AddressSetTrustedPayeePageState createState() =>
      _AddressSetTrustedPayeePageState();
}

class _AddressSetTrustedPayeePageState extends State<AddressSetTrustedPayeePage>
    with SingleTickerProviderStateMixin {
  final TextEditingController passwordController = TextEditingController();
  TextEditingController emailCodeController = TextEditingController();
  TextEditingController googleCodeController = TextEditingController();
  List<String>? idList = [];

  @override
  void initState() {
    super.initState();
    final args = Get.parameters;
    String ids = args['idList'] ?? '';
    setState(() {
      idList = ids.split(',');
    });
  }

  @override
  void dispose() {
    passwordController.dispose();
    emailCodeController.dispose();
    googleCodeController.dispose();
    super.dispose();
  }

  void onConfirm() async {
    if (passwordController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_password'.tr);
      return;
    }
    if (emailCodeController.text.isEmpty) {
      ToastUtil.showToast('please_enter_email_verification_code'.tr);
      return;
    }
    if (googleCodeController.text.isEmpty) {
      ToastUtil.showToast('please_enter_google_verification_code'.tr);
      return;
    }
    ToastUtil.showLoading();
    try {
      await AddressApi.addressSetTrusted({
        'id': idList?[0],
        'loginPassword': passwordController.text,
        'emailCode': emailCodeController.text,
        'googleCode': googleCodeController.text,
      });
      Get.back();
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  void sendCode() async {
    final arguments = Get.arguments;
    final email = arguments['email'];
    ToastUtil.showLoading();
    try {
      await CommonApi.sendEmailCode(
        email: email,
        bizType: SmsBizType.registerEmail,
      );
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: Text('set_trusted_payee'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 25.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'account_password'.tr,
                        style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w400,
                          color: customTheme.textColor,
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {},
                        child: Text(
                          'forgot_password'.tr,
                          style: TextStyle(
                            fontSize: 13.sp,
                            height: 1,
                            fontWeight: FontWeight.w400,
                            color: customTheme.primaryColor,
                            decoration: TextDecoration.underline,
                            decorationColor: customTheme.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 8.h),
                  NormalInput(
                    controller: passwordController,
                    hintText: 'enter_your_password'.tr,
                    obscureText: true,
                  ),
                  SizedBox(height: 18.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "email_verification_code".tr,
                        controller: emailCodeController,
                        padding: EdgeInsets.only(left: 15.w, right: 80.w),
                      ),
                      Positioned(
                        bottom: 15.w,
                        right: 15.w,
                        child: PrimaryButton(
                          title: 'send_code'.tr,
                          height: 28.w,
                          isInline: true,
                          padding: EdgeInsets.symmetric(horizontal: 9.w),
                          onPress: () {},
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 18.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "google_verification_code".tr,
                        controller: googleCodeController,
                        padding: EdgeInsets.only(left: 15.w, right: 80.w),
                      ),
                      Positioned(
                        bottom: 15.w,
                        right: 15.w,
                        child: PrimaryButton(
                          title: 'paste'.tr,
                          height: 28.w,
                          isInline: true,
                          padding: EdgeInsets.symmetric(horizontal: 9.w),
                          onPress: () {
                            Clipboard.getData(Clipboard.kTextPlain).then((
                              value,
                            ) {
                              if (value != null) {
                                setState(() {
                                  googleCodeController.text = value.text ?? '';
                                });
                              }
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Text(
                    'google_verification_code_tips'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: customTheme.hintTextColor,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'Confirm'.tr,
            onSubmit: onConfirm,
          ),
        ],
      ),
    );
  }
}
