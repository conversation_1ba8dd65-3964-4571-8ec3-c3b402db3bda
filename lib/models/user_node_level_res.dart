import 'package:json_annotation/json_annotation.dart';

import 'fund_investment_record_user_level_res.dart';

part 'user_node_level_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class UserNodeLevelRes {
  UserNodeLevelRes();

  late String id;
  late String userId;
  late String userName;
  late String nodeId;
  late String nodeType;
  late String name;
  String? pic;
  late num rewardsRate;
  late String rewardsRateType;
  late num performance;
  late num maintainPerformance;
  late String applyStatus;
  late num createDatetime;
  late List<FundInvestmentRecordUserLevelRes>
  fundInvestmentRecordUserLevelPageResList;

  factory UserNodeLevelRes.fromJson(Map<String, dynamic> json) =>
      _$UserNodeLevelResFromJson(json);
  Map<String, dynamic> toJson() => _$UserNodeLevelResToJson(this);
}
