import 'package:json_annotation/json_annotation.dart';

part 'check_google_secret_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class CheckGoogleSecretRes {
  CheckGoogleSecretRes();

  late String userGoogleSecretFlag;
  late String needGoogleSecretFlag;
  factory CheckGoogleSecretRes.fromJson(Map<String, dynamic> json) =>
      _$CheckGoogleSecretResFromJson(json);
  Map<String, dynamic> toJson() => _$CheckGoogleSecretResToJson(this);
}
