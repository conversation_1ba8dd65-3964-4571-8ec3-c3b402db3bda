import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class RencentActivity extends StatefulWidget {
  const RencentActivity({super.key});

  @override
  _RencentActivityState createState() => _RencentActivityState();
}

class _RencentActivityState extends State<RencentActivity> {
  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Column(
      spacing: 10.w,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          spacing: 6.w,
          children: [
            Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 16.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            GestureDetector(
              onTap: () {
                EventBusUtil.fireSwitchTab(SwitchTabEvent(tabIndex: 3));
              },
              child: RichText(
                text: TextSpan(
                  text: 'See more',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: customTheme.subTextColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ],
        ),
        _item(0),
        _item(1),
        _item(2),
        _item(3),
        _item(4),
        // _empty(),
      ],
    );
  }

  Widget _item(int index) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(3.w),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.5.w),
        child: Row(
          spacing: 9.5.w,
          children: [
            Image.asset(
              index == 0
                  ? customThemeImage.homeAdd
                  : customThemeImage.homeReduce,
              width: 22.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 2.5.w,
                children: [
                  Text(
                    'Thu, 10 Apr 2025',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                  Text(
                    index == 1 ? 'Withdraw' : 'Returns',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (index == 1)
                    Text(
                      '112233445566  Pending',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                ],
              ),
            ),
            Text(
              '\$ XXXX',
              style: TextStyle(
                fontSize: 15.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _empty() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Container(
      padding: EdgeInsets.only(top: 15.w),
      child: Column(
        spacing: 13.5.w,
        children: [
          Image.asset(customThemeImage.commonEmpty, width: 80.w, height: 80.w),
          Text(
            'No recent activity',
            style: TextStyle(fontSize: 13.sp, color: customTheme.subTextColor),
          ),
        ],
      ),
    );
  }
}
