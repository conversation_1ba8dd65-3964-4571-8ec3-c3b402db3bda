import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/country_region_picker.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class EditMobile extends StatefulWidget {
  const EditMobile({super.key});

  static Future<void> showModal(BuildContext context) async {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return EditMobile();
      },
    );
  }

  @override
  State<EditMobile> createState() => _EditMobileState();
}

class _EditMobileState extends State<EditMobile> {
  Region? currentCountry;
  String currentPhoneNumber = '';
  final TextEditingController _phoneNumberController = TextEditingController();
  Region? newCountry;
  bool isCompleted = false; // 是否已经修改完成

  @override
  void initState() {
    super.initState();

    final interCode = context
        .read<AuthProvider>()
        .user!
        .interCode; // 这个是APP当前选择的国家/地区
    currentCountry = context.read<LanguageProvider>().regionList.firstWhere(
      (element) => element.interCode == interCode,
      orElse: () => context.read<LanguageProvider>().region!,
    );
    // 没有绑定过手机的说明是免除KYC的用户
    currentPhoneNumber = context.read<AuthProvider>().user?.mobile ?? '';
    newCountry = context.read<LanguageProvider>().region;
  }

  @override
  void dispose() {
    _phoneNumberController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    if (_phoneNumberController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_new_phone_number'.tr);
      return;
    }

    ToastUtil.showLoading();
    try {
      await UserApi.modifyMobile(
        newMobile: _phoneNumberController.text,
        countryId: newCountry!.id,
      );
      context.read<AuthProvider>().modifyMobile(
        _phoneNumberController.text,
        newCountry!.interCode,
      );
      EventBusUtil.fireModifyMobile(
        ModifyMobileEvent(
          mobile: _phoneNumberController.text,
          countryCode: newCountry!.interCode,
        ),
      );
    } catch (e) {}
    ToastUtil.dismiss();
    setState(() {
      isCompleted = true;
    });
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'basic_info'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  Widget _buildEditContent() {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTopBar(context),
        SizedBox(height: 22.5.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (currentPhoneNumber.isNotEmpty)
                Text(
                  'current_phone_number'.tr,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: customTheme.textColor,
                  ),
                ),
              if (currentPhoneNumber.isNotEmpty) SizedBox(height: 8.h),
              if (currentPhoneNumber.isNotEmpty)
                Row(
                  spacing: 8.w,
                  children: [
                    Container(
                      height: 55.w,
                      padding: EdgeInsets.only(left: 12.5.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3.w),
                        color: customTheme.inputBgColor,
                      ),
                      constraints: BoxConstraints(minWidth: 97.w),
                      child: Row(
                        children: [
                          if (currentCountry != null)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(11.w),
                              child: Image.network(
                                currentCountry!.pic,
                                height: 16.w,
                                width: 16.w,
                                fit: BoxFit.fill,
                              ),
                            ),
                          SizedBox(width: 7.w),
                          if (currentCountry != null)
                            Text(
                              currentCountry!.interCode,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: customTheme.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: NormalInput(readonlyText: currentPhoneNumber),
                    ),
                  ],
                ),
              if (currentPhoneNumber.isNotEmpty) SizedBox(height: 19.h),
              Text(
                'new_phone_number'.tr,
                style: TextStyle(fontSize: 13.sp, color: customTheme.textColor),
              ),
              SizedBox(height: 8.h),
              Row(
                spacing: 8.w,
                children: [
                  GestureDetector(
                    onTap: () {
                      CountryRegionPicker.showModal(
                            context,
                            currentRegion: newCountry,
                          )
                          .then((region) {
                            if (region != null) {
                              setState(() {
                                newCountry = region;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                    child: Container(
                      height: 55.w,
                      constraints: BoxConstraints(minWidth: 97.w),
                      padding: EdgeInsets.only(left: 12.5.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3.w),
                        color: customTheme.inputBgColor,
                      ),
                      child: Row(
                        children: [
                          if (newCountry != null)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(11.w),
                              child: Image.network(
                                newCountry!.pic,
                                height: 16.w,
                                width: 16.w,
                                fit: BoxFit.fill,
                              ),
                            ),
                          SizedBox(width: 7.w),
                          if (newCountry != null)
                            Text(
                              newCountry!.interCode,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: customTheme.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          Icon(
                            Icons.arrow_drop_down_rounded,
                            size: 32.w,
                            color: customTheme.textColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: NormalInput(
                      hintText: 'enter_your_new_phone_number'.tr,
                      controller: _phoneNumberController,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: 40.h),
        WidgetUtil.getBottomButton(
          context: context,
          title: 'confirm'.tr,
          onSubmit: onSubmit,
        ),
      ],
    );
  }

  Widget _buildCompletedContent() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 275.h,
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    SizedBox(height: 39.h),
                    Image.asset(
                      customThemeImage.servicesPhone,
                      width: 80.w,
                      height: 80.w,
                      fit: BoxFit.fill,
                    ),
                    SizedBox(height: 36.5.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Text(
                        'update_complete'.tr,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: customTheme.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Text(
                        'your_new_phone_number_is_now_saved'.tr,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: customTheme.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: 70.h),
                  ],
                ),
              ),
              Positioned(
                top: 23.w,
                right: 10.w,
                child: TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: false,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: isCompleted ? _buildCompletedContent() : _buildEditContent(),
      ),
    );
  }
}
