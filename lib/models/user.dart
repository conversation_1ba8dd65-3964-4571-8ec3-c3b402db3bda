import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  User();

  late String id;
  late String userId;
  late String photo;
  late String availableAmount;
  late String identifyStatus;
  String? failRemark;
  late String status;
  late num registerDatetime;
  late String nickname;
  late String inviteCode;
  String? mobile;
  late String email;
  late String levelName;
  late String levelPic;
  late num level;
  late String nodeType;
  late num nodeLevel;
  late String nodeLevelName;
  String? nodePic;
  late String interCode;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
