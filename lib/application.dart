// ignore_for_file: use_build_context_synchronously

import 'dart:async' show Timer, StreamSubscription;
import 'dart:convert' show jsonDecode;

import 'package:aurenixai_app/config/index.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/l10n/translations.dart';
import 'package:aurenixai_app/models/kline_res.dart';
import 'package:aurenixai_app/models/websocket_res.dart';
import 'package:aurenixai_app/pages/public/empty.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/recaptcha_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:aurenixai_app/utils/theme.dart';
import 'package:aurenixai_app/utils/web_socket_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:crisp_chat/crisp_chat.dart' show FlutterCrispChat;
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart' show EasyLoading;
import 'package:flutter_localizations/flutter_localizations.dart'
    show
        GlobalCupertinoLocalizations,
        GlobalMaterialLocalizations,
        GlobalWidgetsLocalizations;
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart'
    show ChangeNotifierProvider, MultiProvider, Consumer, ReadContext;
import 'package:recaptcha_enterprise_flutter/recaptcha_client.dart';

class Application extends StatefulWidget {
  final RecaptchaClient? client;

  const Application({super.key, this.client});

  @override
  _ApplicationState createState() => _ApplicationState();
}

class _ApplicationState extends State<Application> {
  int loginFlag = 0; // 0: 未初始化 1: 已登录 2: 未登录

  @override
  void initState() {
    super.initState();
    LocalUtil.getUserToken()
        .then((value) {
          setState(() {
            loginFlag = value != null ? 1 : 2;
          });
        })
        .catchError((e) {
          setState(() {
            loginFlag = 2;
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    if (loginFlag == 0) {
      return const SizedBox();
    }

    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (context) => AuthProvider()),
            ChangeNotifierProvider(create: (context) => ThemeProvider()),
            ChangeNotifierProvider(create: (context) => RecaptchaProvider()),
            ChangeNotifierProvider(create: (context) => LanguageProvider()),
          ],
          child: Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return GetMaterialApp(
                title: AppConfig.appName,
                debugShowCheckedModeBanner: false,
                translations: AppTranslations(),
                supportedLocales: AppTranslations.supportedLocales,
                localeResolutionCallback: (locale, supportedLocales) {
                  if (locale?.languageCode.toLowerCase() == 'zh') {
                    return supportedLocales.last;
                  }
                  return supportedLocales.first;
                },
                localizationsDelegates: const [
                  GlobalCupertinoLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                ],
                theme: themeProvider.appTheme,
                initialRoute: loginFlag == 1 ? Routes.home : Routes.auth,
                unknownRoute: GetPage(
                  name: Routes.empty,
                  page: () => const EmptyPage(),
                ),
                getPages: Routes.getPages,
                builder: (context, _widget) {
                  return EasyLoading.init()(
                    context,
                    InitWrapper(
                      client: widget.client,
                      child: _widget ?? const SizedBox(),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  /// 点击屏幕其它区域隐藏键盘
  void hideKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }
}

class InitWrapper extends StatefulWidget {
  const InitWrapper({super.key, required this.child, this.client});
  final RecaptchaClient? client;
  final Widget child;

  @override
  _InitWrapperState createState() => _InitWrapperState();
}

class _InitWrapperState extends State<InitWrapper> {
  StreamSubscription<UserLoginEvent>? loginSubscription;
  StreamSubscription<UserLogoutEvent>? logoutSubscription;

  int initFlag = 0;
  bool isInit = false;
  @override
  void initState() {
    Timer.run(initRecaptcha);
    Timer.run(initVersionAndUserInfo);
    Timer.run(initTheme);
    Timer.run(initLocaleAndRegion);
    Timer.run(initEasyRefresh);
    Timer.run(initWebSocket);

    super.initState();
  }

  @override
  void dispose() {
    loginSubscription?.cancel();
    logoutSubscription?.cancel();
    super.dispose();
  }

  refreshFlag() {
    initFlag++;
    // 除了Recaptcha，其它都初始化完成
    if (initFlag == 3) {
      FlutterNativeSplash.remove();
      setState(() {
        isInit = true;
      });
    }
  }

  initRecaptcha() {
    if (widget.client != null) {
      RecaptchaProvider recaptchaProvider = context.read<RecaptchaProvider>();
      recaptchaProvider.setClient(widget.client!);
    }
  }

  /// 初始化用户信息
  initVersionAndUserInfo() async {
    // TODO: 判断是否需要更新版本
    // await checkVersion();
    await initUserInfo();
  }

  /// 初始化用户和欢迎页
  initUserInfo() async {
    try {
      final token = await LocalUtil.getUserToken();
      if (token != null && mounted) {
        AuthProvider authInfo = context.read<AuthProvider>();
        await authInfo.setToken(token);
      }
    } catch (e) {}
    refreshFlag();
  }

  /// 初始化主题
  initTheme() {
    ThemeProvider themeProvider = context.read<ThemeProvider>();
    ThemeUtil.init(themeProvider);
    refreshFlag();
  }

  /// 初始化语言
  initLocaleAndRegion() async {
    try {
      final locale = await LocalUtil.getLocale();
      final region = await LocalUtil.getRegion();
      Get.updateLocale(locale);
      if (region != null) {
        LanguageProvider languageProvider = context.read<LanguageProvider>();
        final regions = await languageProvider.getRegionList();
        // Map<String, String> regionMap = {};
        // for (var element in regions) {
        //   regionMap[element.id] = element.interName;
        // }
        // Clipboard.setData(ClipboardData(text: jsonEncode(regionMap)));
        final _region = regions.firstWhere(
          (element) => element.id == region.id,
        );
        languageProvider.setLanguageAndRegion(locale, _region);
      } else {
        LanguageProvider languageProvider = context.read<LanguageProvider>();
        await languageProvider.getRegionList();
        final _region = languageProvider.regionList.first;
        languageProvider.setLanguageAndRegion(locale, _region);
      }
    } catch (e) {}
    refreshFlag();
  }

  initEasyRefresh() {
    EasyRefresh.defaultHeaderBuilder = () => WidgetUtil.getClassicHeader();
    EasyRefresh.defaultFooterBuilder = () => WidgetUtil.getClassicFooter();
  }

  initWebSocket() {
    WebSocketUtil().initWebSocket(
      onOpen: () async {
        WebSocketUtil().initHeartBeat();
      },
      onMessage: (data) {
        handleMessage(data);
      },
      onError: (e) {
        debugPrint('WebSocket Error: $e');
      },
    );
    loginSubscription = EventBusUtil.listenLogin((event) {
      WebSocketUtil().openSocket(event.userId);
    });
    logoutSubscription = EventBusUtil.listenUserLogout((event) {
      WebSocketUtil().closeSocket(forceClose: true);
      FlutterCrispChat.resetCrispChatSession();
    });
  }

  handleMessage(data) {
    debugPrint('WebSocket消息：$data');
    try {
      final jsonData = jsonDecode(data);
      if (jsonData["data"] == null) {
        return;
      }
      if (jsonData['ch'] == 'kline.data') {
        EventBusUtil.fireReceiveWebSocketMessage(
          ReceiveWebSocketMessageEvent(
            webSocketRes: WebSocketRes.fromJson(jsonData, KlineRes.fromJson),
          ),
        );
      }
    } catch (e) {
      debugPrint('handleMessage error----');
      debugPrint(data.toString());
      debugPrint(e.toString());
    }
  }

  void hideKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isInit) {
      return GestureDetector(
        onTap: () {
          hideKeyboard(context);
        },
        child: widget.child,
      );
    }
    return const SizedBox();
  }
}
