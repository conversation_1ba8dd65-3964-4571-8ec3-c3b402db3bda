import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

const primaryColor = Color(0xFF5F7EFC);
final primarySwatch = CommonUtils.createMaterialColor(primaryColor);

CustomThemeSwatch getCustomThemeSwatch() {
  return CustomThemeSwatch(
    pageBgColor: const Color(0xFF111117),
    primaryColor: primaryColor,
    buttonBgColor: const Color(0xFF5F7EFC),
    buttonTextColor: const Color(0xFFFFFFFF),
    textColor: const Color(0xFFFFFFFF),
    labelColor: const Color(0xFFFFFFFF).withValues(alpha: 0.8),
    subTextColor: const Color(0xFFFFFFFF).withValues(alpha: 0.7),
    lightTextColor: const Color(0xFFFFFFFF).withValues(alpha: 0.6),
    hintTextColor: const Color(0xFFFFFFFF).withValues(alpha: 0.5),
    inputBgColor: const Color(0xFF282835),
    inputHintColor: const Color(0xFFFFFFFF).withValues(alpha: 0.5),
    dividerColor: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
    modalBarColor: const Color(0xFFFFFFFF).withValues(alpha: 0.23),
    notificationBgColor: const Color(0xFF282835).withValues(alpha: 0.6),
    upColor: const Color(0xFF36E399),
    downColor: const Color(0xFFD74545),
    portfolioRedeemedBgColor: Colors.black.withValues(alpha: 0.4),
    proStartColor: const Color(0xFFCDC8EB),
    proEndColor: const Color(0xFFEBEDFF),
    proColor: const Color(0xFF7876E9),
    preferredStartColor: const Color(0xFF94E3B6),
    preferredEndColor: const Color(0xFFD6F5E1),
    preferredColor: const Color(0xFF009765),
    eliteStartColor: const Color(0xFF9FD4FD),
    eliteEndColor: const Color(0xFFD2EFFE),
    eliteColor: const Color(0xFF3B71D2),
    premierStartColor: const Color(0xFFF5D8AB),
    premierEndColor: const Color(0xFFFFF5CE),
    premierColor: const Color(0xFFB76719),
    signatureStartColor: const Color(0xFFC8D6EB),
    signatureEndColor: const Color(0xFFF6FCFF),
    signatureColor: const Color(0xFF646D95),
    platinumStartColor: const Color(0xFFFBEF7C),
    platinumMidColor: const Color(0xFFFFF7A0),
    platinumEndColor: const Color(0xFFFFAB28),
    platinumColor: const Color(0xFFB5750B),
    ff7ed032: const Color(0xFF7ED032),
    ff40e2fe: const Color(0xFF40E2FE),
    ff363C64: const Color(0xFF363C64),
    ff515165: const Color(0xFF515165),
    ff696978: const Color(0xFF696978),
    ff878C9f: const Color(0xFF878C9F),
    ff99adff: const Color(0xFF99ADFF),
    ffd8f2c0: const Color(0xFFD8F2C0),
    fffce412: const Color(0xFFFCE412),
    ff1f1f2a: const Color(0xFF1F1F2A),
    ff1d1d24: const Color(0xFF1D1D24),
    addFundsRechargeTipsBg: Colors.black,
    addFundsRechargeAddressBg1: const Color(0x002B2B38),
    addFundsRechargeAddressBg2: const Color(0xFF282835),
  );
}

CustomThemeImage getCustomThemeImage() {
  return CustomThemeImage(
    defaultSquareImg: 'assets/images/common/dark/s_defaultImg.png',
    defaultHRectangleImg: 'assets/images/common/dark/h_defaultImg.png',
    defaultVRectangleImg: 'assets/images/common/dark/v_defaultImg.png',
    tabsHome: 'assets/images/tabs/dark/home.png',
    tabsHomeIn: 'assets/images/tabs/dark/home_in.png',
    tabsPortfolio: 'assets/images/tabs/dark/portfolio.png',
    tabsPortfolioIn: 'assets/images/tabs/dark/portfolio_in.png',
    tabsWealth: 'assets/images/tabs/dark/wealth.png',
    tabsWealthIn: 'assets/images/tabs/dark/wealth_in.png',
    tabsAccount: 'assets/images/tabs/dark/account.png',
    tabsAccountIn: 'assets/images/tabs/dark/account_in.png',
    tabsServices: 'assets/images/tabs/dark/services.png',
    tabsServicesIn: 'assets/images/tabs/dark/services_in.png',
    commonEmpty: 'assets/images/common/dark/empty.png',
    commonEmpty1: 'assets/images/common/dark/empty1.png',
    commonEmptyCoupon: 'assets/images/common/dark/empty_coupon.png',
    commonDown: 'assets/images/common/dark/down.png',
    commonIconRight: 'assets/images/common/dark/icon_right.png',
    commonSuccessTip: 'assets/images/common/dark/success_tip.png',
    commonErrorTip: 'assets/images/common/dark/error_tip.png',
    commonWarnTip: 'assets/images/common/dark/warn_tip.png',
    commonIconScreen: 'assets/images/common/dark/icon_screen.png',
    authLogo: 'assets/images/auth/dark/logo.png',
    authBgTop: 'assets/images/auth/dark/bg_top.png',
    authBgBottom: 'assets/images/auth/dark/bg_bottom.png',
    authSignInLogo: 'assets/images/auth/dark/sign_in_logo.png',
    authUnchoose: 'assets/images/auth/dark/unchoose.png',
    authChoose: 'assets/images/auth/dark/choose.png',
    authSignUpLogo: 'assets/images/auth/dark/sign_up_logo.png',
    authVerified: 'assets/images/auth/dark/verified.png',
    authSucTip: 'assets/images/auth/dark/suc_tip.png',
    authLoading: 'assets/images/auth/dark/loading.png',
    authVerification: 'assets/images/auth/dark/verification.png',
    authSelect: 'assets/images/auth/dark/select.png',
    authNight: 'assets/images/auth/dark/night.png',
    authSunny: 'assets/images/auth/dark/sunny.png',
    kycIcon: 'assets/images/kyc/dark/icon.png',
    kycClose: 'assets/images/kyc/dark/close.png',
    kycPlaceholder: 'assets/images/kyc/dark/placeholder.png',
    kycVerification: 'assets/images/kyc/dark/verification.png',
    kycWarn: 'assets/images/kyc/dark/warn.png',
    kycSelect: 'assets/images/kyc/dark/select.png',
    kycUnselect: 'assets/images/kyc/dark/unselect.png',
    homeMessage: 'assets/images/home/<USER>/message.png',
    homeLogo: 'assets/images/home/<USER>/logo.png',
    homeClose: 'assets/images/home/<USER>/close.png',
    homeShare: 'assets/images/home/<USER>/share.png',
    homeAdd: 'assets/images/home/<USER>/add.png',
    homeReduce: 'assets/images/home/<USER>/reduce.png',
    homeChat: 'assets/images/home/<USER>/chat.png',
    homeBalanceBg: 'assets/images/home/<USER>/balance_bg.png',
    homeUp: 'assets/images/home/<USER>/up.png',
    homeDown: 'assets/images/home/<USER>/down.png',
    homeFail: 'assets/images/home/<USER>/fail.png',
    homeWarn: 'assets/images/home/<USER>/warn.png',
    homeCopy: 'assets/images/home/<USER>/copy.png',
    homeLink: 'assets/images/home/<USER>/link.png',
    homeCalendar: 'assets/images/home/<USER>/calendar.png',
    homeEnvelope: 'assets/images/home/<USER>/envelope.png',
    portfolioCardBg: 'assets/images/portfolio/dark/card_bg.png',
    portfolioArrowRight: 'assets/images/portfolio/dark/arrow_right.png',
    portfolioTip: 'assets/images/portfolio/dark/tip.png',
    servicesEdit: 'assets/images/services/dark/edit.png',
    servicesScan: 'assets/images/services/dark/scan.png',
    servicesCoupon: 'assets/images/services/dark/coupon.png',
    servicesPersonal: 'assets/images/services/dark/personal.png',
    servicesAuthenticator: 'assets/images/services/dark/authenticator.png',
    servicesService: 'assets/images/services/dark/service.png',
    servicesAP: 'assets/images/services/dark/ap.png',
    servicesCP: 'assets/images/services/dark/cp.png',
    servicesSL: 'assets/images/services/dark/sl.png',
    servicesRD: 'assets/images/services/dark/rd.png',
    servicesED: 'assets/images/services/dark/ed.png',
    servicesGD: 'assets/images/services/dark/gd.png',
    servicesPro: 'assets/images/services/dark/pro.png',
    servicesPreferred: 'assets/images/services/dark/preferred.png',
    servicesElite: 'assets/images/services/dark/elite.png',
    servicesPremier: 'assets/images/services/dark/premier.png',
    servicesSignature: 'assets/images/services/dark/signature.png',
    servicesPlatinum: 'assets/images/services/dark/platinum.png',
    servicesCouponBg: 'assets/images/services/dark/coupon_bg.png',
    servicesPhone: 'assets/images/services/dark/phone.png',
    servicesLock: 'assets/images/services/dark/lock.png',
    wealthMoney: 'assets/images/wealth/dark/money.png',
    googleSecure: 'assets/images/google/dark/secure.png',
    googleSecurity: 'assets/images/google/dark/security.png',
    googleSuccess: 'assets/images/google/dark/success.png',
    googleSuccess1: 'assets/images/google/dark/success1.png',
    googleUnbind: 'assets/images/google/dark/unbind.png',
    accountListBg1: 'assets/images/account/dark/funding_bg.png',
    accountListBg2: 'assets/images/account/dark/commission_bg.png',
    accountDetailTips: 'assets/images/account/dark/icon_tips.png',

    accountRedeemSb: 'assets/images/account/dark/redeem_sb.png',
    accountRedeemWc: 'assets/images/account/dark/redeem_wc.png',
    accountRedeemDd: 'assets/images/account/dark/redeem_dd.png',
    accountTransferUsdtcg: 'assets/images/account/dark/transfer_usdtcg.png',
    accountTransferUsdtddz: 'assets/images/account/dark/transfer_usdtddz.png',
    accountTransferUsdtsb: 'assets/images/account/dark/transfer_usdtsb.png',
    accountTransferEmail: 'assets/images/account/dark/transfer_email.png',
    accountRechargeUid: 'assets/images/account/dark/recharge_uid.png',
    accountRechargeEmail: 'assets/images/account/dark/recharge_email.png',
    accountRechargeUsdt: 'assets/images/account/dark/recharge_usdt.png',
    accountRechargeYhk: 'assets/images/account/dark/recharge_yhk.png',
    accountCommissionZq: 'assets/images/account/dark/commission_zq.png',
    accountCommissionYqjlsb: 'assets/images/account/dark/commission_yqjlsb.png',
    accountCommissionYqjl: 'assets/images/account/dark/commission_yqjl.png',
    accountCommissionZz: 'assets/images/account/dark/commission_zz.png',
    accountCurrencyUsdt: 'assets/images/account/dark/usdt.png',
    accountCurrencyUsdc: 'assets/images/account/dark/usdc.png',
    addFundsChainRechargeTips: 'assets/images/account/dark/icon_tips_blue.png',
    addFundsChainRechargeCopy: 'assets/images/account/dark/icon_copy.png',

    sendChainIconCopy: 'assets/images/account/dark/icon_copy_white.png',
    sendChainIconScan: 'assets/images/account/dark/icon_scan.png',
    sendChainIconAddress: 'assets/images/account/dark/icon_address.png',
    sendChainIconEdit: 'assets/images/account/dark/icon_edit.png',
    sendChainIconAddressNoRecord:
        'assets/images/account/dark/icon_address_no_record.png',
    sendChainIconAddressCheck:
        'assets/images/account/dark/icon_address_check.png',
  );
}

ThemeData getThemeData() {
  return ThemeData(
    primaryColor: primaryColor,
    primarySwatch: primarySwatch,
    platform: TargetPlatform.iOS,
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
      },
    ),
    tabBarTheme: TabBarThemeData(
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white.withValues(alpha: 0.6),
      indicatorColor: primaryColor,
    ),
    unselectedWidgetColor: Colors.white.withValues(alpha: 0.6),
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.transparent,
      foregroundColor: Color(0xFFFFFFFF),
      elevation: 0,
      titleTextStyle: TextStyle(
        fontSize: kIsWeb ? 18 : 18.sp,
        color: Colors.white,
        fontWeight: FontWeight.w500,
      ),
    ),
    textTheme: const TextTheme(
      bodySmall: TextStyle(color: Color(0xFFFFFFFF)),
      bodyMedium: TextStyle(color: Color(0xFFFFFFFF)),
      bodyLarge: TextStyle(color: Color(0xFFFFFFFF)),
      titleSmall: TextStyle(color: Color(0xFFFFFFFF)),
      titleMedium: TextStyle(color: Color(0xFFFFFFFF)),
      titleLarge: TextStyle(color: Color(0xFFFFFFFF)),
    ),
    iconTheme: const IconThemeData(color: Color(0xFFFFFFFF)),
    scaffoldBackgroundColor: const Color(0xFF111117),
    hintColor: const Color(0xFF999999),
    dividerTheme: DividerThemeData(
      color: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
    ),
    dividerColor: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
  );
}

class ThemeDark {
  static final Map<String, dynamic> theme = {
    "app": getThemeData(),
    "custom": getCustomThemeSwatch(),
    "image": getCustomThemeImage(),
  };
}
