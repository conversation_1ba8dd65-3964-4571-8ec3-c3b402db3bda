import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class UnbindGoogleSuccessfullyPage extends StatefulWidget {
  const UnbindGoogleSuccessfullyPage({super.key});

  @override
  _UnbindGoogleSuccessfullyState createState() =>
      _UnbindGoogleSuccessfullyState();
}

class _UnbindGoogleSuccessfullyState
    extends State<UnbindGoogleSuccessfullyPage> {
  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('details'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 73.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 36.h,
          children: [
            Center(
              child: Image.asset(
                customThemeImage.googleSuccess1,
                width: 80.w,
                height: 80.w,
                fit: BoxFit.fill,
              ),
            ),
            Center(
              child: Text(
                "unbind_google_successfully".tr,
                style: TextStyle(
                  fontSize: 20.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Center(
              child: Text(
                "unbind_google_successfully_description".tr,
                style: TextStyle(fontSize: 14.sp, color: customTheme.textColor),
                textAlign: TextAlign.justify,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
