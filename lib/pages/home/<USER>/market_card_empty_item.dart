import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class MarketCardEmptyItem extends StatelessWidget {
  const MarketCardEmptyItem({
    super.key,
    required this.type,
    required this.onTap,
  });
  final String type; // 0: 水平，1: 垂直
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    if (type == '0') {
      return InkWell(
        onTap: onTap,
        child: DottedBorder(
          options: RoundedRectDottedBorderOptions(
            color: customTheme.primaryColor,
            strokeWidth: 1,
            strokeCap: StrokeCap.round,
            dashPattern: [6, 3],
            radius: Radius.circular(3.w),
          ),
          child: Container(
            height: 45.w,
            alignment: Alignment.center,
            child: Wrap(
              spacing: 2.w,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Icon(
                  Icons.add_rounded,
                  size: 24.w,
                  color: customTheme.primaryColor,
                ),
                Text(
                  'Add Widget',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: customTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return InkWell(
      onTap: onTap,
      child: DottedBorder(
        options: RoundedRectDottedBorderOptions(
          color: customTheme.primaryColor,
          strokeWidth: 1,
          strokeCap: StrokeCap.round,
          dashPattern: [6, 3],
          radius: Radius.circular(3.w),
        ),
        child: Center(
          child: Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            direction: Axis.vertical,
            spacing: 6.w,
            children: [
              Icon(
                Icons.add_rounded,
                size: 24.w,
                color: customTheme.primaryColor,
              ),
              Text(
                'Add Widget',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: customTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
