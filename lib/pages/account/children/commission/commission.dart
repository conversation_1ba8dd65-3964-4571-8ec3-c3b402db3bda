import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/models/jour_list_item.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class CommissionAccountPage extends StatefulWidget {
  const CommissionAccountPage({super.key});

  @override
  _CommissionAccountPageState createState() => _CommissionAccountPageState();
}

class _CommissionAccountPageState extends State<CommissionAccountPage> {
  late EasyRefreshController _controller;

  String isVerified = '0';

  AccountRes? accountData;

  List<JourListItem>? jourList;

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(controlFinishRefresh: true);
    _onRefresh();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    await Future.wait([_getMyAccount(), _getJourList()]);
    _controller.finishRefresh(IndicatorResult.success);
  }

  Future<void> _getMyAccount() async {
    try {
      final res = await AccountApi.getMyAccount();
      setState(() {
        accountData = res;
      });
    } catch (e) {}
  }

  Future<void> _getJourList() async {
    try {
      final res = await AccountApi.getJourList({
        "pageNum": 1,
        "pageSize": 10,
        "category": '1',
      });
      setState(() {
        jourList = res.list;
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('Commission_Account'.tr)),
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 19.w, left: 15.w, right: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF282835),
                  borderRadius: BorderRadius.circular(7.w),
                ),
                padding: EdgeInsets.symmetric(vertical: 6.w, horizontal: 13.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'UID: *********-0',
                      style: TextStyle(fontSize: 12.w, color: Colors.white70),
                    ),
                    SizedBox(width: 8.w),
                    isVerified == '1'
                        ? Text(
                            'Verified'.tr,
                            style: TextStyle(
                              fontSize: 12.w,
                              color: const Color(0xFF7ED032),
                            ),
                          )
                        : Text(
                            'Unverified'.tr,
                            style: TextStyle(
                              fontSize: 12.w,
                              color: const Color(0xFFD74545),
                            ),
                          ),
                  ],
                ),
              ),
              SizedBox(height: 16.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total_Balance'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                  Text(
                    'Transferable_Balance'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '\$${accountData?.rewardAmount ?? ''}',
                    style: TextStyle(
                      fontSize: 25.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  Text(
                    '\$${accountData?.rewardAvailableAmount ?? ''}',
                    style: TextStyle(
                      fontSize: 25.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: PrimaryButton(
                      title: 'Transfer_Commission'.tr,
                      height: 43.w,
                      onPress: () {
                        Get.toNamed(Routes.commissionTransfer);
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24.w),
              Text(
                'Transaction_History'.tr,
                style: TextStyle(
                  fontSize: 16.w,
                  fontWeight: FontWeight.w600,
                  color: customTheme.textColor,
                ),
              ),
              SizedBox(height: 14.w),
              Divider(height: 1.w),
              SizedBox(height: 11.w),
              if ((jourList ?? []).isNotEmpty)
                ...jourList!.map(
                  (item) => _buildItem(
                    info: item,
                    customTheme: customTheme,
                    customThemeImage: customThemeImage,
                  ),
                ),
              SizedBox(height: 10.w),
              if ((jourList ?? []).length > 10)
                GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.transactionHistoryCommission);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'See_More'.tr,
                        style: TextStyle(
                          fontSize: 16.w,
                          fontWeight: FontWeight.w600,
                          color: customTheme.textColor,
                        ),
                      ),
                      Image.asset(
                        customThemeImage.commonIconRight,
                        width: 14.w,
                        height: 14.w,
                        fit: BoxFit.fill,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItem({
    required JourListItem info,
    required CustomThemeSwatch customTheme,
    required CustomThemeImage customThemeImage,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.transactionDetail, parameters: {'id': info.id});
      },
      child: Container(
        padding: EdgeInsets.only(top: 11.w, bottom: 11.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              info.description == '1'
                  ? customThemeImage.homeAdd
                  : customThemeImage.homeReduce,
              width: 22.w,
              height: 22.w,
              fit: BoxFit.fill,
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    DateTime.fromMillisecondsSinceEpoch(
                      info.createDatetime as int,
                    ).yyyyMMddHHmmEEE,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                  Text(
                    info.remark,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  if (info.code != null)
                    Text(
                      info.code ?? '',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(width: 10.w),
            Column(
              children: [
                Text(
                  '\$ ${info.transAmount}',
                  style: TextStyle(
                    fontSize: 15.w,
                    fontWeight: FontWeight.w600,
                    color: customTheme.textColor,
                  ),
                ),
                info.status == '0'
                    ? Text(
                        'Pending',
                        style: TextStyle(
                          fontSize: 12.w,
                          fontWeight: FontWeight.w400,
                          color: customTheme.primaryColor,
                        ),
                      )
                    : SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
