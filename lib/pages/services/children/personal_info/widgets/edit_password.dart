import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class EditPassword extends StatefulWidget {
  const EditPassword({super.key});

  static Future<void> showModal(BuildContext context) async {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return EditPassword();
      },
    );
  }

  @override
  State<EditPassword> createState() => _EditPasswordState();
}

class _EditPasswordState extends State<EditPassword> {
  final TextEditingController _oldPasswordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  bool isPasswordVisible = false;
  bool isConfirmPasswordVisible = false;
  // 邮箱验证码
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _codeFocusNode = FocusNode();
  List<String> _codeDigits = List.filled(6, '');
  int step = 0; // 0: 输入新密码 1: 收入邮箱验证码 2: 修改完成

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _codeController.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  void onStep0Submit() async {
    final oldLoginPwd = _oldPasswordController.text;
    final newLoginPwd = _newPasswordController.text;
    if (oldLoginPwd.isEmpty) {
      ToastUtil.showToast('please_enter_your_current_password'.tr);
      return;
    }
    if (newLoginPwd.isEmpty) {
      ToastUtil.showToast('please_enter_your_new_password'.tr);
      return;
    }
    if (newLoginPwd.length < 8) {
      ToastUtil.showToast('password_must_be_at_least_8_characters'.tr);
      return;
    }
    if (newLoginPwd != _confirmPasswordController.text) {
      ToastUtil.showToast(
        'confirm_password_must_be_the_same_as_new_password'.tr,
      );
      return;
    }
    sendCode(() {
      setState(() {
        step = 1;
      });
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _codeFocusNode.requestFocus();
      });
    });
  }

  void sendCode(VoidCallback? onSuccess) async {
    ToastUtil.showLoading();
    try {
      await CommonApi.sendEmailCode(
        email: context.read<AuthProvider>().user?.email ?? '',
        bizType: SmsBizType.modifyPassword,
      );
      onSuccess?.call();
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
    onSuccess?.call();
  }

  void _onCodeChanged() {
    final text = _codeController.text;

    setState(() {
      // 更新数字列表
      for (int i = 0; i < 6; i++) {
        if (i < text.length) {
          _codeDigits[i] = text[i];
        } else {
          _codeDigits[i] = '';
        }
      }
    });
    if (text.length == 6) {
      onStep1Submit();
    }
  }

  void onStep1Submit() async {
    ToastUtil.showLoading();
    try {
      final oldLoginPwd = _oldPasswordController.text;
      final newLoginPwd = _newPasswordController.text;
      final smsCaptcha = _codeController.text;
      await UserApi.editLoginPwd(
        oldLoginPwd: oldLoginPwd,
        newLoginPwd: newLoginPwd,
        smsCaptcha: smsCaptcha,
      );
      await LocalUtil.setLoginNameAndPwd(
        context.read<AuthProvider>().user!.email,
        newLoginPwd,
      );
      setState(() {
        step = 2;
      });
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'security_setting_access'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  Widget _buildStep0Content() {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTopBar(context),
        SizedBox(height: 22.5.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NormalInput(
                label: 'current_password'.tr,
                controller: _oldPasswordController,
                hintText: 'enter_your_current_password'.tr,
                obscureText: true,
              ),
              SizedBox(height: 16.h),
              NormalInput(
                label: 'new_password'.tr,
                controller: _newPasswordController,
                hintText: 'enter_your_new_password'.tr,
                obscureText: !isPasswordVisible,
                padding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(horizontal: 19.w),
                suffix: GestureDetector(
                  onTap: () {
                    setState(() {
                      isPasswordVisible = !isPasswordVisible;
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: 12.w),
                    child: Icon(
                      isPasswordVisible
                          ? Icons.visibility
                          : Icons.visibility_off,
                      size: 20.w,
                      color: customTheme.textColor,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              NormalInput(
                label: 'confirm_password'.tr,
                controller: _confirmPasswordController,
                hintText: 'enter_your_confirm_password'.tr,
                obscureText: !isConfirmPasswordVisible,
                padding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(horizontal: 19.w),
                suffix: GestureDetector(
                  onTap: () {
                    setState(() {
                      isConfirmPasswordVisible = !isConfirmPasswordVisible;
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: 12.w),
                    child: Icon(
                      isConfirmPasswordVisible
                          ? Icons.visibility
                          : Icons.visibility_off,
                      size: 20.w,
                      color: customTheme.textColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 40.h),
        WidgetUtil.getBottomButton(
          context: context,
          title: 'next'.tr,
          onSubmit: onStep0Submit,
        ),
      ],
    );
  }

  Widget _buildStep1Content() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTopBar(context),
        SizedBox(height: 37.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Image.asset(
                  customThemeImage.googleSecurity,
                  width: 50.w,
                  height: 50.w,
                  fit: BoxFit.fill,
                ),
              ),
              SizedBox(height: 8.5.h),
              Text(
                'change_password_description'.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: customTheme.subTextColor,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 22.5.h),
              SizedBox(
                height: 61.w,
                child: Stack(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(
                        6,
                        (index) => GestureDetector(
                          onTap: () {
                            _codeFocusNode.requestFocus();
                          },
                          child: Container(
                            width: 49.w,
                            height: 61.w,
                            decoration: BoxDecoration(
                              color: customTheme.inputBgColor,
                              borderRadius: BorderRadius.circular(9.w),
                            ),
                            child: Center(
                              child: Text(
                                _codeDigits[index],
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w600,
                                  color: customTheme.textColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0,
                        child: NormalInput(
                          decoration: BoxDecoration(),
                          keyboardType: TextInputType.number,
                          maxLength: 6,
                          contentBuilder: (context) => TextField(
                            controller: _codeController,
                            focusNode: _codeFocusNode,
                            onChanged: (value) {
                              _onCodeChanged();
                            },
                            keyboardType: TextInputType.number,
                            maxLength: 6,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(6),
                            ],
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: Colors.transparent,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 15.h),
              Center(
                child: GestureDetector(
                  onTap: () {
                    sendCode(() {
                      _codeFocusNode.requestFocus();
                      _codeController.clear();
                      setState(() {
                        _codeDigits = List.filled(6, '');
                      });
                    });
                  },
                  child: RichText(
                    text: TextSpan(
                      text: "didnt_receive_resend_a_code".tr,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.primaryColor,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 40.h),
        WidgetUtil.getBottomButton(
          context: context,
          title: 'confirm'.tr,
          onSubmit: onStep1Submit,
        ),
      ],
    );
  }

  Widget _buildCompletedContent() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 275.h,
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    SizedBox(height: 39.h),
                    Image.asset(
                      customThemeImage.servicesLock,
                      width: 80.w,
                      height: 81.5.w,
                      fit: BoxFit.fill,
                    ),
                    SizedBox(height: 36.5.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Text(
                        'update_complete'.tr,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: customTheme.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Text(
                        'your_new_password_is_now_saved'.tr,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: customTheme.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: 70.h),
                  ],
                ),
              ),
              Positioned(
                top: 23.w,
                right: 10.w,
                child: TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: false,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: step == 0
            ? _buildStep0Content()
            : step == 1
            ? _buildStep1Content()
            : _buildCompletedContent(),
      ),
    );
  }
}
