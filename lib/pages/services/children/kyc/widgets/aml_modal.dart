import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AmlModal extends StatefulWidget {
  const AmlModal({super.key});

  static Future<void> showModal(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(top: false, child: AmlModal());
      },
    );
  }

  @override
  State<AmlModal> createState() => _AmlModalState();
}

class _AmlModalState extends State<AmlModal> {
  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'learn_more_about_aml'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final titleStyle = TextStyle(fontSize: 16.sp, color: customTheme.textColor);
    final contentStyle = TextStyle(
      fontSize: 14.sp,
      color: customTheme.lightTextColor,
    );

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('what_is_aml'.tr, style: titleStyle),
                  SizedBox(height: 10.5.w),
                  Text('what_is_aml_description'.tr, style: contentStyle),
                  SizedBox(height: 17.w),
                  Text('why_do_we_ask_these_questions'.tr, style: titleStyle),
                  SizedBox(height: 10.5.w),
                  Text(
                    "why_do_we_ask_these_questions_description".tr,
                    style: contentStyle,
                  ),
                  SizedBox(height: 17.w),
                  Text('what_we_check'.tr, style: titleStyle),
                  SizedBox(height: 10.5.w),
                  Text('check_1'.tr, style: contentStyle),
                  Text('check_2'.tr, style: contentStyle),
                  Text("check_3".tr, style: contentStyle),
                  Text('check_4'.tr, style: contentStyle),
                  SizedBox(height: 17.w),
                  Text('your_privacy_is_protected'.tr, style: titleStyle),
                  SizedBox(height: 10.5.w),
                  Text(
                    'your_privacy_is_protected_description'.tr,
                    style: contentStyle,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
