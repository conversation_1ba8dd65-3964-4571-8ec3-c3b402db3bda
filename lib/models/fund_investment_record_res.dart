import 'package:json_annotation/json_annotation.dart';

part 'fund_investment_record_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class FundInvestmentRecordRes {
  FundInvestmentRecordRes();

  late String id;
  late String productId;
  late num initialAmount;
  late num totalInvested;
  late num totalRedeemed;
  late num returnsToWallet;
  late num returnsReinvested;
  late num totalReturns;
  late num currentBalance;
  late String status;
  late num fundingDate;
  late num interestStartDate;
  late num firstInterestPaidDate;
  late num nextInterestDate;
  String? isReinvestEnabled;
  num? reinvestEnabledDate;
  late num pendingRedeemAmount;
  late String statusName;
  late String canRedeem;
  late String canReinvest;
  late num redemptionPeriodDays;
  late num lockinPeriodDays;
  late num fee;
  late num stopLossEvent;

  factory FundInvestmentRecordRes.fromJson(Map<String, dynamic> json) =>
      _$FundInvestmentRecordResFromJson(json);
  Map<String, dynamic> toJson() => _$FundInvestmentRecordResToJson(this);
}
