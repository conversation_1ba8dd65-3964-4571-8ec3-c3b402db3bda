import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class EditNickname extends StatefulWidget {
  final String? nickname;

  const EditNickname({super.key, this.nickname});

  static Future<void> showModal(
    BuildContext context, {
    String? nickname,
  }) async {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return EditNickname(nickname: nickname);
      },
    );
  }

  @override
  State<EditNickname> createState() => _EditNicknameState();
}

class _EditNicknameState extends State<EditNickname> {
  final TextEditingController _nicknameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nicknameController.text = widget.nickname ?? '';
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    if (_nicknameController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_nickname'.tr);
      return;
    }

    Navigator.of(context).pop();
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'edit_nickname'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return SafeArea(
      top: false,
      bottom: false,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTopBar(context),
            SizedBox(height: 22.5.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: NormalInput(
                controller: _nicknameController,
                hintText: 'enter_your_nickname'.tr,
              ),
            ),
            SizedBox(height: 9.5.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Text(
                'nickname_can_only_be_modified_1_time_per_7_days'.tr,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: customTheme.subTextColor,
                ),
              ),
            ),
            SizedBox(height: 40.h),
            WidgetUtil.getBottomButton(
              context: context,
              title: 'save'.tr,
              onSubmit: onSubmit,
            ),
          ],
        ),
      ),
    );
  }
}
