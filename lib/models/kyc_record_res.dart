import 'package:json_annotation/json_annotation.dart';

part 'kyc_record_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class KycRecordRes {
  KycRecordRes();

  late String id;
  late String type;
  late String fullName;
  late String interId;
  late String mobile;
  late String idNO;
  String? frontPhoto;
  String? backPhoto;
  String? passportPhoto;

  factory KycRecordRes.fromJson(Map<String, dynamic> json) =>
      _$KycRecordResFromJson(json);
  Map<String, dynamic> toJson() => _$KycRecordResToJson(this);
}
