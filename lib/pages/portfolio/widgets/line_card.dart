// ignore_for_file: unused_element

import 'dart:async';

import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart' show NumberFormat;
import 'package:provider/provider.dart';

class LineCard extends StatefulWidget {
  const LineCard({super.key});

  @override
  State<LineCard> createState() => _LineCardState();
}

class _LineCardState extends State<LineCard> {
  late TransformationController _transformationController;
  late StreamController<FLHorizontalAlignment> alignmentStream;
  List<(DateTime, double)>? _bitcoinPriceHistory;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    alignmentStream = StreamController<FLHorizontalAlignment>();
    _reloadData();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    alignmentStream.close();
    super.dispose();
  }

  void _reloadData() async {
    if (!mounted) {
      return;
    }
    setState(() {
      _bitcoinPriceHistory =
          [
            [1702944000000, 39078.47891755909],
            [1703030400000, 38477.47402170458],
            [1703116800000, 39864.87762044923],
            [1703203200000, 39838.679877640185],
            [1703289600000, 39908.58005878162],
            [1703376000000, 39678.979321595696],
            [1703462400000, 39062.32681431535],
            [1703548800000, 39597.85927945271],
            [1703635200000, 38502.06842500796],
            [1703721600000, 39086.65066359461],
            [1703808000000, 38491.64747698571],
            [1703894400000, 38057.70863986569],
            [1703980800000, 38189.68296890573],
            [1704067200000, 38240.20908960317],
            [1704153600000, 40022.56708386281],
            [1704240000000, 41121.44236936086],
            [1704326400000, 39193.97344095043],
            [1704412800000, 40376.019880448854],
            [1704499200000, 40269.190802082194],
            [1704585600000, 40125.3447966974],
            [1704672000000, 40118.34311925359],
            [1704758400000, 42857.43103594771],
            [1704844800000, 42178.411061608065],
            [1704931200000, 42492.38965139214],
            [1705017600000, 42171.814324539526],
            [1705104000000, 39122.52373887039],
            [1705190400000, 39081.06524302686],
            [1705276800000, 38190.210045708205],
            [1705363200000, 38908.89747537756],
            [1705449600000, 39668.76253090919],
            [1705536000000, 39242.29028152058],
            [1705622400000, 37938.77972050555],
            [1705708800000, 38150.14288219012],
            [1705795200000, 38173.221525259156],
            [1705881600000, 38141.44573510522],
            [1705968000000, 36313.41944934261],
            [1706054400000, 36689.35973905385],
            [1706140800000, 36871.20615880076],
            [1706227200000, 36811.15855147119],
            [1706313600000, 38535.125270419456],
            [1706400000000, 38771.39629759249],
            [1706486400000, 38759.18018541519],
            [1706572800000, 39930.07569963493],
            [1706659200000, 39555.50605607913],
            [1706745600000, 39390.4556124346],
            [1706832000000, 39601.98542555142],
            [1706918400000, 39978.18836096943],
            [1707004800000, 39798.71036813004],
            [1707091200000, 39517.8500683694],
            [1707177600000, 39701.74708708336],
            [1707264000000, 40053.71777001113],
            [1707350400000, 41066.29173159883],
            [1707436800000, 42076.0465628706],
            [1707523200000, 43713.572063470674],
            [1707609600000, 44293.80084671968],
            [1707696000000, 44634.239554822176],
            [1707782400000, 46463.69349243575],
            [1707868800000, 46442.363953357555],
            [1707955200000, 48260.30672871491],
            [1708041600000, 48219.73170515296],
            [1708128000000, 48405.75606037122],
            [1708214400000, 47958.58062660045],
            [1708300800000, 48348.85208766836],
            [1708387200000, 48043.59788234124],
            [1708473600000, 48367.898535816996],
            [1708560000000, 47916.29005429376],
            [1708646400000, 47410.49380112791],
            [1708732800000, 46933.94401263771],
            [1708819200000, 47591.24123045009],
            [1708905600000, 47833.38664994704],
            [1708992000000, 50208.13598783667],
            [1709078400000, 52573.782674551876],
            [1709164800000, 57717.736388579004],
            [1709251200000, 56734.564615792624],
            [1709337600000, 57560.48389994141],
            [1709424000000, 57229.6765848964],
            [1709510400000, 58157.09485482637],
            [1709596800000, 62819.675032831015],
            [1709683200000, 59216.16978710529],
            [1709769600000, 60688.021041080945],
            [1709856000000, 61130.91471064688],
            [1709942400000, 62425.38416118097],
            [1710028800000, 62601.51789297492],
            [1710115200000, 63133.50171410394],
            [1710201600000, 65996.21012712058],
            [1710288000000, 65396.96618741416],
            [1710374400000, 66750.17623162274],
            [1710460800000, 65624.65332089699],
            [1710547200000, 63816.32105192292],
            [1710633600000, 59954.666681584524],
            [1710720000000, 62871.803645507665],
            [1710806400000, 62274.42066839416],
            [1710892800000, 57178.75390064521],
            [1710979200000, 62011.64214760789],
            [1711065600000, 60327.113345413214],
            [1711152000000, 58488.654960729786],
            [1711238400000, 59204.01008039168],
            [1711324800000, 62277.19869537941],
            [1711411200000, 64530.77647863455],
            [1711497600000, 64695.8250958296],
            [1711584000000, 64211.47397847694],
            [1711670400000, 65526.713768406174],
            [1711756800000, 64739.24207573283],
            [1711843200000, 64538.55260842681],
            [1711929600000, 66012.15293045473],
            [1712016000000, 64987.58440425927],
            [1712102400000, 60773.862930874704],
            [1712188800000, 61012.09522243375],
            [1712275200000, 63241.87913574653],
            [1712361600000, 62689.87435787321],
            [1712448000000, 63632.39935297565],
            [1712534400000, 64086.823460256426],
            [1712620800000, 65931.06490439967],
            [1712707200000, 63687.50350010323],
            [1712793600000, 65668.16571700791],
            [1712880000000, 65354.13336928861],
            [1712966400000, 63099.287555906376],
            [1713052800000, 60427.82751512378],
            [1713139200000, 61736.684829580605],
            [1713225600000, 59686.89912995451],
            [1713312000000, 59995.209903171795],
            [1713398400000, 57478.36312839251],
            [1713484800000, 59623.56249209243],
            [1713571200000, 60012.93722485975],
            [1713657600000, 60862.26888013952],
            [1713744000000, 60924.51880957888],
            [1713830400000, 62738.59113235908],
            [1713916800000, 62051.58606093932],
            [1714003200000, 60081.61563815237],
            [1714089600000, 60110.989276790104],
            [1714176000000, 59659.006735859955],
            [1714262400000, 59381.3230914825],
            [1714348800000, 58876.98016307695],
            [1714435200000, 59532.80475223963],
            [1714521600000, 56955.30306317563],
            [1714608000000, 54392.86086133425],
            [1714694400000, 55109.42816389191],
            [1714780800000, 58355.75377420205],
            [1714867200000, 59266.86265965134],
            [1714953600000, 59488.78971098448],
            [1715040000000, 58653.24832859499],
            [1715126400000, 57997.6431882983],
            [1715212800000, 56957.965704980525],
            [1715299200000, 58566.91642282563],
            [1715385600000, 56489.04308941497],
            [1715472000000, 56385.835846451875],
            [1715558400000, 57100.99619053609],
            [1715644800000, 58257.94727352527],
            [1715731200000, 56927.17129981614],
            [1715817600000, 60811.29527582236],
            [1715904000000, 60054.301551755365],
            [1715990400000, 61631.8782491127],
            [1716076800000, 61502.73339909207],
            [1716163200000, 60924.86820709035],
            [1716249600000, 65770.16026770095],
            [1716336000000, 64653.893276922405],
            [1716422400000, 63912.083874721946],
            [1716508800000, 62795.077789946045],
            [1716595200000, 63150.07451451353],
            [1716681600000, 63850.68338638202],
            [1716768000000, 63150.48139241663],
            [1716854400000, 63871.133659487794],
            [1716940800000, 62950.705715262724],
            [1717027200000, 62561.005165216935],
            [1717113600000, 63123.741723088686],
            [1717200000000, 62145.91785386879],
            [1717286400000, 62362.92899401087],
            [1717372800000, 62419.71597472404],
            [1717459200000, 63083.78769270654],
            [1717545600000, 64881.83386232028],
            [1717632000000, 65462.71014376164],
            [1717718400000, 64964.377920006424],
            [1717804800000, 64146.75781805342],
            [1717891200000, 64137.2658453789],
            [1717977600000, 64623.80690329454],
            [1718064000000, 64559.64845115045],
            [1718150400000, 62689.904415286815],
            [1718236800000, 63085.64417953837],
            [1718323200000, 62107.46654002352],
            [1718409600000, 61562.52612996296],
            [1718496000000, 61759.23292317989],
            [1718582400000, 62231.3694306833],
            [1718668800000, 61877.28055510076],
            [1718755200000, 60621.91572601943],
            [1718841600000, 60398.36957706064],
            [1718928000000, 60572.188509925974],
            [1719014400000, 59924.844689378995],
            [1719100800000, 60079.40613872267],
            [1719187200000, 59147.18345045349],
            [1719273600000, 56243.927094112085],
            [1719360000000, 57677.50624706287],
            [1719446400000, 56873.38456798354],
            [1719532800000, 57491.13821425305],
            [1719619200000, 56265.05472146868],
            [1719705600000, 56775.63073475873],
            [1719792000000, 58430.814422068135],
            [1719878400000, 58509.4401642161],
            [1719964800000, 57718.07416888454],
            [1720051200000, 55833.80621748973],
            [1720137600000, 52904.90433375453],
            [1720224000000, 52307.68520432331],
            [1720310400000, 53715.700026296974],
            [1720396800000, 51613.90871758563],
            [1720483200000, 52316.60116848915],
            [1720569600000, 53621.436327802985],
            [1720656000000, 53272.288732207024],
            [1720742400000, 52781.06838693916],
            [1720828800000, 53014.0364517072],
            [1720915200000, 54166.67728993813],
            [1721001600000, 55984.24278202276],
            [1721088000000, 59500.95530894624],
            [1721174400000, 59763.54497883689],
            [1721260800000, 58625.721758164145],
            [1721347200000, 58665.955279138856],
            [1721433600000, 61224.60704078251],
            [1721520000000, 61705.0466917566],
            [1721606400000, 62467.87253811925],
            [1721692800000, 62073.819234324954],
            [1721779200000, 60765.80580937294],
            [1721865600000, 60341.49772175119],
            [1721952000000, 60594.574722199846],
            [1722038400000, 62469.76061302131],
            [1722124800000, 62599.81463153654],
            [1722211200000, 62873.08389964636],
            [1722297600000, 61707.72841612238],
            [1722384000000, 61216.26793898385],
            [1722470400000, 59750.92079501405],
            [1722556800000, 60586.10315866606],
            [1722643200000, 56231.21313117775],
            [1722729600000, 55639.42359487421],
            [1722816000000, 53169.53306907562],
            [1722902400000, 49271.40149467704],
            [1722988800000, 51222.280928669235],
            [1723075200000, 50411.00101226333],
            [1723161600000, 56671.842504303975],
            [1723248000000, 55761.51548402803],
            [1723334400000, 55729.910819518554],
            [1723420800000, 53884.90745860033],
            [1723507200000, 54270.12342034756],
            [1723593600000, 55114.14602647766],
            [1723680000000, 53343.88139134332],
            [1723766400000, 52521.96236743676],
            [1723852800000, 53376.171320376874],
            [1723939200000, 53873.71546267861],
            [1724025600000, 53006.74653077159],
            [1724112000000, 53742.26074312489],
            [1724198400000, 53072.96487740486],
            [1724284800000, 54811.25973940124],
            [1724371200000, 54317.08310206506],
            [1724457600000, 57173.9518647258],
            [1724544000000, 57319.03380665118],
            [1724630400000, 57448.04865756021],
            [1724716800000, 56355.54477751003],
            [1724803200000, 53250.67556605576],
            [1724889600000, 53049.55806257478],
            [1724976000000, 53564.80049060293],
            [1725062400000, 53481.26904169355],
            [1725148800000, 53307.011182245114],
            [1725235200000, 51929.8407759559],
            [1725321600000, 53412.30560377036],
            [1725408000000, 52046.1502790778],
            [1725494400000, 52329.681939274866],
            [1725580800000, 50518.54680242768],
            [1725667200000, 48622.690205035346],
            [1725753600000, 48827.90111822669],
            [1725840000000, 49416.66965979366],
            [1725926400000, 51694.7737107547],
            [1726012800000, 52293.63869759159],
            [1726099200000, 52115.49563129617],
            [1726185600000, 52451.75030832475],
            [1726272000000, 54674.77446852742],
            [1726358400000, 54142.357125255534],
            [1726444800000, 53407.547394791305],
            [1726531200000, 52308.16606889451],
            [1726617600000, 54228.992673647364],
            [1726704000000, 55288.44506329167],
            [1726790400000, 56427.20338319878],
            [1726876800000, 56488.082242429715],
            [1726963200000, 56759.67683409224],
            [1727049600000, 56960.916902308025],
            [1727136000000, 56989.44770900782],
            [1727222400000, 57493.81502099898],
            [1727308800000, 56737.43484845022],
            [1727395200000, 58275.038981340615],
            [1727481600000, 58891.82868753552],
            [1727568000000, 59019.92695395789],
            [1727654400000, 58791.91905839133],
            [1727740800000, 56785.567724519715],
            [1727827200000, 55018.60488254399],
            [1727913600000, 54905.83731260847],
            [1728000000000, 55029.88993283354],
            [1728086400000, 56557.5221526609],
            [1728172800000, 56539.73406535857],
            [1728259200000, 57252.32733417741],
            [1728345600000, 56754.33895468004],
            [1728432000000, 56657.33655044081],
            [1728518400000, 55382.099090773205],
            [1728604800000, 55055.71620633765],
            [1728691200000, 57015.18140309107],
            [1728777600000, 57760.33532582211],
            [1728864000000, 57417.523313436555],
            [1728950400000, 60551.20074941091],
            [1729036800000, 61522.6779321783],
            [1729123200000, 62295.13515961688],
            [1729209600000, 62168.14661024002],
            [1729296000000, 62971.25326535072],
            [1729382400000, 62900.664368658116],
            [1729468800000, 63461.0436312693],
            [1729555200000, 62314.63337960675],
            [1729641600000, 62379.86708703343],
            [1729728000000, 61857.31577287963],
            [1729814400000, 62996.90492256556],
            [1729900800000, 61649.93632490859],
            [1729987200000, 62050.376516974386],
            [1730073600000, 62938.34448352703],
            [1730160000000, 64580.714702132216],
            [1730246400000, 67268.45323344307],
            [1730332800000, 66618.65645361826],
            [1730419200000, 64550.8369757725],
            [1730505600000, 63941.79763295739],
            [1730592000000, 63726.32978776158],
            [1730678400000, 63270.25948561604],
            [1730764800000, 62346.71648632486],
            [1730851200000, 63437.06088138662],
            [1730937600000, 70456.7357599368],
            [1731024000000, 70367.30136816247],
            [1731110400000, 71385.26799176612],
            [1731196800000, 71493.3957234108],
            [1731283200000, 75100.39122172692],
            [1731369600000, 83136.67035524877],
            [1731456000000, 83116.39043645399],
            [1731542400000, 85646.80574476697],
            [1731628800000, 83012.8378117053],
            [1731715200000, 86243.41823875198],
            [1731801600000, 85927.71877600063],
            [1731888000000, 85275.3689714038],
            [1731974400000, 85439.42645883636],
            [1732060800000, 86966.37073765934],
            [1732147200000, 89326.4051029836],
            [1732233600000, 94069.11559788969],
            [1732320000000, 94955.55602347082],
            [1732406400000, 93760.27068972368],
            [1732492800000, 93546.11482101932],
            [1732579200000, 88960.11249694412],
            [1732665600000, 87635.2127980843],
            [1732752000000, 90869.12691365478],
            [1732838400000, 90578.80838013266],
            [1732924800000, 92112.0297646138],
            [1733011200000, 91239.47122285848],
            [1733097600000, 92304.33867392405],
            [1733184000000, 91254.9956460552],
            [1733270400000, 91374.28893844674],
            [1733356800000, 94065.64524925785],
            [1733443200000, 91830.04825245812],
            [1733529600000, 94532.37474294563],
            [1733616000000, 94396.5048454845],
            [1733702400000, 95806.82736572677],
            [1733788800000, 92230.50083900787],
            [1733875200000, 91771.99667795865],
            [1733961600000, 96256.94286515891],
            [1734048000000, 95478.4718086457],
            [1734134400000, 96474.24824906969],
            [1734220800000, 96521.66753667717],
            [1734307200000, 99631.30349379999],
            [1734393600000, 100856.74619693069],
            [1734396005000, 100857.82408798973],
          ].map((item) {
            final timestamp = item[0] as int;
            final price = item[1] as double;
            return (DateTime.fromMillisecondsSinceEpoch(timestamp), price);
          }).toList();
    });
  }

  getFormattedCurrency(
    BuildContext context,
    double value, {
    bool noDecimals = true,
  }) {
    final germanFormat = NumberFormat.currency(
      symbol: '\$',
      decimalDigits: noDecimals && value % 1 == 0 ? 0 : 2,
    );
    return germanFormat.format(value);
  }

  void _transformationReset() {
    _transformationController.value = Matrix4.identity();
  }

  void _transformationZoomIn() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    if (currentScale < 4.0) {
      _transformationController.value *= Matrix4.diagonal3Values(1.1, 1.1, 1);
    }
  }

  void _transformationMoveLeft() {
    _transformationController.value *= Matrix4.translationValues(20, 0, 0);
  }

  void _transformationMoveRight() {
    _transformationController.value *= Matrix4.translationValues(-20, 0, 0);
  }

  void _transformationZoomOut() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    if (currentScale > 1.0) {
      _transformationController.value *= Matrix4.diagonal3Values(0.9, 0.9, 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    const leftReservedSize = 40.0;
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return AspectRatio(
      aspectRatio: 1.7,
      child: GestureDetector(
        onDoubleTap: () {
          // _transformationZoomIn();
        },
        child: StreamBuilder(
          stream: alignmentStream.stream,
          builder: (context, snapshot) {
            return LineChart(
              transformationConfig: FlTransformationConfig(
                scaleAxis: FlScaleAxis.horizontal,
                minScale: 1.0,
                maxScale: 4.0,
                panEnabled: true,
                scaleEnabled: true,
                transformationController: _transformationController,
              ),
              LineChartData(
                lineBarsData: [
                  LineChartBarData(
                    spots:
                        _bitcoinPriceHistory?.asMap().entries.map((e) {
                          final index = e.key;
                          final item = e.value;
                          final value = item.$2;

                          // 检查值是否为有效数字
                          if (value.isNaN || value.isInfinite) {
                            return FlSpot(index.toDouble(), 0.0);
                          }

                          return FlSpot(index.toDouble(), value);
                        }).toList() ??
                        [],
                    dotData: const FlDotData(show: false),
                    color: customTheme.ff99adff,
                    barWidth: 1,
                    shadow: Shadow(
                      color: customTheme.ff99adff,
                      blurRadius: 0.5,
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          customTheme.ff99adff.withValues(alpha: 0.2),
                          customTheme.ff99adff.withValues(alpha: 0.0),
                        ],
                        stops: const [0.5, 1.0],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  touchSpotThreshold: 5,
                  getTouchLineStart: (_, __) => -double.infinity,
                  getTouchLineEnd: (_, __) => double.infinity,
                  getTouchedSpotIndicator:
                      (LineChartBarData barData, List<int> spotIndexes) {
                        return spotIndexes.map((spotIndex) {
                          return TouchedSpotIndicatorData(
                            FlLine(
                              color: Colors.white.withValues(alpha: 0.2),
                              strokeWidth: 0.5,
                              dashArray: [8, 2],
                            ),
                            FlDotData(
                              show: true,
                              getDotPainter: (spot, percent, barData, index) {
                                return FlDotCirclePainter(
                                  radius: 5,
                                  color: customTheme.primaryColor,
                                  strokeWidth: 0,
                                  strokeColor: customTheme.primaryColor,
                                );
                              },
                            ),
                          );
                        }).toList();
                      },
                  touchTooltipData: LineTouchTooltipData(
                    tooltipHorizontalAlignment:
                        snapshot.data ?? FLHorizontalAlignment.center,
                    getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                      return touchedBarSpots.map((barSpot) {
                        final price = barSpot.y;
                        final index = barSpot.x.toInt();
                        final date = _bitcoinPriceHistory![index].$1;

                        // 如果index小于_bitcoinPriceHistory.length的1/3，则设置为right
                        // 1/3 ～ 2/3 则设置为center，否则设置为left
                        alignmentStream.add(
                          index < (_bitcoinPriceHistory?.length ?? 0) ~/ 3
                              ? FLHorizontalAlignment.right
                              : index <
                                    (_bitcoinPriceHistory?.length ?? 0) * 2 ~/ 3
                              ? FLHorizontalAlignment.center
                              : FLHorizontalAlignment.left,
                        );

                        return LineTooltipItem(
                          '',
                          const TextStyle(),
                          children: [
                            TextSpan(
                              text: '${date.day}/${date.month}',
                              style: TextStyle(
                                color: customTheme.subTextColor,
                                fontSize: 12,
                              ),
                            ),
                            TextSpan(
                              text:
                                  '\n${getFormattedCurrency(context, price, noDecimals: true)}',
                              style: TextStyle(
                                color: customTheme.textColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        );
                      }).toList();
                    },
                    getTooltipColor: (LineBarSpot barSpot) =>
                        customTheme.inputBgColor,
                    tooltipPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    tooltipBorderRadius: BorderRadius.circular(3),
                  ),
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  leftTitles: AxisTitles(
                    drawBelowEverything: true,
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: leftReservedSize,
                      maxIncluded: false,
                      minIncluded: false,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return SideTitleWidget(
                          meta: meta,
                          child: Text(
                            meta.formattedValue,
                            style: TextStyle(
                              color: customTheme.subTextColor,
                              fontSize: 11,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 38,
                      maxIncluded: false,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final date = _bitcoinPriceHistory![value.toInt()].$1;
                        return SideTitleWidget(
                          meta: meta,
                          child: Transform.rotate(
                            angle: -45 * 3.14 / 180,
                            child: Text(
                              '${date.day}/${date.month}',
                              style: TextStyle(
                                color: customTheme.subTextColor,
                                fontSize: 11,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  drawHorizontalLine: true,
                  getDrawingHorizontalLine: (double value) =>
                      FlLine(color: customTheme.dividerColor, strokeWidth: 0.5),
                ),
              ),
              duration: Duration.zero,
            );
          },
        ),
      ),
    );
  }
}
