import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class RedeemSuccessfullyPage extends StatefulWidget {
  const RedeemSuccessfullyPage({super.key});

  @override
  _RedeemSuccessfullyPageState createState() => _RedeemSuccessfullyPageState();
}

class _RedeemSuccessfullyPageState extends State<RedeemSuccessfullyPage> {
  String redemptionPeriodDays = '';
  String redeemAmount = '';

  @override
  void initState() {
    super.initState();
    redemptionPeriodDays = Get.parameters['redemptionPeriodDays'] ?? '';
    redeemAmount = Get.parameters['redeemAmount'] ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('redemption'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          children: [
            SizedBox(height: 73.h),
            Image.asset(
              customThemeImage.googleSuccess1,
              width: 80.w,
              height: 80.w,
              fit: BoxFit.fill,
            ),
            SizedBox(height: 37.h),
            Text(
              'redemption_request_submitted'.tr,
              style: TextStyle(
                fontSize: 20.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 26.5.h),
            Text.rich(
              textAlign: TextAlign.justify,
              TextSpan(
                style: TextStyle(fontSize: 14.sp, color: customTheme.textColor),
                children: [
                  TextSpan(text: "redemption_successfully_tip".tr),
                  TextSpan(
                    text: " \$ ${redeemAmount.fmt} ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: customTheme.primaryColor,
                    ),
                  ),
                  TextSpan(
                    text:
                        "${'will_be_processed_within'.tr} $redemptionPeriodDays ",
                  ),
                  TextSpan(
                    text: "calendar_days".tr,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: customTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 50.h),
            Align(
              alignment: Alignment.centerLeft,
              child: Wrap(
                spacing: 3.5.w,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Image.asset(
                    customThemeImage.kycWarn,
                    width: 15.w,
                    height: 15.w,
                    fit: BoxFit.fill,
                  ),
                  Text(
                    'kyc_note'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 7.h),
            Text(
              "no_interest_during_redemption_period".tr,
              style: TextStyle(
                fontSize: 13.sp,
                color: customTheme.subTextColor,
              ),
              textAlign: TextAlign.justify,
            ),
            SizedBox(height: 29.h),
          ],
        ),
      ),
    );
  }
}
