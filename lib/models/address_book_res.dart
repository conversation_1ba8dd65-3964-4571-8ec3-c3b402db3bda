import 'package:json_annotation/json_annotation.dart';

part 'address_book_res.g.dart';

@JsonSerializable()
class AddressBookRes {
  AddressBookRes();

  late String id;
  String? type;
  String? addressType;
  String? network;
  String? address;
  late String trustFlag;
  String? remark;
  String? symbol;
  bool? isCheck;

  factory AddressBookRes.fromJson(Map<String, dynamic> json) =>
      _$AddressBookResFromJson(json);
  Map<String, dynamic> toJson() => _$AddressBookResToJson(this);
}
