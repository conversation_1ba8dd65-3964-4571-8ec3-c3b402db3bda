import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/models/user_node_level_res.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../widgets/team_table.dart';

class NextTeamPage extends StatefulWidget {
  const NextTeamPage({super.key, required this.uid, required this.name});
  final String uid;
  final String name;

  @override
  _NextTeamPageState createState() => _NextTeamPageState();
}

class _NextTeamPageState extends State<NextTeamPage> {
  List<UserNodeLevelRes> teamList = [];
  bool isTeamLoading = true;
  bool isTeamEmpty = false;

  @override
  void initState() {
    super.initState();
    getTeamList();
  }

  getTeamList() async {
    try {
      setState(() {
        isTeamLoading = true;
        isTeamEmpty = false;
      });
      final res = await UserApi.getUserNodeLevelList({
        'pageNum': 1,
        'pageSize': 500,
        'userReferee': widget.uid,
      });
      teamList = res.list;
      isTeamEmpty = res.isEnd && res.list.isEmpty;
    } catch (e) {
      e.printError();
    }
    isTeamLoading = false;
    if (!mounted) {
      return;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.name)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
        child: TeamTable(
          hideAction: true,
          teamList: teamList,
          loading: isTeamLoading,
          isEmpty: isTeamEmpty,
        ),
      ),
    );
  }
}
