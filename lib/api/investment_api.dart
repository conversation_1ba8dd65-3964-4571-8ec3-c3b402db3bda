import 'package:aurenixai_app/models/fund_investment_record_res.dart';
import 'package:aurenixai_app/models/fund_investment_res.dart';
import 'package:aurenixai_app/models/fund_product_res.dart';
import 'package:aurenixai_app/models/fund_product_total_res.dart';
import 'package:aurenixai_app/models/investment_log_res.dart';
import 'package:aurenixai_app/models/investment_summary.dart';
import 'package:aurenixai_app/models/page_info.dart';
import 'package:aurenixai_app/utils/http_util.dart';
import 'package:aurenixai_app/utils/index.dart';

class InvestmentApi {
  static Future<List<FundProductRes>> getFundProductList() async {
    final res = await HttpUtil.post('/core/v1/fund_product/list_front');
    return (res as List<dynamic>)
        .map((e) => FundProductRes.fromJson(CommonUtils.removeNullKeys(e)))
        .toList();
  }

  static Future<FundProductRes> getFundProduct(String id) async {
    final res = await HttpUtil.post(
      '/core/v1/fund_product/public/detail_front/$id',
    );
    return FundProductRes.fromJson(CommonUtils.removeNullKeys(res));
  }

  // 查询投资首页百分比
  static Future<FundProductTotalRes> getFundProductTotal() async {
    final res = await HttpUtil.post('/core/v1/fund_product/user_total');
    return FundProductTotalRes.fromJson(CommonUtils.removeNullKeys(res));
  }

  static Future<FundInvestmentRes> investmentProduct(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post(
      '/core/v1/fund_investment_record/create',
      data,
    );
    return FundInvestmentRes.fromJson(CommonUtils.removeNullKeys(res));
  }

  static Future<PageInfo<InvestmentSummary>> getFundInvestmentRecordList(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post(
      '/core/v1/fund_investment_record/my_page_front',
      data,
    );
    return PageInfo.fromJson<InvestmentSummary>(
      res,
      InvestmentSummary.fromJson,
    );
  }

  static Future<PageInfo<FundInvestmentRecordRes>>
  getFundInvestmentDetailRecordList(Map<String, dynamic> data) async {
    final res = await HttpUtil.post(
      '/core/v1/fund_investment_record/page_front',
      data,
    );
    return PageInfo.fromJson<FundInvestmentRecordRes>(
      res,
      FundInvestmentRecordRes.fromJson,
    );
  }

  static Future<PageInfo<InvestmentLogRes>> getFundInvestmentLogList(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post(
      '/core/v1/fund_investment_log/page_front',
      data,
    );
    return PageInfo.fromJson<InvestmentLogRes>(res, InvestmentLogRes.fromJson);
  }

  // 设置复投 1开启，0关闭
  static Future<void> modifyReinvest(
    String investmentId,
    String isReinvestEnabled,
  ) async {
    await HttpUtil.post('/core/v1/fund_investment_record/set_reinvest_front', {
      'investmentId': investmentId,
      'isReinvestEnabled': isReinvestEnabled,
    });
  }

  // 前端赎回申请
  static Future<void> applyRedeem(
    String investmentId,
    String redeemAmount,
  ) async {
    await HttpUtil.post('/core/v1/fund_redeem_request/create', {
      'investmentId': investmentId,
      'redeemAmount': redeemAmount,
    });
  }
}
