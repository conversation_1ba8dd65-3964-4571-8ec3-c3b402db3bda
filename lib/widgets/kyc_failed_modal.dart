import 'package:aurenixai_app/config/index.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:crisp_chat/crisp_chat.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';

class KycFailedModal extends StatefulWidget {
  const KycFailedModal({super.key});

  static Future<T?> showModal<T>({required BuildContext context}) async {
    return showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return KycFailedModal();
      },
    );
  }

  @override
  State<KycFailedModal> createState() => _KycFailedModalState();
}

class _KycFailedModalState extends State<KycFailedModal> {
  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final user = context.read<AuthProvider>().user;
    final failRemark = user?.failRemark;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.w)),
      insetPadding: EdgeInsets.symmetric(horizontal: 26.w),
      child: Container(
        width: 323.w,
        padding: EdgeInsets.fromLTRB(32.w, 22.w, 17.w, 28.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.w),
          color: customTheme.inputBgColor,
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(right: 10.w, top: 15.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'kyc_verification_failed'.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 34.w),
                  Text(
                    // 默认提示，如果有错误原因，则显示错误原因
                    failRemark ?? "kyc_verification_failed_description".tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 34.w),
                  Row(
                    spacing: 9.w,
                    children: [
                      Expanded(
                        child: OutlineButton(
                          title: 'contact_us'.tr,
                          fontSize: 13.sp,
                          onPress: () {
                            Navigator.pop(context);
                            final userInfo = context.read<AuthProvider>().user;
                            if (userInfo == null) {
                              return;
                            }
                            final crispUser = User(
                              email: userInfo.email,
                              nickName: userInfo.email,
                              phone: userInfo.mobile ?? '',
                              avatar: userInfo.photo,
                            );
                            final _crispConfig = CrispConfig(
                              websiteID: AppConfig.crispWebsiteID,
                              user: crispUser,
                              enableNotifications: false,
                            );
                            FlutterCrispChat.openCrispChat(
                              config: _crispConfig,
                            );
                          },
                        ),
                      ),
                      Expanded(
                        child: PrimaryButton(
                          title: 're_verify_now'.tr,
                          fontSize: 13.sp,
                          onPress: () {
                            Navigator.pop(context);
                            Get.toNamed(Routes.kyc);
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: IconButton.styleFrom(
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                ),
                iconSize: 26.w,
                icon: Icon(Icons.close_rounded, color: customTheme.textColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
