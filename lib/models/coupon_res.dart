import 'package:json_annotation/json_annotation.dart';

part 'coupon_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class CouponRes {
  CouponRes();

  late String id;
  late String voucherId;
  late String type;
  late String code;
  late String name;
  late String status;
  late num expiredDatetime;

  factory CouponRes.fromJson(Map<String, dynamic> json) =>
      _$CouponResFromJson(json);
  Map<String, dynamic> toJson() => _$CouponResToJson(this);
}
