import 'package:aurenixai_app/models/config.dart';
import 'package:aurenixai_app/models/coupon_res.dart';
import 'package:aurenixai_app/models/dict.dart';
import 'package:aurenixai_app/models/navigate.dart';
import 'package:aurenixai_app/models/page_info.dart';
import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/models/sms_res.dart';
import 'package:aurenixai_app/utils/http_util.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:get/get.dart';

/// 短信业务类型
enum SmsBizType { registerEmail, forgetPassword, modifyPassword }

const smsBizTypeValues = [
  'C_REG_EMAIL',
  'C_FORGET_LOGINPWD',
  'MODIFY_LOGIN_PWD',
];

extension SmsBizTypeExtension on SmsBizType {
  String get value => smsBizTypeValues[index];
}

class CommonApi {
  static Future<List<Region>> getRegionList() async {
    final res = await HttpUtil.post('/core/v1/country/public/list_front');
    return (res as List<dynamic>)
        .map((e) => Region.fromJson(CommonUtils.removeNullKeys(e)))
        .toList();
  }

  static Future<void> sendEmailCode({
    required String email,
    required SmsBizType bizType,
    String? googleToken,
  }) async {
    final res = await HttpUtil.post(
      '/core/v1/sms_out/permission_none/email_code',
      {'email': email, 'bizType': bizType.value, 'googleToken': googleToken},
    );
    return res;
  }

  /// 获取系统参数
  static Future<Config> getConfig({
    String? type,
    String? key,
    List<String>? typeList,
  }) async {
    final res = await HttpUtil.post('/core/v1/config/public/list', {
      "type": type,
      "key": key,
      "typeList": typeList,
    });
    return Config.fromJson(CommonUtils.removeNullKeys(res));
  }

  /// 获取数据字典
  static Future<List<Dict>> getDictList({
    String? type,
    String? key,
    String? parentKey,
    List<String>? parentKeyList,
  }) async {
    try {
      final res = await HttpUtil.post('/core/v1/dict/public/list', {
        "type": type,
        "key": key,
        "parentKey": parentKey,
        "parentKeyList": parentKeyList,
      });
      List<Dict> dictList = (res as List<dynamic>)
          .map((item) => Dict.fromJson(CommonUtils.removeNullKeys(item)))
          .toList();
      return dictList;
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取导航列表
  static Future<List<Navigate>> getNavigateList(
    String location, {
    String? type,
  }) async {
    final res = await HttpUtil.post('/core/v1/cnavigate/public/list_front', {
      "location": location,
      "type": type ?? 'app_banner',
      "status": "1",
    });
    List<Navigate> navList = (res as List<dynamic>)
        .map((item) => Navigate.fromJson(CommonUtils.removeNullKeys(item)))
        .toList();
    return navList;
  }

  /// 通知分页查询
  static Future<PageInfo<SmsRes>> getNoticeList(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post('/core/v1/sms/page_notices', data);
    return PageInfo.fromJson<SmsRes>(res, SmsRes.fromJson);
  }

  /// 交易通知分页查询
  static Future<PageInfo<SmsRes>> getTradeNoticeList(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post('/core/v1/sms/page_transaction', data);
    return PageInfo.fromJson<SmsRes>(res, SmsRes.fromJson);
  }

  /// 通知详情
  static Future<SmsRes> getNoticeDetail(String id) async {
    final res = await HttpUtil.post('/core/v1/sms/detail_sms/$id');
    return SmsRes.fromJson(CommonUtils.removeNullKeys(res));
  }

  /// 优惠券分页查询
  static Future<PageInfo<CouponRes>> getCouponList(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post('/core/v1/voucher_user/page_front', data);
    return PageInfo.fromJson<CouponRes>(res, CouponRes.fromJson);
  }
}
