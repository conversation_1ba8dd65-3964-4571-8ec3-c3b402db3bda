import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/common_picker.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/normal_select.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SendChainPage extends StatefulWidget {
  const SendChainPage({super.key});

  @override
  _SendChainPageState createState() => _SendChainPageState();
}

class _SendChainPageState extends State<SendChainPage>
    with SingleTickerProviderStateMixin {
  TextEditingController addressController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  TextEditingController voucherController = TextEditingController();

  List<String> currencyList = ['USDT', 'USDC'];
  int currencyIndex = 0;

  List<String> networkList = ['TRC20'];
  int networkIndex = 0;

  String? addressBookId;
  String? trustFlag;
  AccountRes? accountData;

  @override
  void initState() {
    super.initState();
    _getMyAccount();
  }

  @override
  void dispose() {
    addressController.dispose();
    amountController.dispose();
    voucherController.dispose();
    super.dispose();
  }

  Future<void> _getMyAccount() async {
    try {
      final res = await AccountApi.getMyAccount();
      setState(() {
        accountData = res;
      });
    } catch (e) {}
  }

  void onSubmit() async {
    final result = await Get.toNamed(
      Routes.sendChainConfirm,
      parameters: {
        'currency': currencyList[currencyIndex],
        'network': networkList[networkIndex],
        'addressBookId': addressBookId ?? '',
        'address': addressController.text,
        'amount': amountController.text,
        'trustFlag': trustFlag ?? '0',
        'voucherUserId': voucherController.text,
      },
    );
    if (result != null && result['success'] == true) {
      Get.back(result: {'success': true});
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    List<String> currencyImage = [
      customThemeImage.accountCurrencyUsdt,
      customThemeImage.accountCurrencyUsdc,
    ];
    return Scaffold(
      appBar: AppBar(title: Text('Send'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 6.h),
                  NormalSelect(
                    label: 'Currency'.tr,
                    hintText: 'select_currency'.tr,
                    content: currencyList[currencyIndex],
                    prefix: Image.asset(
                      currencyImage[currencyIndex],
                      width: 22.w,
                      height: 22.w,
                      fit: BoxFit.fill,
                    ),
                    onTap: () {
                      CommonPicker.showModal(
                            context,
                            title: 'select_currency'.tr,
                            list: currencyList,
                            contentBuilder: (context, index) => Row(
                              children: [
                                Image.asset(
                                  currencyImage[index],
                                  width: 22.w,
                                  height: 22.w,
                                  fit: BoxFit.fill,
                                ),
                                SizedBox(width: 7.w),
                                Text(
                                  currencyList[index],
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    color: customTheme.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          )
                          .then((index) {
                            if (index != null) {
                              setState(() {
                                currencyIndex = index;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                  ),
                  SizedBox(height: 18.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "Address".tr,
                        hintText: 'enter_transfer_address'.tr,
                        controller: addressController,
                        padding: EdgeInsets.only(left: 15.w, right: 100.w),
                      ),
                      Positioned(
                        bottom: 18.w,
                        right: 12.w,
                        width: 20.w,
                        height: 20.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          child: Image.asset(
                            customThemeImage.sendChainIconAddress,
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.fill,
                          ),
                          onTap: () async {
                            final result = await Get.toNamed(
                              Routes.addressBook,
                              parameters: {'type': 'chain'},
                            );
                            setState(() {
                              addressBookId = result['id'];
                              addressController.text = result['address'];
                              trustFlag = result['trustFlag'];
                            });
                          },
                        ),
                      ),
                      Positioned(
                        bottom: 18.w,
                        right: 40.w,
                        width: 20.w,
                        height: 20.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          child: Image.asset(
                            customThemeImage.sendChainIconScan,
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.fill,
                          ),
                          onTap: () {},
                        ),
                      ),
                      Positioned(
                        bottom: 18.w,
                        right: 68.w,
                        width: 20.w,
                        height: 20.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          child: Image.asset(
                            customThemeImage.sendChainIconCopy,
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.fill,
                          ),
                          onTap: () {},
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 18.h),
                  NormalSelect(
                    label: 'Network'.tr,
                    hintText: 'enter_transfer_address'.tr,
                    content: networkList[networkIndex],
                    onTap: () {
                      CommonPicker.showModal(
                            context,
                            title: 'select_network'.tr,
                            list: networkList,
                            contentBuilder: (context, index) => Row(
                              children: [
                                Text(
                                  networkList[index],
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    color: customTheme.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          )
                          .then((index) {
                            if (index != null) {
                              setState(() {
                                networkIndex = index;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                  ),
                  SizedBox(height: 18.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "Transfer_amount".tr,
                        hintText: 'Enter_amount'.tr,
                        controller: amountController,
                        padding: EdgeInsets.only(left: 15.w, right: 80.w),
                      ),
                      Positioned(
                        bottom: 15.w,
                        right: 15.w,
                        child: PrimaryButton(
                          title: 'Max'.tr,
                          width: 50.w,
                          height: 28.w,
                          onPress: () {
                            setState(() {
                              amountController.text =
                                  accountData?.availableAmount ?? '0';
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '1 USD ≈ 1.00 USDT',
                        style: TextStyle(
                          fontSize: 12.w,
                          fontWeight: FontWeight.w400,
                          color: customTheme.textColor,
                        ),
                      ),
                      Text(
                        'Minimum: \$ 1',
                        style: TextStyle(
                          fontSize: 12.w,
                          fontWeight: FontWeight.w400,
                          color: customTheme.textColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 18.h),
                  NormalInput(
                    label: "Enter_voucher_code_if_any".tr,
                    hintText: 'Enter_voucher_code'.tr,
                    controller: voucherController,
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomWrapper(
            context: context,
            color: customTheme.pageBgColor,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 12.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Receive_amount'.tr,
                      style: TextStyle(
                        fontSize: 12.w,
                        fontWeight: FontWeight.w400,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                    Text(
                      '${amountController.text} ${currencyList[currencyIndex]}',
                      style: TextStyle(
                        fontSize: 13.w,
                        fontWeight: FontWeight.w600,
                        color: customTheme.textColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 11.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Fees'.tr,
                      style: TextStyle(
                        fontSize: 12.w,
                        fontWeight: FontWeight.w400,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                    Text(
                      '1 USDT',
                      style: TextStyle(
                        fontSize: 13.w,
                        fontWeight: FontWeight.w600,
                        color: customTheme.textColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20.h),
                PrimaryButton(
                  title: 'Withdraw'.tr,
                  height: 45.w,
                  onPress: onSubmit,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
