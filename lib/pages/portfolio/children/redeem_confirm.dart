import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/fund_investment_record_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class RedeemConfirmPage extends StatefulWidget {
  const RedeemConfirmPage({
    super.key,
    required this.investment,
    required this.productName,
    required this.redeemFeeRate,
    required this.receiveAmount,
    required this.redeemFee,
    required this.redeemAmount,
  });

  final FundInvestmentRecordRes investment;
  final String productName;
  final String redeemFeeRate;
  final String receiveAmount;
  final String redeemFee;
  final String redeemAmount;

  @override
  _RedeemConfirmPageState createState() => _RedeemConfirmPageState();
}

class _RedeemConfirmPageState extends State<RedeemConfirmPage> {
  double remainBalance = 0.0;

  @override
  void initState() {
    super.initState();
    remainBalance = _safeSubtract(
      widget.investment.currentBalance.toDouble(),
      double.parse(widget.redeemAmount),
    );
  }

  /// 安全的减法运算，避免浮点数精度问题
  double _safeSubtract(double a, double b) {
    // 将小数转换为整数进行计算，然后再转回小数
    const int precision = 10000;
    int intA = (a * precision).round();
    int intB = (b * precision).round();
    double result = (intA - intB) / precision;
    // 保留2位小数
    return double.parse(result.toStringAsFixed(2));
  }

  void onSubmit() async {
    try {
      ToastUtil.showLoading();
      await InvestmentApi.applyRedeem(
        widget.investment.id,
        widget.redeemAmount,
      );
      ToastUtil.dismiss();
      EventBusUtil.fireRedeemApply(RedeemApplyEvent());
      Get.offAndToNamed(
        Routes.redeemSuccessfully,
        parameters: {
          'redeemAmount': widget.redeemAmount,
          "redemptionPeriodDays": widget.investment.redemptionPeriodDays
              .toString(),
        },
      );
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('redemption'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          children: [
            SizedBox(height: 19.h),
            Text(
              'confirm_redemption'.tr,
              style: TextStyle(
                fontSize: 20.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 10.h),
            Text(
              '${'you_are_about_to_redeem_your_allocation'.tr}:',
              style: TextStyle(
                fontSize: 13.sp,
                color: customTheme.primaryColor,
              ),
            ),
            SizedBox(height: 22.h),
            Container(
              decoration: BoxDecoration(
                color: customTheme.ff1f1f2a,
                borderRadius: BorderRadius.circular(3.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
              child: Column(
                children: [
                  _line(
                    'redemption_amount'.tr,
                    '\$ ${widget.redeemAmount.fmt}',
                  ),
                  _line(
                    '${'less_redemption_fee'.tr} (${widget.redeemFeeRate}%)',
                    '-\$ ${widget.redeemFee.fmt}',
                  ),
                  _line(
                    'amount_to_receive'.tr,
                    '\$ ${widget.receiveAmount.fmt}',
                  ),
                  _line(
                    'redemption_processing_period'.tr,
                    '${widget.investment.redemptionPeriodDays} ${widget.investment.redemptionPeriodDays > 1 ? 'days'.tr : 'day'.tr}',
                  ),
                  _line(
                    'remaining_allocation_balance'.tr,
                    '\$ ${remainBalance.toString().fmt}',
                    isLast: true,
                  ),
                ],
              ),
            ),
            SizedBox(height: 19.h),
            Align(
              alignment: Alignment.centerLeft,
              child: Wrap(
                spacing: 3.5.w,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Image.asset(
                    customThemeImage.kycWarn,
                    width: 15.w,
                    height: 15.w,
                    fit: BoxFit.fill,
                  ),
                  Text(
                    'kyc_note'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 7.h),
            Text(
              "redeem_note_description".tr,
              style: TextStyle(
                fontSize: 13.sp,
                color: customTheme.subTextColor,
              ),
              textAlign: TextAlign.justify,
            ),
            SizedBox(height: 10.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 9.5.w, vertical: 6.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.5.r),
                border: Border.all(color: customTheme.primaryColor),
              ),
              child: Row(
                spacing: 8.w,
                children: [
                  Image.asset(
                    customThemeImage.commonWarnTip,
                    width: 23.w,
                    height: 23.w,
                    fit: BoxFit.fill,
                  ),
                  Expanded(
                    child: Text(
                      "redeem_note_description1".tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.textColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 29.h),
            Row(
              spacing: 11.w,
              children: [
                Expanded(
                  child: OutlineButton(
                    title: 'cancel'.tr,
                    fontSize: 13.sp,
                    onPress: () {
                      Get.back();
                    },
                  ),
                ),
                Expanded(
                  child: PrimaryButton(
                    title: 'confirm_redemption'.tr,
                    fontSize: 13.sp,
                    onPress: onSubmit,
                  ),
                ),
              ],
            ),
            SizedBox(height: 29.h),
          ],
        ),
      ),
    );
  }

  Widget _line(String title, String value, {bool isLast = false}) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    final content = Padding(
      padding: EdgeInsets.symmetric(vertical: 16.5.h),
      child: Row(
        spacing: 4.w,
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                color: customTheme.labelColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13.sp,
              color: customTheme.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
    if (isLast) {
      return content;
    }
    return Column(children: [content, Divider(height: 0.5)]);
  }
}
