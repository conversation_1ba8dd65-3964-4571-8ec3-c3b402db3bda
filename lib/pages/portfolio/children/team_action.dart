import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/user_node_level_res.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class TeamActionPage extends StatefulWidget {
  const TeamActionPage({super.key, required this.info});
  final UserNodeLevelRes info;

  @override
  _TeamActionPageState createState() => _TeamActionPageState();
}

class _TeamActionPageState extends State<TeamActionPage> {
  final TextEditingController _amountController = TextEditingController();
  String applyNodeType = '0'; // 0:AP,1:CP

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  onSubmit() async {
    if (_amountController.text.isEmpty) {
      ToastUtil.showToast(
        'please_enter_minimum_required_maintenance_amount'.tr,
      );
      return;
    }
    if (!_amountController.text.isNum) {
      ToastUtil.showToast('please_enter_valid_amount'.tr);
      return;
    }
    try {
      ToastUtil.showLoading();
      await UserApi.applyUpgrade({
        'upgradeUser': widget.info.userId,
        'applyNodeType': applyNodeType,
        'maintainPerformance': _amountController.text,
      });
      ToastUtil.dismiss();
      EventBusUtil.fireUserNodeUpgrade(
        UserNodeUpgradeEvent(userId: widget.info.userId),
      );
      Get.offAndToNamed(
        Routes.teamActionSuccessfully,
        parameters: {
          "name": widget.info.userName,
          "levelName": applyNodeType == '0'
              ? 'Affiliate Partner (AP)'.tr
              : 'Certified Partner (CP)'.tr,
        },
      );
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    final fundInvestmentRecordUserLevelPageResList =
        widget.info.fundInvestmentRecordUserLevelPageResList;
    double overallAllocation = 0;
    for (var element in fundInvestmentRecordUserLevelPageResList) {
      overallAllocation += element.totalInvested;
    }

    final nodeLevel = context.read<AuthProvider>().user?.nodeLevel ?? 1;

    return Scaffold(
      appBar: AppBar(title: Text('action'.tr)),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${'Full Name'.tr}: ${widget.info.userName}',
                    style: TextStyle(
                      fontSize: 19.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 22.h),
                  Text(
                    'portfolio'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 13.5.h),
                  for (var item in fundInvestmentRecordUserLevelPageResList)
                    Text(
                      item.productName,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.subTextColor,
                      ),
                    ),
                  Padding(
                    padding: EdgeInsets.only(top: 6.h),
                    child: Text(
                      '${'overall_investment'.tr}: \$ ${overallAllocation.toStringAsFixed(2).removeZero.fmt}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.subTextColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 22.5.h),
                  Divider(height: 0.5),
                  SizedBox(height: 12.h),
                  Text(
                    'select_new_tier'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  if (nodeLevel > 2)
                    Padding(
                      padding: EdgeInsets.only(top: 12.5.h),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            applyNodeType = '0';
                          });
                        },
                        child: Row(
                          spacing: 10.5.w,
                          children: [
                            Image.asset(
                              applyNodeType == '0'
                                  ? customThemeImage.kycSelect
                                  : customThemeImage.kycUnselect,
                              width: 16.w,
                              height: 16.w,
                            ),
                            Text(
                              'Affiliate Partner (AP)'.tr,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: customTheme.textColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (nodeLevel > 3)
                    Padding(
                      padding: EdgeInsets.only(top: 12.5.h),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            applyNodeType = '1';
                          });
                        },
                        child: Row(
                          spacing: 10.5.w,
                          children: [
                            Image.asset(
                              applyNodeType == '1'
                                  ? customThemeImage.kycSelect
                                  : customThemeImage.kycUnselect,
                              width: 16.w,
                              height: 16.w,
                            ),
                            Text(
                              'Certified Partner (CP)'.tr,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: customTheme.subTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  SizedBox(height: 25.h),
                  Divider(height: 0.5),
                  SizedBox(height: 24.5.h),
                  NormalInput(
                    label: 'enter_minimum_required_maintenance_amount'.tr,
                    hintText: '0.00',
                    controller: _amountController,
                    keyboardType: TextInputType.number,
                    height: 61.h,
                    fontSize: 23.sp,
                    fontWeight: FontWeight.bold,
                    prefix: Padding(
                      padding: EdgeInsets.only(right: 6.w),
                      child: Text(
                        '\$',
                        style: TextStyle(
                          fontSize: 23.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 27.5.h),
                  Divider(height: 0.5),
                  SizedBox(height: 27.h),
                  Wrap(
                    spacing: 3.5.w,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      Image.asset(
                        customThemeImage.kycWarn,
                        width: 15.w,
                        height: 15.w,
                        fit: BoxFit.fill,
                      ),
                      Text(
                        'kyc_note'.tr,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: customTheme.textColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 7.h),
                  Text(
                    "1.Accounts upgraded to a higher tier must maintain the minimum investment amount. Failure to meet this requirement will result in ineligibility to earn commissions.",
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.subTextColor,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                  SizedBox(height: 14.h),
                  Text(
                    "2.Any commissions accrued while the account is ineligible will be disbursed to the respective qualified accounts. Commission will resume upon the account's fulfillment of the minimum investment requirement. Tier advancement is subject to approval by the platform.",
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.subTextColor,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                  SizedBox(height: 29.h),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'Confirm Tier Advancement',
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }
}
