// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fund_product_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FundProductRes _$FundProductResFromJson(Map<String, dynamic> json) =>
    FundProductRes()
      ..id = json['id'] as String
      ..name = json['name'] as String
      ..lockinPeriodDays = json['lockinPeriodDays'] as num
      ..interestCycleDays = json['interestCycleDays'] as num
      ..interestRatePerCycle = json['interestRatePerCycle'] as num
      ..redemptionPeriodDays = json['redemptionPeriodDays'] as num
      ..minAmount = json['minAmount'] as num
      ..allowReinvest = json['allowReinvest'] as String
      ..text = json['text'] as String?
      ..introduce = json['introduce'] as String?
      ..structure = json['structure'] as String?
      ..description = json['description'] as String?;

Map<String, dynamic> _$FundProductResToJson(FundProductRes instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'lockinPeriodDays': instance.lockinPeriodDays,
      'interestCycleDays': instance.interestCycleDays,
      'interestRatePerCycle': instance.interestRatePerCycle,
      'redemptionPeriodDays': instance.redemptionPeriodDays,
      'minAmount': instance.minAmount,
      'allowReinvest': instance.allowReinvest,
      'text': instance.text,
      'introduce': instance.introduce,
      'structure': instance.structure,
      'description': instance.description,
    };
