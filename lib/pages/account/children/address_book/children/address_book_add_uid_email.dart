import 'package:aurenixai_app/api/address_api.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddressBookAddUidEmailPage extends StatefulWidget {
  const AddressBookAddUidEmailPage({super.key});

  @override
  _SendUidEmailPageState createState() => _SendUidEmailPageState();
}

class _SendUidEmailPageState extends State<AddressBookAddUidEmailPage>
    with SingleTickerProviderStateMixin {
  TextEditingController emailController = TextEditingController();
  TextEditingController uidController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  late TabController _tabController;
  int currentTabIndex = 0;

  bool isAgree = false;

  @override
  void initState() {
    super.initState();

    final args = Get.parameters;
    String type = args['type'] ?? '';
    setState(() {
      currentTabIndex = type == 'email' ? 0 : 1;
    });
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    emailController.dispose();
    uidController.dispose();
    remarkController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    if (currentTabIndex == 0) {
      if (emailController.text.isEmpty) {
        ToastUtil.showToast('please_enter_email'.tr);
        return;
      }
    } else {
      if (uidController.text.isEmpty) {
        ToastUtil.showToast('please_enter_uid'.tr);
        return;
      }
    }
    try {
      ToastUtil.showLoading();
      await AddressApi.addressTransferAdd({
        'address': currentTabIndex == 0
            ? emailController.text
            : uidController.text,
        'addressType': currentTabIndex == 0 ? '0' : '1',
        'remark': remarkController.text,
      });
      ToastUtil.dismiss();
      Get.back();
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    return Scaffold(
      appBar: AppBar(title: Text('send'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _tabs(),
                  SizedBox(height: 20.h),
                  currentTabIndex == 0
                      ? NormalInput(
                          label: "payee".tr,
                          hintText: 'enter_email'.tr,
                          controller: emailController,
                          padding: EdgeInsets.only(left: 15.w, right: 100.w),
                        )
                      : NormalInput(
                          label: "payee".tr,
                          hintText: 'enter_uid'.tr,
                          controller: uidController,
                          padding: EdgeInsets.only(left: 15.w, right: 100.w),
                        ),
                  SizedBox(height: 20.5.h),
                  NormalInput(
                    label: "Remark_optional".tr,
                    hintText: '20_character_remark'.tr,
                    controller: remarkController,
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomWrapper(
            context: context,
            color: customTheme.pageBgColor,
            decoration: const BoxDecoration(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 12.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          isAgree = !isAgree;
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.only(top: 3.w),
                        child: Image.asset(
                          isAgree
                              ? customThemeImage.authChoose
                              : customThemeImage.authUnchoose,
                          width: 18.w,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        'set_trusted_payee_to_skip'.tr,
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: customTheme.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 35.h),
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: 7.w,
                    horizontal: 15.w,
                  ),
                  decoration: BoxDecoration(
                    color: customTheme.inputBgColor,
                    border: Border(
                      top: BorderSide(
                        color: customTheme.dividerColor,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: PrimaryButton(
                    title: '$currentTabIndex',
                    height: 45.w,
                    onPress: onSubmit,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _tabs() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    // 获取当前文字方向，如果是从右到左，则需要将TabBar的labelPadding设置为0
    final textDirection = Directionality.of(context);
    final labelPadding = textDirection == TextDirection.rtl
        ? EdgeInsets.only(left: 54.w)
        : EdgeInsets.only(right: 54.w);

    return Padding(
      padding: EdgeInsets.only(top: 7.h),
      child: TabBar(
        controller: _tabController,
        onTap: (index) {
          setState(() {
            currentTabIndex = index;
            emailController.text = '';
            uidController.text = '';
            remarkController.text = '';
          });
        },
        indicatorWeight: 3.w,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 6.w),
        isScrollable: true,
        dividerHeight: 0.5,
        tabAlignment: TabAlignment.start,
        dividerColor: customTheme.dividerColor,
        labelPadding: labelPadding,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.lightTextColor,
          fontWeight: FontWeight.w600,
        ),
        tabs: [
          Tab(child: Text('email'.tr)),
          Tab(child: Text('UID'.tr)),
        ],
      ),
    );
  }
}
