import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/aws_upload_util.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/common_image.dart';
import 'package:aurenixai_app/widgets/common_picker.dart';
import 'package:aurenixai_app/widgets/country_region_picker.dart';
import 'package:aurenixai_app/widgets/image_picker_modal.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/normal_select.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class KycVerifyPage extends StatefulWidget {
  const KycVerifyPage({super.key});

  @override
  _KycVerifyPageState createState() => _KycVerifyPageState();
}

class _KycVerifyPageState extends State<KycVerifyPage> {
  final _fullNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  List<String> idTypeList = ['id_card'.tr, 'passport'.tr];
  int idTypeIndex = 0;
  final _idNumberController = TextEditingController();
  String frontImage = '';
  String backImage = '';
  String passportImage = '';

  Region? currentCountry;

  @override
  void initState() {
    super.initState();
    getLastKycRecord();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneNumberController.dispose();
    super.dispose();
  }

  void getLastKycRecord() async {
    try {
      ToastUtil.showLoading();
      final res = await UserApi.getLastKycRecord();
      if (res != null) {
        setState(() {
          _fullNameController.text = res.fullName;
          _phoneNumberController.text = res.mobile;
          idTypeIndex = res.type == '0' ? 0 : 1;
          _idNumberController.text = res.idNO;
          if (res.type == '0') {
            frontImage = res.frontPhoto ?? '';
            backImage = res.backPhoto ?? '';
          } else {
            passportImage = res.passportPhoto ?? '';
          }
          currentCountry = context
              .read<LanguageProvider>()
              .regionList
              .firstWhere(
                (element) => element.id == res.interId,
                orElse: () => context.read<LanguageProvider>().region!,
              );
        });
      } else {
        setState(() {
          currentCountry = context.read<LanguageProvider>().region;
        });
      }
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  void onSubmit() {
    final fullName = _fullNameController.text;
    final phoneNumber = _phoneNumberController.text;
    final frontImage = this.frontImage;
    final backImage = this.backImage;
    final idNumber = _idNumberController.text;

    if (fullName.isEmpty) {
      ToastUtil.showToast('please_enter_your_full_name'.tr);
      return;
    }

    if (phoneNumber.isEmpty) {
      ToastUtil.showToast('please_enter_your_phone_number'.tr);
      return;
    }

    if (idNumber.isEmpty) {
      ToastUtil.showToast('please_enter_your_id_document_number'.tr);
      return;
    }

    if (idTypeIndex == 0) {
      if (frontImage.isEmpty) {
        ToastUtil.showToast('please_upload_your_front_photo'.tr);
        return;
      }

      if (backImage.isEmpty) {
        ToastUtil.showToast('please_upload_your_back_photo'.tr);
        return;
      }
    } else {
      if (passportImage.isEmpty) {
        ToastUtil.showToast('please_upload_your_passport_photo'.tr);
        return;
      }
    }

    Get.toNamed(
      Routes.kycQuestion,
      parameters: {
        'type': idTypeIndex.toString(),
        'fullName': fullName,
        'interId': currentCountry!.id,
        'idNO': idNumber,
        'mobile': phoneNumber,
        'frontPhoto': frontImage,
        'backPhoto': backImage,
        'passportPhoto': passportImage,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: Text('verify_your_identity'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              padding: EdgeInsets.only(left: 15.w, right: 7.w, bottom: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 11.5.h),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Text(
                      'kyc_verify_description'.tr,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: customTheme.subTextColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 18.h),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Text(
                      'kyc_verify_description_2'.tr,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: customTheme.subTextColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 27.h),
                  _title('full_name'.tr),
                  SizedBox(height: 8.h),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: NormalInput(
                      hintText: 'enter_your_full_name'.tr,
                      controller: _fullNameController,
                    ),
                  ),
                  SizedBox(height: 22.5.h),
                  _title('phone_number'.tr),
                  SizedBox(height: 8.h),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Row(
                      spacing: 8.w,
                      children: [
                        GestureDetector(
                          onTap: () {
                            CountryRegionPicker.showModal(
                                  context,
                                  currentRegion: currentCountry,
                                )
                                .then((region) {
                                  if (region != null) {
                                    setState(() {
                                      currentCountry = region;
                                    });
                                  }
                                })
                                .catchError((e) {});
                          },
                          child: Container(
                            height: 55.w,
                            padding: EdgeInsets.only(left: 12.5.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(3.w),
                              color: customTheme.inputBgColor,
                            ),
                            child: Row(
                              children: [
                                if (currentCountry != null)
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(11.w),
                                    child: Image.network(
                                      currentCountry!.pic,
                                      height: 16.w,
                                      width: 16.w,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                SizedBox(width: 7.w),
                                if (currentCountry != null)
                                  Text(
                                    currentCountry!.interCode,
                                    style: TextStyle(
                                      fontSize: 15.sp,
                                      color: customTheme.textColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                Icon(
                                  Icons.arrow_drop_down_rounded,
                                  size: 32.w,
                                  color: customTheme.textColor,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: NormalInput(
                            hintText: 'enter_your_phone_number'.tr,
                            controller: _phoneNumberController,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 22.5.h),
                  _title('id_type'.tr),
                  SizedBox(height: 8.h),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: NormalSelect(
                      hintText: 'select_your_id_type'.tr,
                      content: idTypeList[idTypeIndex],
                      onTap: () {
                        CommonPicker.showModal(
                              context,
                              title: 'select_your_id_type'.tr,
                              list: idTypeList,
                            )
                            .then((index) {
                              if (index != null) {
                                setState(() {
                                  idTypeIndex = index;
                                });
                              }
                            })
                            .catchError((e) {});
                      },
                    ),
                  ),
                  SizedBox(height: 27.h),
                  _title('id_document_number'.tr),
                  SizedBox(height: 8.h),
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: NormalInput(
                      hintText: 'enter_your_id_document_number'.tr,
                      controller: _idNumberController,
                    ),
                  ),
                  SizedBox(height: 22.5.h),
                  _title('upload_photos'.tr),
                  SizedBox(height: 1.h),
                  _images(),
                  SizedBox(height: 18.h),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'continue'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }

  Widget _title(String title) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(right: 8.w),
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: title,
              style: TextStyle(fontSize: 13.sp, color: customTheme.textColor),
            ),
            TextSpan(
              text: ' *',
              style: TextStyle(fontSize: 13.sp, color: customTheme.downColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _image({
    required String imageUrl,
    required VoidCallback onDelete,
    required ValueChanged<String> onSuccess,
    String? title,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    Widget upload = Padding(
      padding: EdgeInsets.only(top: 7.w, right: 8.w),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          ImagePickerModal.showModal(context)
              .then((file) async {
                if (file != null) {
                  final bytes = await file.readAsBytes();
                  const maxSize = 10 * 1024 * 1024; // 10M
                  final sizeMb = bytes.lengthInBytes;
                  if (sizeMb > maxSize) {
                    ToastUtil.showToast('image_size_cannot_exceed_10m'.tr);
                    return;
                  }

                  ToastUtil.showLoading(status: "image_uploading".tr);
                  final url = await AwsUploadUtil().upload(file: file);
                  ToastUtil.dismiss();
                  onSuccess(url);
                }
              })
              .catchError((e) {
                ToastUtil.showError('upload_failed_please_try_again'.tr);
              });
        },
        child: Container(
          width: 167.w,
          height: 96.w,
          decoration: BoxDecoration(
            color: customTheme.inputBgColor,
            borderRadius: BorderRadius.circular(3.w),
          ),
          child: Center(
            child: Image.asset(
              customThemeImage.kycPlaceholder,
              width: 30.w,
              height: 30.w,
              fit: BoxFit.fill,
            ),
          ),
        ),
      ),
    );

    if (imageUrl.isNotEmpty) {
      upload = SizedBox(
        height: 103.w,
        width: 175.w,
        child: Stack(
          children: [
            Positioned(
              top: 7.w,
              right: 8.w,
              left: 0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(3.w),
                child: CommonImage(
                  imageUrl,
                  width: 167.w,
                  height: 96.w,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: onDelete,
                child: Image.asset(
                  customThemeImage.kycClose,
                  width: 22.w,
                  height: 22.w,
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      spacing: 8.h,
      children: [
        upload,
        if (title != null)
          Text(
            title,
            style: TextStyle(fontSize: 13.sp, color: customTheme.textColor),
          ),
      ],
    );
  }

  Widget _images() {
    if (idTypeIndex == 0) {
      return Row(
        spacing: 3.w,
        children: [
          _image(
            title: 'front'.tr,
            imageUrl: frontImage,
            onDelete: () {
              setState(() {
                frontImage = '';
              });
            },
            onSuccess: (url) {
              setState(() {
                frontImage = url;
              });
            },
          ),
          _image(
            title: 'back'.tr,
            imageUrl: backImage,
            onDelete: () {
              setState(() {
                backImage = '';
              });
            },
            onSuccess: (url) {
              setState(() {
                backImage = url;
              });
            },
          ),
        ],
      );
    }
    return Row(
      spacing: 3.w,
      children: [
        _image(
          imageUrl: passportImage,
          onDelete: () {
            setState(() {
              passportImage = '';
            });
          },
          onSuccess: (url) {
            setState(() {
              passportImage = url;
            });
          },
        ),
        SizedBox(height: 103.w, width: 175.w),
      ],
    );
  }
}
