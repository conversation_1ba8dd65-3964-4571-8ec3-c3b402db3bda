import 'package:json_annotation/json_annotation.dart';

part 'fund_investment_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class FundInvestmentRes {
  FundInvestmentRes();

  late num createDatetime;
  late num nextInterestDate;

  factory FundInvestmentRes.fromJson(Map<String, dynamic> json) =>
      _$FundInvestmentResFromJson(json);
  Map<String, dynamic> toJson() => _$FundInvestmentResToJson(this);
}
