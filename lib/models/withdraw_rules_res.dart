import 'package:json_annotation/json_annotation.dart';

part 'withdraw_rules_res.g.dart';

@JsonSerializable()
class WithdrawRulesRes {
  WithdrawRulesRes();

  late String id;
  late String symbol;
  late String pic;
  String? network;
  late String type;
  late num withdrawMin;
  late String withdrawFeeType;
  late String withdrawFeeTakeLocation;
  late num withdrawFee;
  late num withdrawFeeMin;
  late num withdrawFeeMax;
  late num withdrawStep;
  late num withdrawLimit;
  late String withdrawRule;

  factory WithdrawRulesRes.fromJson(Map<String, dynamic> json) =>
      _$WithdrawRulesResFromJson(json);
  Map<String, dynamic> toJson() => _$WithdrawRulesResToJson(this);
}
