import 'dart:async' show StreamSubscription;

import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/confirm_identity.dart';

class GooglePage extends StatefulWidget {
  const GooglePage({super.key});

  @override
  _GoogleState createState() => _GoogleState();
}

class _GoogleState extends State<GooglePage> {
  late StreamSubscription<UnbindGoogleSuccessEvent>
  _unbindGoogleSuccessSubscription;
  bool isBindGoogle = false;

  @override
  void initState() {
    super.initState();
    _unbindGoogleSuccessSubscription = EventBusUtil.listenUnbindGoogleSuccess((
      event,
    ) {
      setState(() {
        isBindGoogle = false;
      });
    });
  }

  @override
  void dispose() {
    _unbindGoogleSuccessSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('google_authenticator_h'.tr)),
      floatingActionButton: FloatingActionButton(
        child: Icon(Icons.add),
        onPressed: () {
          setState(() {
            isBindGoogle = !isBindGoogle;
          });
        },
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 29.h),
            Center(
              child: isBindGoogle
                  ? Image.asset(
                      customThemeImage.googleUnbind,
                      width: 129.w,
                      height: 120.w,
                      fit: BoxFit.fill,
                    )
                  : Image.asset(
                      customThemeImage.googleSecure,
                      width: 105.w,
                      height: 120.w,
                      fit: BoxFit.fill,
                    ),
            ),
            SizedBox(height: 15.5.h),
            Center(
              child: Text(
                isBindGoogle
                    ? 'unbind_google_authenticator1'.tr
                    : 'secure_your_account'.tr,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 27.h),
            if (isBindGoogle)
              _content("unbind_google_description".tr)
            else
              _content("bind_google_description".tr),
            SizedBox(height: 15.h),
            if (isBindGoogle)
              _content("unbind_google_description1".tr)
            else
              _content("bind_google_description1".tr),
            SizedBox(height: 15.h),
            if (isBindGoogle) _content("unbind_google_description2".tr),
            SizedBox(height: 15.h),
            if (isBindGoogle) _content("unbind_google_description3".tr),
            if (!isBindGoogle) SizedBox(height: 29.h),
            if (!isBindGoogle)
              Wrap(
                spacing: 3.5.w,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Image.asset(
                    customThemeImage.kycWarn,
                    width: 15.w,
                    height: 15.w,
                    fit: BoxFit.fill,
                  ),
                  Text(
                    'kyc_note'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            if (!isBindGoogle) SizedBox(height: 7.h),
            if (!isBindGoogle)
              Text(
                "bind_google_note".tr,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: customTheme.subTextColor,
                ),
              ),
            SizedBox(height: isBindGoogle ? 29.h : 161.h),
            if (!isBindGoogle)
              PrimaryButton(
                title: 'enable_now'.tr,
                onPress: () {
                  Get.toNamed(Routes.bindGoogle);
                },
              )
            else
              Row(
                spacing: 11.w,
                children: [
                  Expanded(
                    child: OutlineButton(
                      title: 'cancel'.tr,
                      height: 45.w,
                      fontSize: 13.sp,
                      onPress: () {
                        Get.back();
                      },
                    ),
                  ),
                  Expanded(
                    child: PrimaryButton(
                      title: 'continue'.tr,
                      height: 45.w,
                      fontSize: 13.sp,
                      onPress: () {
                        ConfirmIdentityModal.showModal(context);
                      },
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  _content(String text) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Text(
      text,
      style: TextStyle(fontSize: 15.sp, color: customTheme.subTextColor),
    );
  }
}
