import 'package:aurenixai_app/utils/index.dart';

class PageInfo<T> {
  late int pageNum;
  late int pageSize;
  late int size;
  late int total;
  late int pages;
  late List<T> list;

  PageInfo();

  /// 是否最后一页
  bool get isEnd {
    return pages <= pageNum;
  }

  static PageInfo<T> fromJson<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic> json) _fromJson,
  ) {
    PageInfo pageInfo = PageInfo<T>()
      ..pageNum = json["pageNum"] as int
      ..pageSize = json["pageSize"] as int
      ..size = json["size"] as int
      ..total = json["total"] as int
      ..pages = json["pages"] as int;

    pageInfo.list = (json['list'] as List<dynamic>)
        .map((e) => _fromJson(CommonUtils.removeNullKeys(e)))
        .toList();

    return pageInfo as PageInfo<T>;
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
    'pageNum': pageNum,
    'pageSize': pageSize,
    'size': size,
    'total': total,
    'pages': pages,
    'list': list,
  };
}
