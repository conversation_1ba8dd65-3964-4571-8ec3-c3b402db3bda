import 'package:json_annotation/json_annotation.dart';

part 'user_info.g.dart';

@JsonSerializable()
class UserInfo {
  UserInfo();

  late String userId;
  String? fullName;
  String? countryCode;
  String? phoneNumber;
  String? countryName;
  late String email;
  String? documentType;
  String? documentNumber;
  String? idPhoto;
  String? selfie;
  late String verStatus;
  late String kycStatus;
  late String accountLevel;
  late String accountStatus;
  String? nameLevel;

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);
  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}
