import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class VerifyEmailPage extends StatefulWidget {
  const VerifyEmailPage({super.key});

  @override
  _VerifyEmailPageState createState() => _VerifyEmailPageState();
}

class _VerifyEmailPageState extends State<VerifyEmailPage> {
  TextEditingController codeController = TextEditingController();
  List<String> codeDigits = List.filled(6, '');
  FocusNode codeFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    sendCode();
  }

  @override
  void dispose() {
    codeController.dispose();
    codeFocusNode.dispose();
    super.dispose();
  }

  void sendCode() async {
    final arguments = Get.arguments;
    final email = arguments['email'];
    ToastUtil.showLoading();
    try {
      await CommonApi.sendEmailCode(
        email: email,
        bizType: SmsBizType.registerEmail,
      );
      codeFocusNode.requestFocus();
      codeController.clear();
      setState(() {
        codeDigits = List.filled(6, '');
      });
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  void _onCodeChanged() {
    final text = codeController.text;

    setState(() {
      // 更新数字列表
      for (int i = 0; i < 6; i++) {
        if (i < text.length) {
          codeDigits[i] = text[i];
        } else {
          codeDigits[i] = '';
        }
      }
    });
    if (text.length == 6) {
      onSubmit();
    }
  }

  void onSubmit() async {
    codeFocusNode.unfocus();
    ToastUtil.showLoading();
    try {
      final arguments = Get.arguments;
      final email = arguments['email'];
      final password = arguments['password'];
      final countryCode = arguments['countryCode'];
      final inviteCode = arguments['inviteCode'];
      final code = codeController.text;
      final res = await UserApi.registerEmail(
        email: email,
        smsCode: code,
        loginPwd: password,
        countryCode: countryCode,
        inviteCode: inviteCode != null && inviteCode.isNotEmpty
            ? inviteCode
            : null,
      );
      await context.read<AuthProvider>().setToken(res.token);
      Get.offAndToNamed(Routes.signUpSuccessfully);
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customeThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;

    // 使用工具类获取AppBar高度
    final appBarHeight = CommonUtils.getAppBarHeight(context);

    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.transparent),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              customeThemeImage.authBgBottom,
              width: MediaQuery.of(context).size.width,
              height: 313.h,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
            ),
          ),
          Positioned.fill(
            left: 23.w,
            right: 23.w,
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 使用工具类获取的高度
                  SizedBox(height: appBarHeight + 53.5.h),
                  Text(
                    'verify_your_email'.tr,
                    style: TextStyle(
                      fontSize: 22.sp,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 28.5.h),
                  Text(
                    "we_ve_sent_a_verification_code_to_your_email_address_enter_the_code_below_to_verify_your_account_and_complete_your_sign_up"
                        .tr,
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: customTheme.subTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 66.5.h),
                  SizedBox(
                    height: 61.w,
                    child: Stack(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: List.generate(
                            6,
                            (index) => GestureDetector(
                              onTap: () {
                                codeFocusNode.requestFocus();
                              },
                              child: Container(
                                width: 49.w,
                                height: 61.w,
                                decoration: BoxDecoration(
                                  color: customTheme.inputBgColor,
                                  borderRadius: BorderRadius.circular(9.w),
                                ),
                                child: Center(
                                  child: Text(
                                    codeDigits[index],
                                    style: TextStyle(
                                      fontSize: 24.sp,
                                      fontWeight: FontWeight.w600,
                                      color: customTheme.textColor,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Positioned.fill(
                          child: Opacity(
                            opacity: 0,
                            child: NormalInput(
                              decoration: BoxDecoration(),
                              keyboardType: TextInputType.number,
                              maxLength: 6,
                              contentBuilder: (context) => TextField(
                                controller: codeController,
                                focusNode: codeFocusNode,
                                onChanged: (value) {
                                  _onCodeChanged();
                                },
                                keyboardType: TextInputType.number,
                                maxLength: 6,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(6),
                                ],
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  color: Colors.transparent,
                                ),
                                decoration: InputDecoration(
                                  counterText: '',
                                  border: InputBorder.none,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 15.w),
                  Text(
                    "enter_the_6_digit_code".tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                  SizedBox(height: 114.5.w),
                  Text(
                    "didnt_get_the_email".tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 9.5.w),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "check_your_spam_junk_folder_or".tr,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: customTheme.textColor,
                        ),
                      ),
                      GestureDetector(
                        onTap: sendCode,
                        child: RichText(
                          text: TextSpan(
                            text: 'resend_the_code'.tr,
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: customTheme.primaryColor,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 40.w),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
