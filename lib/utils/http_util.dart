// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:io';

import 'package:aurenixai_app/config/index.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/platform.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;

import 'local_util.dart';

bool isSuccess(Response<dynamic> response) {
  return response.statusCode == 200 && response.data['code'] == '200';
}

bool _isAuthError(String errorCode, String code) {
  if (code == '300') {
    return true;
  }
  switch (errorCode) {
    // token 错误
    case '300000':
    // token 过期
    case '300001':
    // token不能为空
    case '300002':
    // 访问/操作权限不足
    case '300003':
    // 用户认证失败
    case 'A50004':
    // 账号在别处登录
    case 'CORE00042':
      return true;
    default:
      return false;
  }
}

String getErrorMsg(Response<dynamic> response) {
  if (response.statusCode != 200) {
    return "Timeout, please try again".tr;
  }
  String errorCode = response.data['errorCode'];
  String code = response.data['code'];
  if (_isAuthError(errorCode, code)) {
    LocalUtil.removeUserToken();
    Get.offAllNamed(Routes.auth);
    EventBusUtil.fireUserLogout();
    // if (errorCode == '300003') {
    //   return '';
    // }
    if (errorCode == 'CORE00042') {
      // 单点登录
      return response.data['errorMsg'] ??
          "Login expired, please login again".tr;
    }
    return "Login expired, please login again".tr;
  }
  return response.data['errorMsg'] ?? "Timeout, please try again".tr;
}

class HttpUtil {
  late Dio _dio;
  HttpUtil._internal() {
    String baseUrl = AppConfig.apiUrl;
    BaseOptions options = BaseOptions(baseUrl: baseUrl);
    _dio = Dio(options);
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final String? token = await LocalUtil.getUserToken();
          final locale = await LocalUtil.getLocale();

          options.data ??= {};

          options.headers.addAll({
            'Authorization': token ?? " ",
            'content-Type': 'application/json',
            'Accept-Language': locale.toString(),
            'language': locale.toString(),
            'client': PlatformUtils().isIOS ? 'iOS' : 'android',
          });
          return handler.next(options);
        },
        onResponse: (response, handler) {
          // 打印出本次请求的curl
          if (!AppConfig.isBuildApk) {
            _printCurlCommand(response.requestOptions);
            print("------------response------------");
            print("data: ${jsonEncode(response.data)}");
          }
          if (isSuccess(response)) {
            return handler.next(response);
          }
          String errorMsg = getErrorMsg(response);
          return handler.reject(
            DioException(
              requestOptions: response.requestOptions,
              error: errorMsg,
              message: errorMsg,
              response: response,
            ),
          );
        },
        onError: (DioException e, handler) {
          if (!AppConfig.isBuildApk) {
            print("------------request error------------");
            print(e.message);
          }
          return handler.next(e);
        },
      ),
    );
  }

  static final HttpUtil _singleton = HttpUtil._internal();

  factory HttpUtil() => _singleton;

  /// 打印curl命令
  void _printCurlCommand(RequestOptions options) {
    StringBuffer curlCmd = StringBuffer();
    curlCmd.write('curl -X ${options.method}');

    // 添加headers
    options.headers.forEach((key, value) {
      curlCmd.write(' -H "$key: $value"');
    });

    // 添加数据
    if (options.data != null) {
      String dataStr;
      if (options.data is Map) {
        dataStr = jsonEncode(options.data);
      } else {
        dataStr = options.data.toString();
      }
      curlCmd.write(' -d \'$dataStr\'');
    }

    // 添加查询参数
    String url = '${options.baseUrl}${options.path}';
    if (options.queryParameters.isNotEmpty) {
      List<String> params = [];
      options.queryParameters.forEach((key, value) {
        params.add('$key=$value');
      });
      url += '?${params.join('&')}';
    }

    curlCmd.write(' "$url"');

    print("------------curl command------------");
    print(curlCmd.toString());
  }

  static Future<T> post<T>(String path, [Map<String, dynamic>? data]) async {
    return _singleton._dio
        .post(path, data: data ?? {})
        .then((res) {
          return res.data["data"] as T;
        })
        .catchError((e) {
          if ((e is DioException && e.error is SocketException) ||
              e is SocketException) {
            ToastUtil.showError('Timeout, please try again'.tr);
            throw e;
          }
          ToastUtil.showError(
            e.message ?? e.error ?? 'Timeout, please try again'.tr,
          );
          throw e;
        });
  }

  static Future<T> get<T>(String path, [Map<String, dynamic>? data]) async {
    return _singleton._dio
        .get(path, queryParameters: data ?? {})
        .then((res) {
          return res.data["data"] as T;
        })
        .catchError((e) {
          if ((e is DioException && e.error is SocketException) ||
              e is SocketException) {
            ToastUtil.showError('Timeout, please try again'.tr);
            throw e;
          }
          ToastUtil.showError(e.message ?? 'Timeout, please try again'.tr);
          throw e;
        });
  }
}
