import 'dart:convert';

import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/question_option_res.dart';
import 'package:aurenixai_app/models/question_res.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AnswerRes {
  final String questionId;
  final String optionTitle;
  final String extraContent;

  AnswerRes({
    required this.questionId,
    required this.optionTitle,
    required this.extraContent,
  });

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'optionTitle': optionTitle,
      'extraContent': extraContent,
    };
  }
}

class KycQuestionPage extends StatefulWidget {
  const KycQuestionPage({super.key});

  @override
  _KycQuestionPageState createState() => _KycQuestionPageState();
}

class _KycQuestionPageState extends State<KycQuestionPage> {
  List<QuestionRes> questions = [];

  @override
  void initState() {
    super.initState();
    getKycQuestions();
  }

  void getKycQuestions() async {
    try {
      ToastUtil.showLoading();
      final questions = await UserApi.getKycQuestions();
      setState(() {
        this.questions = questions;
      });
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  void onSubmit() async {
    List<AnswerRes> answers = [];
    for (var question in questions) {
      bool isSelected = false;
      for (var option in question.questions) {
        if (option.isSelected) {
          isSelected = true;
          answers.add(
            AnswerRes(
              questionId: question.id,
              optionTitle: option.title,
              extraContent: option.extraContent,
            ),
          );
        }
      }
      if (!isSelected) {
        ToastUtil.showToast('please_fill_in_the_complete'.tr);
        return;
      }
    }
    // 后退2页
    ToastUtil.showLoading();
    final parameters = Get.parameters;
    try {
      final params = {
        'type': parameters['type'],
        'fullName': parameters['fullName'],
        'interId': int.parse(parameters['interId'] ?? '0'),
        'idNO': parameters['idNO'],
        'mobile': parameters['mobile'],
        'questionsList': jsonEncode(answers.map((e) => e.toJson()).toList()),
      };
      if (parameters['type'] == '0') {
        params['frontPhoto'] = parameters['frontPhoto'];
        params['backPhoto'] = parameters['backPhoto'];
      } else {
        params['passportPhoto'] = parameters['passportPhoto'];
      }
      await UserApi.applyKyc(params);
      context.read<AuthProvider>().editIdentifyStatus('4');
      EventBusUtil.fireKycApply(const KycApplyEvent());
      ToastUtil.dismiss();
      Navigator.of(context).popUntil((route) {
        return !route.willHandlePopInternally &&
            route is ModalRoute &&
            !(route.settings.name?.contains(Routes.kycQuestion) ?? true) &&
            route.settings.name != Routes.kycVerify;
      });
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final iconButtonTheme = IconButtonTheme.of(context);
    final EdgeInsets padding =
        (iconButtonTheme.style?.padding as EdgeInsets?) ??
        EdgeInsets.symmetric(horizontal: 15.w);

    return Scaffold(
      appBar: AppBar(title: Text('additional_verification_questions'.tr)),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: padding.copyWith(bottom: 23.h),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: customTheme.dividerColor, width: 0.5),
              ),
            ),
            child: Text(
              'kyc_question_description'.tr,
              style: TextStyle(
                fontSize: 11.sp,
                color: customTheme.lightTextColor,
              ),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(left: 15.w, bottom: 20.h),
              child: Column(
                children: List.generate(
                  questions.length,
                  (index) => KycQuestionItem(question: questions[index]),
                ),
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'submit'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }
}

class KycQuestionItem extends StatefulWidget {
  final QuestionRes question;
  const KycQuestionItem({super.key, required this.question});

  @override
  State<KycQuestionItem> createState() => _KycQuestionItemState();
}

class _KycQuestionItemState extends State<KycQuestionItem> {
  final _extraController = TextEditingController();

  @override
  void initState() {
    super.initState();
    for (var option in widget.question.questions) {
      if (option.isSelected) {
        _extraController.text = option.extraContent;
        break;
      }
    }
  }

  @override
  void dispose() {
    _extraController.dispose();
    super.dispose();
  }

  QuestionOptionRes? get selectedOption {
    for (var option in widget.question.questions) {
      if (option.isSelected) {
        return option;
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: customTheme.dividerColor, width: 0.5),
        ),
      ),
      padding: EdgeInsets.only(right: 15.w, bottom: 7.5.h, top: 23.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.question.title,
            style: TextStyle(
              fontSize: 16.sp,
              color: customTheme.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w),
            child: Column(
              children: [
                if (widget.question.content != null)
                  Padding(
                    padding: EdgeInsets.only(top: 15.h),
                    child: Text(
                      widget.question.content!,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: customTheme.textColor,
                      ),
                    ),
                  ),
                SizedBox(height: 18.h),
                _buildOptions(),
                if (selectedOption?.needExtra ?? false)
                  Padding(
                    padding: EdgeInsets.only(bottom: 12.5.h),
                    child: NormalInput(
                      hintText: selectedOption?.extraPrompt ?? 'input...'.tr,
                      controller: _extraController,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      autoHeight: true,
                      minLines: 2,
                      maxLines: 3,
                      onChanged: (value) {
                        selectedOption?.extraContent = value;
                      },
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onSelect(int index) {
    for (int i = 0; i < widget.question.questions.length; i++) {
      if (i != index) {
        widget.question.questions[i].isSelected = false;
      }
    }
    _extraController.text = '';
    setState(() {});
  }

  void _onExtraChange(int index) {
    for (int i = 0; i < widget.question.questions.length; i++) {
      if (i != index) {
        widget.question.questions[i].extraContent = '';
      }
    }
    setState(() {});
  }

  Widget _buildOptions() {
    if (widget.question.type == '0') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(
          widget.question.questions.length,
          (index) => KycQuestionOption(
            option: widget.question.questions[index],
            onSelect: () {
              _onSelect(index);
            },
            onExtraChange: () {
              _onExtraChange(index);
            },
          ),
        ),
      );
    }
    return Wrap(
      spacing: 4.w,
      children: List.generate(
        widget.question.questions.length,
        (index) => SizedBox(
          width: 162.w,
          child: KycQuestionOption(
            option: widget.question.questions[index],
            onSelect: () {
              _onSelect(index);
            },
            onExtraChange: () {
              _onExtraChange(index);
            },
          ),
        ),
      ),
    );
  }
}

class KycQuestionOption extends StatefulWidget {
  final QuestionOptionRes option;
  final VoidCallback onSelect;
  final VoidCallback onExtraChange;

  const KycQuestionOption({
    super.key,
    required this.option,
    required this.onSelect,
    required this.onExtraChange,
  });

  @override
  _KycQuestionOptionState createState() => _KycQuestionOptionState();
}

class _KycQuestionOptionState extends State<KycQuestionOption> {
  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Padding(
      padding: EdgeInsets.only(bottom: 12.5.h),
      child: GestureDetector(
        onTap: () {
          setState(() {
            if (!widget.option.isSelected) {
              widget.option.isSelected = true;
              widget.onSelect();
            }
          });
        },
        child: Row(
          spacing: 10.5.w,
          children: [
            Image.asset(
              widget.option.isSelected
                  ? customThemeImage.kycSelect
                  : customThemeImage.kycUnselect,
              width: 16.w,
              height: 16.w,
              fit: BoxFit.fill,
            ),
            Expanded(
              child: Text(
                widget.option.title,
                style: TextStyle(
                  fontSize: 15.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
