import 'package:aurenixai_app/utils/theme.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider with ChangeNotifier {
  ThemeType _currentThemeType = ThemeType.dark;

  ThemeData _appTheme = ThemeUtil.defaultTheme[ThemeType.dark]!["app"];
  CustomThemeSwatch _customTheme =
      ThemeUtil.defaultTheme[ThemeType.dark]!["custom"];
  CustomThemeImage _customThemeImage =
      ThemeUtil.defaultTheme[ThemeType.dark]!["image"];

  ThemeData get appTheme {
    return _appTheme;
  }

  set appTheme(ThemeData theme) {
    _appTheme = theme;
    notifyListeners();
  }

  CustomThemeSwatch get customTheme {
    return _customTheme;
  }

  set customTheme(CustomThemeSwatch theme) {
    _customTheme = theme;
    notifyListeners();
  }

  CustomThemeImage get customThemeImage {
    return _customThemeImage;
  }

  set customThemeImage(CustomThemeImage theme) {
    _customThemeImage = theme;
    notifyListeners();
  }

  ThemeType get currentThemeType => _currentThemeType;

  set currentThemeType(ThemeType type) {
    _currentThemeType = type;
    Future<SharedPreferences> prefs = SharedPreferences.getInstance();
    prefs.then((prefs) {
      prefs.setString("themeType", type.toString());
    });
    _appTheme = ThemeUtil.defaultTheme[type]!["app"];
    _customTheme = ThemeUtil.defaultTheme[type]!["custom"];
    _customThemeImage = ThemeUtil.defaultTheme[type]!["image"];
    notifyListeners();
  }
}
