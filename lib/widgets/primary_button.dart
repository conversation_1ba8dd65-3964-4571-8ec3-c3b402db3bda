import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class PrimaryButton extends StatelessWidget {
  final VoidCallback? onPress;
  final String title;
  final EdgeInsetsGeometry? margin;
  final double? height;
  final double? borderRadius;
  final bool isInline;
  final Color? backgroundColor;
  final Color? color;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsetsGeometry? padding;
  final Widget? icon;
  final double? width;

  const PrimaryButton({
    super.key,
    this.onPress,
    required this.title,
    this.margin,
    this.height,
    this.borderRadius,
    this.isInline = false,
    this.backgroundColor,
    this.color,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.icon,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    return Container(
      height: height ?? 51.w,
      width: width ?? (!isInline ? double.infinity : null),
      margin: margin,
      child: ElevatedButton(
        onPressed: onPress,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? customTheme.buttonBgColor,
          padding: padding ?? EdgeInsets.zero,
          disabledForegroundColor: customTheme.buttonTextColor,
          disabledBackgroundColor: customTheme.buttonBgColor.withValues(
            alpha: 0.2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 3.w),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Offstage(offstage: icon == null, child: icon),
            Text(
              title,
              style: TextStyle(
                color: color ?? customTheme.buttonTextColor,
                fontSize: fontSize ?? 14.sp,
                fontWeight: fontWeight ?? FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
