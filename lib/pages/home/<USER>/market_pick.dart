import 'package:aurenixai_app/pages/home/<USER>/market_card.dart'
    show MarketCardItemModel;
import 'package:aurenixai_app/pages/home/<USER>/market_card_item.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class MarketPickModal extends StatefulWidget {
  const MarketPickModal({super.key, required this.currentList});
  final List<MarketCardItemModel> currentList;

  static Future<MarketCardItemModel?> showModal(
    BuildContext context, {
    required List<MarketCardItemModel> currentList,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<MarketCardItemModel?>(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          child: MarketPickModal(currentList: currentList),
        );
      },
    );
  }

  @override
  State<MarketPickModal> createState() => _MarketPickModalState();
}

class _MarketPickModalState extends State<MarketPickModal> {
  final List<MarketCardItemModel> _marketList = [
    MarketCardItemModel(
      name: 'Bitcoin',
      symbol: 'BTC/USD',
      typeName: 'Cryptocurrency',
    ),
    MarketCardItemModel(name: 'AAPL', symbol: 'AAPL', typeName: 'Stocks'),
  ];

  List<MarketCardItemModel> marketList = [];

  @override
  void initState() {
    super.initState();
    for (var item in _marketList) {
      if (!widget.currentList.any((element) => element.symbol == item.symbol)) {
        marketList.add(item);
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(top: 23.w, bottom: 21.w, right: 10.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Add Widget',
                style: TextStyle(
                  fontSize: 18.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                onPressed: () {
                  Get.back();
                },
                child: Icon(
                  Icons.close_rounded,
                  size: 26.w,
                  color: customTheme.textColor,
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(right: 15.w),
          child: Divider(height: 0.5, color: customTheme.dividerColor),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15.w),
      height: 330.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Padding(
            padding: EdgeInsets.only(right: 15.w, top: 18.w),
            child: GridView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true, // 让GridView自适应内容高度
              physics: NeverScrollableScrollPhysics(), // 禁止内部滚动，交给外层滚动
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 167 / 195,
                mainAxisSpacing: 11.w,
                crossAxisSpacing: 11.w,
              ),
              itemCount: marketList.length,
              itemBuilder: (context, index) {
                return MarketCardItem(
                  title: marketList[index].name,
                  typeName: marketList[index].typeName,
                  symbol: marketList[index].symbol,
                  isAdd: true,
                  onTap: () {
                    Get.back(result: marketList[index]);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
