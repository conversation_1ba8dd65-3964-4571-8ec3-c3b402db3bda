import 'dart:async' show StreamSubscription;

import 'package:aurenixai_app/config/index.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/pages/public/rich_text_config.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/widgets/invite_modal.dart';
import 'package:crisp_chat/crisp_chat.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/user_card.dart';

class ServicesPage extends StatefulWidget {
  const ServicesPage({super.key});

  @override
  _ServicesPageState createState() => _ServicesPageState();
}

class _ServicesPageState extends State<ServicesPage> {
  late StreamSubscription<BindGoogleSuccessEvent>
  _bindGoogleSuccessSubscription;
  late StreamSubscription<UnbindGoogleSuccessEvent>
  _unbindGoogleSuccessSubscription;

  bool showGoogleSuccess = false;
  final ScrollController _scrollController = ScrollController();
  double _appBarOpacity = 0.0;

  bool isOpening = false;

  @override
  void initState() {
    super.initState();
    _bindGoogleSuccessSubscription = EventBusUtil.listenBindGoogleSuccess((
      event,
    ) {
      setState(() {
        showGoogleSuccess = true;
      });
      Future.delayed(Duration(seconds: 2), () {
        setState(() {
          showGoogleSuccess = false;
        });
      });
    });
    _unbindGoogleSuccessSubscription = EventBusUtil.listenUnbindGoogleSuccess((
      event,
    ) {
      setState(() {
        showGoogleSuccess = false;
      });
    });

    // 监听滚动事件
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _bindGoogleSuccessSubscription.cancel();
    _unbindGoogleSuccessSubscription.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final scrollOffset = _scrollController.offset;
    final maxScrollOffset = 50.0; // 滚动100像素时完全透明
    final opacity = (scrollOffset / maxScrollOffset).clamp(0.0, 1.0);

    setState(() {
      _appBarOpacity = opacity;
    });
  }

  @override
  Widget build(BuildContext context) {
    final authInfo = context.watch<AuthProvider>();
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    final customThemeImage = Provider.of<ThemeProvider>(
      context,
    ).customThemeImage;

    // 使用工具类获取AppBar高度
    final appBarHeight = CommonUtils.getAppBarHeight(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: customTheme.pageBgColor.withValues(
          alpha: _appBarOpacity,
        ),
        elevation: _appBarOpacity > 0 ? 4 : 0,
        actions: [
          if ((authInfo.user?.nodeLevel ?? 1) >= 2)
            IconButton(
              onPressed: () {
                InviteModal.showModal(context: context);
              },
              icon: Image.asset(
                customThemeImage.homeShare,
                width: 25.w,
                height: 25.w,
                fit: BoxFit.fill,
              ),
            ),
          IconButton(
            onPressed: () {},
            icon: Image.asset(
              customThemeImage.servicesScan,
              width: 25.w,
              height: 25.w,
              fit: BoxFit.fill,
            ),
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              customThemeImage.authBgBottom,
              width: MediaQuery.of(context).size.width,
              height: 313.h,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
            ),
          ),
          Positioned.fill(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: EdgeInsets.only(bottom: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: appBarHeight),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        UserCard(),
                        SizedBox(height: 10.h),
                        _title('service'.tr),
                        SizedBox(height: 17.h),
                        Row(
                          spacing: 2.w,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _normalItem(
                              'wealth'.tr,
                              customThemeImage.tabsWealthIn,
                              () {
                                EventBusUtil.fireSwitchTab(
                                  SwitchTabEvent(tabIndex: 2),
                                );
                              },
                            ),
                            _normalItem(
                              'coupon'.tr,
                              customThemeImage.servicesCoupon,
                              () {
                                Get.toNamed(Routes.coupon);
                              },
                            ),
                          ],
                        ),
                        _title('setting'.tr),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 2.w,
                          children: [
                            _iconItem(
                              'personal_info'.tr,
                              customThemeImage.servicesPersonal,
                              () {
                                if (authInfo.isKyc) {
                                  Get.toNamed(Routes.personalInfo);
                                } else {
                                  Get.toNamed(Routes.kyc);
                                }
                              },
                              isWarn: !authInfo.isKyc,
                            ),
                            _iconItem(
                              'google_authenticator'.tr,
                              customThemeImage.servicesAuthenticator,
                              () {
                                Get.toNamed(Routes.google);
                              },
                              isWarn: false,
                              isSuccess: showGoogleSuccess,
                            ),
                          ],
                        ),
                        _title('support'.tr),
                        SizedBox(height: 17.h),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 2.w,
                          children: [
                            _normalItem(
                              'customer_service'.tr,
                              customThemeImage.servicesService,
                              () async {
                                if (isOpening) {
                                  return;
                                }
                                final userInfo = context
                                    .read<AuthProvider>()
                                    .user;
                                if (userInfo == null) {
                                  return;
                                }
                                try {
                                  final crispUser = User(
                                    email: userInfo.email,
                                    nickName: userInfo.email,
                                    phone: userInfo.mobile ?? '',
                                    avatar: userInfo.photo,
                                  );
                                  final _crispConfig = CrispConfig(
                                    websiteID: AppConfig.crispWebsiteID,
                                    user: crispUser,
                                    enableNotifications: false,
                                  );
                                  await FlutterCrispChat.openCrispChat(
                                    config: _crispConfig,
                                  );
                                } catch (e) {
                                } finally {
                                  isOpening = false;
                                }
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 19.5.h),
                  Divider(color: customTheme.dividerColor, height: 0.5),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 19.5.h,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 10.h,
                          children: [
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => RichTextConfig(
                                      title: 'terms_and_conditions'.tr,
                                      configKey: 'terms_condition',
                                      configType: 'system',
                                    ),
                                  ),
                                );
                              },
                              child: Text(
                                'terms_and_conditions'.tr,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: customTheme.subTextColor,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => RichTextConfig(
                                      title: 'privacy_policy'.tr,
                                      configKey: 'privacy_policy',
                                      configType: 'system',
                                    ),
                                  ),
                                );
                              },
                              child: Text(
                                'privacy_policy'.tr,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: customTheme.subTextColor,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => RichTextConfig(
                                      title: 'faqs'.tr,
                                      configKey: 'FAQs',
                                      configType: 'system',
                                    ),
                                  ),
                                );
                              },
                              child: Text(
                                'faqs'.tr,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: customTheme.subTextColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          spacing: 5.h,
                          children: [
                            Container(
                              margin: EdgeInsets.only(top: 11.h),
                              height: 37.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3.w),
                                border: Border.all(
                                  color: customTheme.buttonBgColor,
                                ),
                              ),
                              clipBehavior: Clip.hardEdge,
                              child: TextButton(
                                onPressed: () {
                                  context.read<AuthProvider>().logout();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 20.w,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(3.w),
                                  ),
                                ),
                                child: Text(
                                  'log_out'.tr,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: customTheme.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                            Text(
                              '${'app_version'.tr} 1.0.0',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: customTheme.hintTextColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _normalItem(String title, String image, VoidCallback onTap) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    return Column(
      spacing: 6.h,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.5.w),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(11.w),
            child: Container(
              width: 45.w,
              height: 45.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(11.w),
                border: Border.all(color: customTheme.ff696978, width: 0.5),
              ),
              alignment: Alignment.center,
              child: Image.asset(
                image,
                width: 25.w,
                height: 25.w,
                fit: BoxFit.fill,
              ),
            ),
          ),
        ),
        Text(
          title,
          style: TextStyle(fontSize: 12.sp, color: customTheme.subTextColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _iconItem(
    String title,
    String image,
    VoidCallback onTap, {
    bool isWarn = false,
    bool isSuccess = false,
  }) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    final customThemeImage = Provider.of<ThemeProvider>(
      context,
    ).customThemeImage;

    return Column(
      spacing: 6.h,
      children: [
        Stack(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 20.5.w,
              ).copyWith(top: 19.5.h),
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(11.w),
                child: Container(
                  width: 45.w,
                  height: 45.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(11.w),
                    border: Border.all(color: customTheme.ff696978, width: 0.5),
                  ),
                  alignment: Alignment.center,
                  child: Image.asset(
                    image,
                    width: 25.w,
                    height: 25.w,
                    fit: BoxFit.fill,
                  ),
                ),
              ),
            ),
            if (isWarn || isSuccess)
              Positioned(
                left: 48.w,
                top: 0,
                child: Container(
                  height: 31.w,
                  padding: EdgeInsets.symmetric(horizontal: 5.5.w),
                  decoration: BoxDecoration(
                    color: customTheme.inputBgColor,
                    borderRadius: BorderRadius.circular(10.w),
                  ),
                  child: Image.asset(
                    isSuccess
                        ? customThemeImage.googleSuccess
                        : customThemeImage.homeWarn,
                    width: 18.w,
                    height: 18.w,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
          ],
        ),
        Text(
          title,
          style: TextStyle(fontSize: 12.sp, color: customTheme.subTextColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _title(String title) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
