import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/sms_res.dart';
import 'package:aurenixai_app/pages/home/<USER>/widgets/date_pick.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showUnread = false;
  DateTime? _selectedDate;
  List<SmsRes> notices = [];
  int _noticePageNum = 1;
  bool _noticeIsEnd = false;
  late EasyRefreshController _noticesController;
  List<SmsRes> transactions = [];
  int _transactionPageNum = 1;
  bool _transactionIsEnd = false;
  late EasyRefreshController _transactionsController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _noticesController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    _transactionsController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _noticesController.dispose();
    _transactionsController.dispose();
    super.dispose();
  }

  Future<void> _onRefreshNotices() async {
    try {
      _noticePageNum = 1;
      final res = await CommonApi.getNoticeList({
        'pageNum': _noticePageNum,
        'pageSize': 10,
        'isReadFlag': _showUnread ? '0' : '',
        'date': _selectedDate?.yyyyMM,
      });
      _noticePageNum++;
      notices = res.list;
      _noticeIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _noticesController.finishRefresh();
    if (_noticeIsEnd) {
      _noticesController.finishLoad(IndicatorResult.noMore);
    } else {
      _noticesController.resetFooter();
    }
  }

  Future<void> _onLoadMoreNotices() async {
    try {
      final res = await CommonApi.getNoticeList({
        'pageNum': _noticePageNum,
        'pageSize': 10,
      });
      _noticePageNum++;
      notices.addAll(res.list);
      _noticeIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _noticesController.finishLoad(
      _noticeIsEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  Future<void> _onRefreshTransactions() async {
    try {
      _transactionPageNum = 1;
      final res = await CommonApi.getTradeNoticeList({
        'pageNum': _transactionPageNum,
        'pageSize': 10,
        'isReadFlag': _showUnread ? '0' : '',
        'date': _selectedDate?.yyyyMM,
      });
      _transactionPageNum++;
      transactions = res.list;
      _transactionIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _transactionsController.finishRefresh();
    if (_transactionIsEnd) {
      _transactionsController.finishLoad(IndicatorResult.noMore);
    } else {
      _transactionsController.resetFooter();
    }
  }

  Future<void> _onLoadMoreTransactions() async {
    try {
      final res = await CommonApi.getTradeNoticeList({
        'pageNum': _transactionPageNum,
        'pageSize': 10,
      });
      _transactionPageNum++;
      transactions.addAll(res.list);
      _transactionIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _transactionsController.finishLoad(
      _transactionIsEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('notification'.tr)),
      body: Column(
        children: [
          _tabs(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                NoticesTab(
                  items: notices,
                  controller: _noticesController,
                  onRefresh: _onRefreshNotices,
                  onLoadMore: _onLoadMoreNotices,
                  isEnd: _noticeIsEnd,
                  itemBuilder: (index) => _item(notices[index], () {
                    setState(() {
                      notices[index].isRead = '1';
                    });
                  }),
                ),
                NoticesTab(
                  items: transactions,
                  controller: _transactionsController,
                  onRefresh: _onRefreshTransactions,
                  onLoadMore: _onLoadMoreTransactions,
                  isEnd: _transactionIsEnd,
                  itemBuilder: (index) => _item(transactions[index], () {
                    setState(() {
                      transactions[index].isRead = '1';
                    });
                  }),
                ),
              ],
            ),
          ),
          WidgetUtil.getBottomWrapper(
            context: context,
            color: customTheme.inputBgColor,
            child: Row(
              spacing: 11.w,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: OutlineButton(
                    title: 'show_unread'.tr,
                    icon: Padding(
                      padding: EdgeInsets.only(right: 6.w),
                      child: Image.asset(
                        customThemeImage.homeEnvelope,
                        width: 16.w,
                        height: 16.w,
                      ),
                    ),
                    height: 45.w,
                    onPress: () {
                      setState(() {
                        _showUnread = !_showUnread;
                      });
                      _onRefreshNotices();
                      _onRefreshTransactions();
                    },
                  ),
                ),
                Expanded(
                  child: PrimaryButton(
                    title: _selectedDate != null
                        ? _selectedDate!.yMMMM
                        : 'filter_date'.tr,
                    icon: Padding(
                      padding: EdgeInsets.only(right: 6.w),
                      child: Image.asset(
                        customThemeImage.homeCalendar,
                        width: 16.w,
                        height: 16.w,
                      ),
                    ),
                    height: 45.w,
                    onPress: () async {
                      try {
                        final date = await DatePickModal.showModal(
                          context,
                          initialDate: _selectedDate,
                        );
                        if (date != null) {
                          setState(() {
                            _selectedDate = date;
                          });
                          _onRefreshNotices();
                          _onRefreshTransactions();
                        }
                      } catch (e) {}
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _item(SmsRes item, VoidCallback onRead) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return InkWell(
      onTap: () {
        Get.toNamed(
          Routes.notificationDetail,
          parameters: {
            'id': item.id,
            'title': item.title,
            'date': item.updateDatetime.toString(),
          },
        );
        onRead();
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: customTheme.dividerColor, width: 0.5),
            ),
          ),
          padding: EdgeInsets.symmetric(vertical: 22.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.title,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: item.isRead == '0'
                      ? customTheme.textColor
                      : customTheme.hintTextColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 10.5.w),
              Text(
                item.content,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: item.isRead == '0'
                      ? customTheme.subTextColor
                      : customTheme.hintTextColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _tabs() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    // 获取当前文字方向，如果是从右到左，则需要将TabBar的labelPadding设置为0
    final textDirection = Directionality.of(context);
    final labelPadding = textDirection == TextDirection.rtl
        ? EdgeInsets.only(left: 54.w)
        : EdgeInsets.only(right: 54.w);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: TabBar(
        controller: _tabController,
        indicatorWeight: 3.w,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 6.w),
        isScrollable: true,
        dividerHeight: 0.5,
        tabAlignment: TabAlignment.start,
        dividerColor: customTheme.dividerColor,
        labelPadding: labelPadding,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.lightTextColor,
          fontWeight: FontWeight.w600,
        ),
        tabs: [
          Tab(child: Text('notices'.tr)),
          Tab(child: Text('transaction'.tr)),
        ],
      ),
    );
  }
}

class NoticesTab extends StatefulWidget {
  final List<SmsRes> items;
  final EasyRefreshController controller;
  final Future<void> Function() onRefresh;
  final Future<void> Function() onLoadMore;
  final bool isEnd;
  final Widget Function(int) itemBuilder;

  const NoticesTab({
    required this.items,
    required this.controller,
    required this.onRefresh,
    required this.onLoadMore,
    required this.isEnd,
    required this.itemBuilder,
    super.key,
  });
  @override
  State<NoticesTab> createState() => _NoticesTabState();
}

class _NoticesTabState extends State<NoticesTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return EasyRefresh(
      controller: widget.controller,
      onRefresh: widget.onRefresh,
      onLoad: widget.onLoadMore,
      refreshOnStart: true,
      header: WidgetUtil.getRefreshOnStartHeader(context),
      child: widget.isEnd && widget.items.isEmpty
          ? _empty()
          : ListView.builder(
              itemCount: widget.items.length,
              itemBuilder: (context, index) {
                return widget.itemBuilder(index);
              },
            ),
    );
  }

  Widget _empty() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 40.h),
      child: Column(
        spacing: 13.5.w,
        children: [
          SizedBox(height: 100.h),
          Center(
            child: Image.asset(
              customThemeImage.commonEmpty,
              width: 80.w,
              height: 80.w,
            ),
          ),
          Text(
            'no_record_found'.tr,
            style: TextStyle(fontSize: 13.sp, color: customTheme.subTextColor),
          ),
        ],
      ),
    );
  }
}
