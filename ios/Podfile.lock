PODS:
  - audio_session (0.0.1):
    - Flutter
  - <PERSON>risp (2.8.2):
    - Crisp/Crisp (= 2.8.2)
  - Crisp/Crisp (2.8.2)
  - crisp_chat (2.3.0):
    - Crisp (~> 2.8.2)
    - Flutter
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - recaptcha_enterprise_flutter (18.7.1):
    - Flutter
    - RecaptchaEnterprise (<= 18.7.1, >= 18.7.0)
  - RecaptchaEnterprise (18.7.0):
    - RecaptchaEnterpriseSDK (= 18.7.0)
    - RecaptchaInterop (~> 101.0.0)
  - RecaptchaEnterpriseSDK (18.7.0)
  - RecaptchaInterop (101.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - crisp_chat (from `.symlinks/plugins/crisp_chat/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - recaptcha_enterprise_flutter (from `.symlinks/plugins/recaptcha_enterprise_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Crisp
    - RecaptchaEnterprise
    - RecaptchaEnterpriseSDK
    - RecaptchaInterop

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  crisp_chat:
    :path: ".symlinks/plugins/crisp_chat/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  recaptcha_enterprise_flutter:
    :path: ".symlinks/plugins/recaptcha_enterprise_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  Crisp: 6747c96b2b2c2a81babf1eaecd1688a65d98edd4
  crisp_chat: 7cfa883efa81a122430f3605a7d8b44be3db8a60
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  recaptcha_enterprise_flutter: 00b38414c923503e20fecd7170e6b2c4094265b6
  RecaptchaEnterprise: a5914ad9db6ff79eaca7258d82ecef6f10009936
  RecaptchaEnterpriseSDK: 5e72d7f19d2f679b68c25071c33b40f817e21149
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 2952b950c3eeb57764c593c119d909d2e91e6b52

COCOAPODS: 1.16.2
