import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  _SignInState createState() => _SignInState();
}

class _SignInState extends State<SignInPage> {
  bool isPasswordVisible = false;
  bool isRememberMe = false;
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    LocalUtil.getLoginNameAndPwd().then((value) {
      if (value != null) {
        emailController.text = value['loginName'] ?? '';
        passwordController.text = value['pwd'] ?? '';
        setState(() {
          isRememberMe = true;
        });
      }
    });
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    try {
      if (emailController.text.isEmpty) {
        ToastUtil.showToast('please_enter_your_email'.tr);
        return;
      }
      if (passwordController.text.isEmpty) {
        ToastUtil.showToast('please_enter_your_password'.tr);
        return;
      }
      ToastUtil.showLoading();
      final res = await UserApi.login(
        loginName: emailController.text,
        loginPwd: passwordController.text,
      );
      await context.read<AuthProvider>().setToken(res.token);
      if (isRememberMe) {
        await LocalUtil.setLoginNameAndPwd(
          emailController.text,
          passwordController.text,
        );
      } else {
        await LocalUtil.removeLoginNameAndPwd();
      }
      Get.offAllNamed(Routes.home, parameters: {"tabIndex": "0"});
    } catch (e) {
    } finally {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customeThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;

    // 使用工具类获取AppBar高度
    final appBarHeight = CommonUtils.getAppBarHeight(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text('sign_in_s'.tr),
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              customeThemeImage.authBgBottom,
              width: MediaQuery.of(context).size.width,
              height: 313.h,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
            ),
          ),
          Positioned.fill(
            left: 15.w,
            right: 15.w,
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 使用工具类获取的高度
                  SizedBox(height: appBarHeight + 20.5.h),
                  Image.asset(customeThemeImage.authSignInLogo, width: 232.w),
                  SizedBox(height: 76.5.h),
                  Text(
                    'welcome_back'.tr,
                    style: TextStyle(
                      fontSize: 22.sp,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 27.h),
                  NormalInput(
                    label: 'email'.tr,
                    hintText: 'enter_your_email'.tr,
                    controller: emailController,
                  ),
                  SizedBox(height: 27.h),
                  NormalInput(
                    label: 'password'.tr,
                    hintText: 'enter_your_password'.tr,
                    controller: passwordController,
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: !isPasswordVisible,
                    padding: EdgeInsets.zero,
                    contentPadding: EdgeInsets.symmetric(horizontal: 19.w),
                    suffix: GestureDetector(
                      onTap: () {
                        setState(() {
                          isPasswordVisible = !isPasswordVisible;
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.only(right: 12.w),
                        child: Icon(
                          isPasswordVisible
                              ? Icons.visibility
                              : Icons.visibility_off,
                          size: 20.w,
                          color: customTheme.textColor,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 26.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            isRememberMe = !isRememberMe;
                          });
                        },
                        child: Wrap(
                          spacing: 7.w,
                          children: [
                            Image.asset(
                              isRememberMe
                                  ? customeThemeImage.authChoose
                                  : customeThemeImage.authUnchoose,
                              width: 18.w,
                            ),
                            Text(
                              'remember_me'.tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: customTheme.textColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.toNamed(Routes.resetPassword);
                        },
                        child: RichText(
                          text: TextSpan(
                            text: 'forgot_password'.tr,
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: customTheme.primaryColor,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 103.h),
                  PrimaryButton(title: 'sign_in'.tr, onPress: onSubmit),
                  SizedBox(height: 27.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 3,
                    children: [
                      Text(
                        "dont_have_an_account".tr,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: customTheme.textColor,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.toNamed(Routes.signUp);
                        },
                        child: RichText(
                          text: TextSpan(
                            text: 'create_account'.tr,
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: customTheme.primaryColor,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 40.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
