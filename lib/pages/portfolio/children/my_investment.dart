import 'dart:async' show StreamSubscription;

import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/models/fund_investment_record_res.dart';
import 'package:aurenixai_app/pages/portfolio/children/investment_detail.dart';
import 'package:aurenixai_app/pages/portfolio/children/redeem_amount.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/investment_tip.dart';

class MyInvestmentPage extends StatefulWidget {
  const MyInvestmentPage({super.key});

  @override
  State<MyInvestmentPage> createState() => _MyInvestmentPageState();
}

class _MyInvestmentPageState extends State<MyInvestmentPage> {
  late StreamSubscription<RedeemApplyEvent> redeemApplySubscription;
  late EasyRefreshController _controller;
  List<FundInvestmentRecordRes> investmentList = [];
  int _pageNum = 1;
  bool _isEnd = false;
  String productName = '';
  String productId = '';

  @override
  void initState() {
    super.initState();
    productName = Get.parameters['name'] ?? '';
    productId = Get.parameters['productId'] ?? '';
    redeemApplySubscription = EventBusUtil.listenRedeemApply((event) {
      _onRefresh(pageSize: investmentList.isEmpty ? 10 : investmentList.length);
    });
    _controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    redeemApplySubscription.cancel();
    _controller.dispose();
    super.dispose();
  }

  Future<void> _onRefresh({int pageSize = 10}) async {
    try {
      _pageNum = 1;
      final res = await InvestmentApi.getFundInvestmentDetailRecordList({
        'pageNum': _pageNum,
        'pageSize': pageSize,
        'productId': productId,
      });
      _pageNum++;
      investmentList = res.list;
      _isEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _controller.finishRefresh();
    if (_isEnd) {
      _controller.finishLoad(IndicatorResult.noMore);
    } else {
      _controller.resetFooter();
    }
  }

  Future<void> _onLoadMore() async {
    try {
      final res = await InvestmentApi.getFundInvestmentDetailRecordList({
        'pageNum': _pageNum,
        'pageSize': 10,
      });
      _pageNum++;
      investmentList.addAll(res.list);
      _isEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _controller.finishLoad(
      _isEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  @override
  Widget build(BuildContext context) {
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(
        title: Text('Subscribed Strategies'),
        actions: [
          IconButton(
            onPressed: () {
              InvestmentTip.showModal(context: context);
            },
            icon: Image.asset(
              customThemeImage.portfolioTip,
              width: 22.w,
              height: 22.w,
            ),
          ),
        ],
      ),
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        onLoad: _onLoadMore,
        refreshOnStart: true,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: ListView.separated(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          itemBuilder: (context, index) {
            return InvestmentItem(
              item: investmentList[index],
              productName: productName,
            );
          },
          separatorBuilder: (context, index) {
            return SizedBox(height: 20.h);
          },
          itemCount: investmentList.length,
        ),
      ),
    );
  }
}

class InvestmentItem extends StatefulWidget {
  const InvestmentItem({
    super.key,
    required this.item,
    required this.productName,
  });

  final FundInvestmentRecordRes item;
  final String productName;

  @override
  State<InvestmentItem> createState() => _InvestmentItemState();
}

class _InvestmentItemState extends State<InvestmentItem> {
  modifyReallocate() async {
    try {
      ToastUtil.showLoading();
      await InvestmentApi.modifyReinvest(
        widget.item.id,
        widget.item.isReinvestEnabled == '1' ? '0' : '1',
      );
      setState(() {
        widget.item.isReinvestEnabled = widget.item.isReinvestEnabled == '1'
            ? '0'
            : '1';
      });
      ToastUtil.dismiss();
    } catch (e) {
      e.printError();
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    final initialFundingDate = DateTime.fromMillisecondsSinceEpoch(
      widget.item.fundingDate.toInt(),
    );

    return Container(
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(3.r),
      ),
      padding: EdgeInsets.symmetric(vertical: 17.h),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.5.w),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    Get.to(
                      () => InvestmentDetailPage(
                        investment: widget.item,
                        productName: widget.productName,
                      ),
                    );
                  },
                  child: Row(
                    spacing: 9.w,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          widget.productName,
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: customTheme.textColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 3.5.h),
                        child: Image.asset(
                          customThemeImage.portfolioArrowRight,
                          width: 16.w,
                          height: 16.w,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 11.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  spacing: 4.w,
                  children: [
                    Expanded(
                      child: Text(
                        '${'lock_in_period'.tr}: ${widget.item.lockinPeriodDays} ${widget.item.lockinPeriodDays > 1 ? 'days'.tr : 'day'.tr}',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: customTheme.subTextColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        '${'redemption_fees'.tr}: ${widget.item.fee}%',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: customTheme.subTextColor,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 15.h),
                Divider(height: 0.5),
              ],
            ),
          ),
          SizedBox(height: 12.h),
          Padding(
            padding: EdgeInsets.only(left: 16.5.w),
            child: Row(
              spacing: 4.w,
              children: [
                Expanded(
                  child: Text(
                    '${'initial_funding_date'.tr}: ${initialFundingDate.yyyyMMdd}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                ),
                Container(
                  height: 16.h,
                  padding: EdgeInsets.only(left: 5.w, right: 3.5.w),
                  decoration: BoxDecoration(
                    color: customTheme.primaryColor,
                    borderRadius: BorderRadius.horizontal(
                      left: Radius.circular(9.5.r),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    widget.item.status == '0'
                        ? 'active'.tr
                        : widget.item.status == '1'
                        ? 'In-progress'.tr
                        : 'redeemed'.tr,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16.5.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.5.w),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 4.h,
                        children: [
                          Text(
                            'initial_amount_allocated'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: customTheme.lightTextColor,
                            ),
                          ),
                          Text(
                            '\$${widget.item.initialAmount.toString().fmt}',
                            style: TextStyle(
                              fontSize: 19.sp,
                              color:
                                  customTheme.textColor, // downColor 免除30%赎回时
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        spacing: 4.h,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'total_amount_allocated'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: customTheme.lightTextColor,
                            ),
                            textAlign: TextAlign.right,
                          ),
                          Text(
                            '\$${widget.item.totalInvested.toString().fmt}',
                            style: TextStyle(
                              fontSize: 19.sp,
                              color: customTheme.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 18.h),
                _line(
                  'total_returns_to_date'.tr,
                  '\$${widget.item.totalReturns.toString().fmt}',
                  context: context,
                ),
                _line(
                  'returns_credited_to_funding_account'.tr,
                  '\$${widget.item.returnsToWallet.toString().fmt}',
                  context: context,
                ),
                _line(
                  'returns_auto_reallocated'.tr,
                  '\$${widget.item.returnsReinvested.toString().fmt}',
                  context: context,
                ),
                _line(
                  'total_amount_redeemed'.tr,
                  '\$${widget.item.totalRedeemed.toString().fmt}',
                  context: context,
                ),
                if (widget.item.stopLossEvent > 0)
                  _line(
                    'stop_loss_event'.tr,
                    '\$${widget.item.stopLossEvent.toString().fmt}',
                    context: context,
                  ),
                _line(
                  'current_allocated_balance'.tr,
                  '\$${widget.item.currentBalance.toString().fmt}',
                  context: context,
                  highlight: widget.item.stopLossEvent > 0 ? false : true,
                  isWarn: widget.item.stopLossEvent > 0,
                ),
                SizedBox(height: 6.5.h),
                Divider(height: 0.5),
                SizedBox(height: 13.h),
                _line(
                  'redemption_period'.tr,
                  '${widget.item.redemptionPeriodDays} ${widget.item.redemptionPeriodDays > 1 ? 'days'.tr : 'day'.tr}',
                  context: context,
                ),
                if (widget.item.canRedeem == '1' ||
                    widget.item.canReinvest == '1')
                  Padding(
                    padding: EdgeInsets.only(top: 14.h),
                    child: Row(
                      spacing: 8.w,
                      children: [
                        if (widget.item.canReinvest == '1')
                          Expanded(
                            child: OutlineButton(
                              title: widget.item.isReinvestEnabled == '1'
                                  ? 'cancel_reallocate'.tr
                                  : 'auto_reallocate'.tr,
                              height: 39.h,
                              borderRadius: 9.r,
                              fontSize: 13.sp,
                              onPress: modifyReallocate,
                            ),
                          ),
                        if (widget.item.canRedeem == '1')
                          Expanded(
                            child: PrimaryButton(
                              title: 'Redeem'.tr,
                              height: 39.h,
                              borderRadius: 9.r,
                              fontSize: 13.sp,
                              onPress: () {
                                Get.to(
                                  () => RedeemAmountPage(
                                    investment: widget.item,
                                    productName: widget.productName,
                                  ),
                                );
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                // TODO: 需要判断是否开启查看合同的权限
                Padding(
                  padding: EdgeInsets.only(top: 13.h),
                  child: GestureDetector(
                    onTap: () {},
                    child: Text(
                      'subscription_agreement'.tr,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                        decorationColor: customTheme.primaryColor,
                        decorationThickness: 1.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _line(
    String title,
    String value, {
    required BuildContext context,
    bool highlight = false,
    bool isWarn = false,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        spacing: 4.w,
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                color: customTheme.lightTextColor,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13.sp,
              color: highlight
                  ? customTheme.primaryColor
                  : isWarn
                  ? customTheme.downColor
                  : customTheme.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
