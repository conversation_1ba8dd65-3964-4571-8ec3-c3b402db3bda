// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_rules_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WithdrawRulesRes _$WithdrawRulesResFromJson(Map<String, dynamic> json) =>
    WithdrawRulesRes()
      ..id = json['id'] as String
      ..symbol = json['symbol'] as String
      ..pic = json['pic'] as String
      ..network = json['network'] as String?
      ..type = json['type'] as String
      ..withdrawMin = json['withdrawMin'] as num
      ..withdrawFeeType = json['withdrawFeeType'] as String
      ..withdrawFeeTakeLocation = json['withdrawFeeTakeLocation'] as String
      ..withdrawFee = json['withdrawFee'] as num
      ..withdrawFeeMin = json['withdrawFeeMin'] as num
      ..withdrawFeeMax = json['withdrawFeeMax'] as num
      ..withdrawStep = json['withdrawStep'] as num
      ..withdrawLimit = json['withdrawLimit'] as num
      ..withdrawRule = json['withdrawRule'] as String;

Map<String, dynamic> _$WithdrawRulesResToJson(WithdrawRulesRes instance) =>
    <String, dynamic>{
      'id': instance.id,
      'symbol': instance.symbol,
      'pic': instance.pic,
      'network': instance.network,
      'type': instance.type,
      'withdrawMin': instance.withdrawMin,
      'withdrawFeeType': instance.withdrawFeeType,
      'withdrawFeeTakeLocation': instance.withdrawFeeTakeLocation,
      'withdrawFee': instance.withdrawFee,
      'withdrawFeeMin': instance.withdrawFeeMin,
      'withdrawFeeMax': instance.withdrawFeeMax,
      'withdrawStep': instance.withdrawStep,
      'withdrawLimit': instance.withdrawLimit,
      'withdrawRule': instance.withdrawRule,
    };
