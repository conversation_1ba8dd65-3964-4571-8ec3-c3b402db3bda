import 'regions/en_us.dart';

const enUS = {
  ...enUSRegions,

  /// easy_refresh
  'pull_to_refresh': 'Pull to refresh',
  'release_ready': 'Release ready',
  'refreshing': 'Refreshing...',
  'succeeded': 'Succeeded',
  'no_more': 'No more',
  'failed': 'Failed',
  'pull_to_load': 'Pull to load',
  'last_updated_at': 'Last updated at %T',
  'loading': 'Loading...',

  /// http_util
  'timeout_please_try_again': 'Timeout, please try again',
  'login_expired_please_login_again': 'Login expired, please login again',

  /// theme_pick
  'theme': 'Theme',
  'logout': 'Logout',

  /// language_pick
  'country_region': 'Country & Region',
  'language': 'Language',

  // image_picker
  'take_photo': 'Take Photo',
  'album': 'Album',
  'upload_failed_please_try_again': 'Upload failed, please try again',
  'image_size_cannot_exceed_10m': 'Image size cannot exceed 10M',
  'image_uploading': 'Image uploading...',

  /// common
  'please_select': 'Please select',

  /// tabs
  'home': 'Home',
  'portfolio': 'Portfolio',
  'wealth': 'Wealth',
  'account': 'Account',
  'services': 'Services',

  /// auth
  'create_your_account': 'Create Your Account',
  'create_your_account_s': 'Create your account',
  'sign_in': 'Sign In',
  'sign_in_s': 'Sign in',
  'welcome_back': 'Welcome Back!',
  'please_enter_your_email': 'Please enter your email',
  'please_enter_your_password': 'Please enter your password',
  'enter_your_email': 'Enter your email',
  'enter_your_password': 'Enter your password',
  'remember_me': 'Remember Me',
  'email': 'Email',
  'password': 'Password',
  'forgot_password': 'Forgot Password?',
  'dont_have_an_account': "Don't have an account?",
  'create_account': 'Create account',
  'please_enter_a_valid_email': 'Please enter a valid email',
  'password_must_be_at_least_8_characters':
      'Password must be at least 8 characters',
  'please_agree_to_the_terms_and_conditions_and_privacy_policy':
      'Please agree to the terms & conditions and privacy policy',
  'welcome_to': 'Welcome to',
  'create_a_secure_password': 'Create a secure password',
  'weak': 'Weak',
  'medium': 'Medium',
  'strong': 'Strong',
  'invitation_code': 'Invitation code',
  'enter_your_invitation_code': 'Enter your invitation code',
  'by_proceeding_you_acknowledge_and_agree_to_the':
      'By proceeding, you acknowledge and agree to the',
  'terms_and_conditions': 'Terms & Conditions',
  'and': 'and',
  'privacy_policy': 'Privacy Policy',
  'already_have_an_account': 'Already have an account?',
  'verify_your_email': 'Verify Your Email',
  'we_ve_sent_a_verification_code_to_your_email_address_enter_the_code_below_to_verify_your_account_and_complete_your_sign_up':
      "We've sent a verification code to your email address. Enter the code below to verify your account and complete your sign-up.",
  'enter_the_6_digit_code': 'Enter the 6-digit code',
  'didnt_get_the_email': "Didn't get the email?",
  'check_your_spam_junk_folder_or': 'Check your spam/junk folder or ',
  'resend_the_code': 'resend the code',
  'account_created_successfully': 'Account created successfully!',
  'you_re_all_set_just_one_more_step_complete_your_identity_verification_kyc_to_access_all_features_and_keep_your_account_secure':
      "You're all set! Just one more step — complete your identity verification (KYC) to access all features and keep your account secure.",
  'start_verification': 'Start Verification',
  'skip_for_now': 'Skip for now',
  'reset_your_password': 'Reset your password',
  'enter_your_email_address_and_we_ll_send_you_a_password_reset_link':
      "Enter your email address and we'll send you a password reset link",
  'i_m_not_a_robot': "I'm not a robot",
  'submit': 'Submit',
  'a_password_reset_link_has_been_sent_to_your_email_please_check_your_inbox':
      "A password reset link has been sent to your email. Please check your inbox.",
  'verify': 'Verify',
  'passed': 'Passed',

  /// kyc
  "kyc": "KYC",
  "kyc_fail": "KYC Fail",
  "kyc_in_progress": "KYC In-progress",
  'kyc_verification': 'KYC Verification',
  "kyc_review_tip":
      "We are performing additional compliance checks. This may take up to 24 hours. You'll be notified once it's completed.",
  'kyc_verification_required': 'Identity Verification Required',
  "kyc_verification_required_description":
      "To protect your account and comply with financial regulations, please complete identity verification (KYC).",
  "kyc_verification_required_description_2":
      "This quick and secure process helps unlock full access to your account and ensures continued use of key features.",
  "kyc_note": "Note:",
  "kyc_note_description":
      "Your information is securely encrypted and used only for regulatory compliance purposes.",
  "verify_now": "Verify Now",
  "learn_more_about_aml": "Learn more about AML",
  "verify_your_identity": "Verify your identity",
  "kyc_verify_description":
      "To keep your account secure and comply with financial regulations, we need to verify your identity.",
  "kyc_verify_description_2": "This process only takes a few minutes.",
  "full_name": "Full name",
  "enter_your_full_name": "Enter your full name",
  "please_enter_your_full_name": "Please enter your full name",
  "phone_number": "Phone number",
  "enter_your_phone_number": "Enter your phone number",
  "please_enter_your_phone_number": "Please enter your phone number",
  "id_type": "ID type",
  "id_card": "ID Card",
  "passport": "Passport",
  "select_your_id_type": "Select your ID type",
  "id_document_number": "ID document number",
  "enter_your_id_document_number": "Enter your ID document number",
  "please_enter_your_id_document_number":
      "Please enter your ID document number",
  "upload_photos": "Upload photos",
  "front": "Front",
  "back": "Back",
  "please_upload_your_front_photo": "Please upload your front photo",
  "please_upload_your_back_photo": "Please upload your back photo",
  "please_upload_your_passport_photo": "Please upload your passport photo",
  "continue": "Continue",
  "additional_verification_questions": "Additional Verification Questions",
  "kyc_question_description":
      "Required to comply with global anti-money laundering regulations",
  "please_fill_in_the_complete": "Please fill in the complete",
  "what_is_aml": "What is AML? (Anti-Money Laundering)",
  "what_is_aml_description":
      "Anti-Money Laundering (AML) refers to laws and procedures designed to prevent criminals from disguising illegally obtained funds as legitimate income.",
  "why_do_we_ask_these_questions": "Why do we ask these questions?",
  "why_do_we_ask_these_questions_description":
      "To comply with global financial regulations and protect all users, we're required to verify your identity and understand how you intend to use your account.",
  "what_we_check": "What we check:",
  "check_1": "·Your identity and background (via KYC)",
  "check_2": "·The source of your funds",
  "check_3": "·Whether you're a politically exposed person (PEP)",
  "check_4": "·Any history of financial crimes",
  "your_privacy_is_protected": "Your privacy is protected.",
  "your_privacy_is_protected_description":
      "All information is securely stored and only used for regulatory compliance.",
  "kyc_verification_failed": "KYC Verification Failed",
  "kyc_verification_failed_description":
      "We're unable to verify your identity at this time. Please check your submitted information and ensure all documents are clear, valid, and up to date.",
  "contact_us": "Contact Us",
  "re_verify_now": "Re-verify Now",
  "please_enter_keywords_to_search": "Please enter keywords to search",
  "input...": "input...",

  /// home
  'hello': 'Hello',
  'insights': 'Insights',
  'notification': 'Notification',
  'notices': 'Notices',
  'transaction': 'Transaction',
  'show_unread': 'Show unread',
  'filter_date': 'Filter date',

  /// services
  'verified': 'Verified',
  'unverified': 'Unverified',
  'service': 'Service',
  'coupon': 'Coupon',
  'setting': 'Setting',
  'personal_info': 'Personal\nInfo',
  'google_authenticator': 'Google\nAuthenticator',
  'support': 'Support',
  'customer_service': 'Customer\nService',
  'faqs': 'FAQs',
  'log_out': 'Log Out',
  'app_version': 'App version',
  'coupon_code': 'Coupon Code',
  'active': 'Active',
  'expired': 'Expired',
  'copy_code': 'Copy Code',
  'used': 'Used',
  'code': 'Code',
  'status': 'Status',
  'applicable_description': 'Applicable: Walve withdrawal fees only',
  'valid_until': 'Valid Until',
  'applicable_description1': 'Applicable to waive withdrawal fees only',
  'no_record_found': 'No record found',
  'edit_nickname': 'Edit Nickname',
  'enter_your_nickname': 'Enter your nickname',
  'please_enter_your_nickname': 'Please enter your nickname',
  'nickname_can_only_be_modified_1_time_per_7_days':
      'Nickname can only be modified 1 time per 7 days',
  'save': 'Save',

  /// Google Authenticator
  'google_authenticator_h': 'Google Authenticator',
  'unbind_google_authenticator': 'Unbind Google Authenticator',
  'unbind_google_authenticator1': 'Unbind Google Authenticator?',
  'secure_your_account': 'Secure Your Account',
  'unbind_google_description':
      "You are about to remove Google Authenticator from your account.",
  'unbind_google_description1':
      "This will disable two-factor authentication (2FA), which is a key layer of protection for your account. Without 2FA, your account may become more vulnerable to unauthorized access.",
  'unbind_google_description2':
      "We strongly recommend keeping Google Authenticator enabled to safeguard your assets and prevent unauthorized actions.",
  'unbind_google_description3':
      "Are you sure you want to proceed with unbinding?",
  'bind_google_description':
      "For your account's security, we recommend enabling Google Authenticator for two-factor authentication (2FA).",
  'bind_google_description1':
      "This adds an extra layer of protection to sensitive actions and helps keep your assets safe.",
  'bind_google_note':
      "Get the Google Authenticator app from your app store to begin setup.",
  'enable_now': 'Enable Now',
  'cancel': 'Cancel',
  "download_and_install_google_authenticator":
      "Download and Install Google Authenticator",
  "download_and_install_google_authenticator_description":
      "Go to the App Store (iOS) or Google Play (Android), search for Google Authenticator, and install it.",
  "add_your_account_in_google_authenticator":
      "Add your account in Google Authenticator",
  "add_your_account_in_google_authenticator_description":
      "Open the Google Authenticator app.",
  "add_your_account_in_google_authenticator_description_2":
      'Tap the "+" button.',
  "add_your_account_in_google_authenticator_description_3":
      'Choose "Enter a setup key".',
  "add_your_account_in_google_authenticator_description_4":
      "Copy and paste the following key:",
  "copy_key": "Copy Key",
  "get_your_6_digit_google_code": "Get your 6-digit Google code",
  "get_your_6_digit_google_code_description":
      "Google Authenticator will now generate a 6-digit code that refreshes every 30 seconds. This is your dynamic security code.",
  "paste": "Paste",
  "please_enter_a_valid_verification_code":
      "Please enter a valid verification code",
  "confirm_your_identity": "Confirm Your Identity",
  "confirm_your_identity_description":
      "For your security, please enter your account password to confirm the removal of Google Authenticator.",
  "please_enter_your_account_password": "Please enter your account password",
  "confirm": "Confirm",
  "unbind_google_verification": "Unbind Google Verification",
  "unbind_google_verification_description":
      "Enter the 6-digit code from your Google Authenticator to confirm.",
  "unbind_google_successfully":
      "Google Verification has been successfully unbound",
  "unbind_google_successfully_description":
      "Your account no longer uses two-factor authentication. We recommend enabling another security method as soon as possible.",
  "details": "Details",

  /// personal info
  'personal_info_h': 'Personal Info',
  'edit': 'Edit',
  'basic_info': 'Basic Info',
  'account_status': 'Account Status',
  'security_setting_access': 'Security Setting Access',
  'document_type': 'Document type',
  'document_number': 'Document number',
  'id_photo_upload': 'ID photo upload',
  'verification_status': 'Verification status',
  'kyc_status': 'KYC status',
  'withdrawal': 'Withdrawal',
  'account_level': 'Account level',
  'completed': 'Completed',
  'enabled': 'Enabled',
  'account_status_active': 'Active',
  'current_phone_number': 'Current phone number',
  'new_phone_number': 'New phone number',
  'enter_your_new_phone_number': 'Enter your new phone number',
  'please_enter_your_new_phone_number': 'Please enter your new phone number',
  'update_complete': 'Update complete!',
  'your_new_phone_number_is_now_saved': 'Your new phone number is now saved.',
  'current_password': 'Current password',
  'enter_your_current_password': 'Enter your current password',
  'please_enter_your_current_password': 'Please enter your current password',
  'new_password': 'New password',
  'enter_your_new_password': 'Enter your new password',
  'please_enter_your_new_password': 'Please enter your new password',
  'confirm_password': 'Confirm password',
  'enter_your_confirm_password': 'Enter your confirm password',
  'confirm_password_must_be_the_same_as_new_password':
      'Confirm password must be the same as new password',
  'next': 'Next',
  "change_password_description":
      "A verification code has been sent to your email. Please enter the 6-digit code to proceed with changing your password.",
  "didnt_receive_resend_a_code": "Didn't receive? Resend a code",
  "your_new_password_is_now_saved": "Your new password is now saved.",

  /// Wealth
  'investment': 'Investment',
  'wealth_allocation_composition': 'Wealth Allocation Compostion',
  'cash': 'Cash',
  'commission': 'Commission',
  "projected_returns": "Projected Returns",
  "minimum_investment": "Minimum Investment",
  "lock_in_period": "Lock-in Period",
  "redemption_fee": "Redemption Fee",
  "return_description":
      "*Returns are variable and based on actual trading performance.",
  'day': 'day',
  'days': 'days',
  'daily': 'daily',
  'weekly': 'weekly',
  'monthly': 'monthly',
  'per_xx_days': 'per @days days',
  'aurenix_alphaedge_series': 'Aurenix AlphaEdge Series',
  "structure": "Structure",
  "invest": "Invest",
  "enter_investment_amount": "Enter Investment Amount",
  "please_enter_investment_amount": "Please enter investment amount",
  "please_enter_valid_investment_amount":
      "Please enter a valid investment amount",
  "investment_amount_description":
      "This will allocate funds from your Funding Account into the strategy.",
  "minimum": "Minimum",
  "available_balance": "Available Balance",
  "please_increase_your_investment_amount":
      "Please increase your investment amount",
  "please_provide_your_digital_signature":
      "Please provide your digital signature",
  "failed_to_upload_signature": "Failed to upload signature",
  "signature_required": "Signature Required",
  "clear": "Clear",
  "signature_description":
      "To complete your investment, please provide your digital signature as confirmation that you have reviewed and agreed to the",
  "investment_terms_and_conditions": "investment terms and conditions",
  "investment_confirmed": "Investment Confirmed",
  "subscription_date": "Subscription Date",
  "next_allocation_return_date": "Next Allocation Return Date",
  "view_my_investment": "View My Investment",
  "you_ve_successfully_invested": "You've successfully invested",
  "in": "in",

  /// Portfolio
  'my_portfolio': 'My Portfolio',
  'team_analytics': 'Team Analytics',
  'subscribed_strategies': 'Subscribed Strategies',
  'redemption_fees': 'Redemption Fees',
  "initial_funding_date": "Initial Funding Date",
  "initial_amount_allocated": "Initial Amount Allocated",
  "total_amount_allocated": "Total Amount Allocated",
  "total_returns_to_date": "Total Returns to date",
  "returns_credited_to_funding_account": "Returns Credited to Funding Account",
  "returns_auto_reallocated": "Returns auto-reallocated",
  "total_amount_redeemed": "Total Amount Redeemed",
  "stop_loss_event": "Stop Loss Event",
  "current_allocated_balance": "Current Allocated Balance",
  "redemption_period": "Redemption Period",
  "auto_reallocate": "Auto Reallocate",
  "cancel_reallocate": "Cancel Reallocate",
  "Redeem": "Redeem",
  "subscription_details": "Subscription Details",
  "In-progress": "In-progress",
  "redeemed": "Redeemed",
  "subscription_agreement": "Subscription Agreement",
  "redemption": "Redemption",
  "event": "Event",
  "amount": "Amount",
  "enter_redemption_amount": "Enter redemption amount",
  "please_enter_correct_redemption_amount":
      "Please enter correct redemption amount",
  "receive_amount": "Receive amount",
  "amount_exceeds_maximum_redeemable": "Amount exceeds maximum redeemable",
  "confirm_redemption": "Confirm Redemption",
  "you_are_about_to_redeem_your_allocation":
      "You are about to redeem your allocation",
  "redemption_amount": "Redemption Amount",
  "less_redemption_fee": "Less Redemption Fee",
  "amount_to_receive": "Amount to Receive",
  "redemption_processing_period": "Redemption Processing Period",
  "remaining_allocation_balance": "Remaining Allocation Balance",
  "redeem_note_description":
      "Returns are only distributed on your remaining allocation balance. If you redeem ahead of the next scheduled return cycle, no returns will be accrued on the redeemed amount.",
  "redeem_note_description1": "Once submitted, this action cannot be reversed.",
  "redemption_request_submitted": "Redemption Request Submitted",
  "redemption_successfully_tip":
      "Your redemption request has been successfully submitted. The total amount of",
  "will_be_processed_within": "will be processed within",
  "calendar_days": "calendar days",
  "no_interest_during_redemption_period":
      "No interest will be accrued during the redemption period.",
  "earning_tracker": "Earning Tracker",
  "earnings_tracker": "Earnings Tracker",
  "activate_strategy": "Activate Strategy",
  "requested": "Requested",
  "advance_tier": "Advance Tier",
  "rejected": "Rejected",
  "action": "Action",
  "Full Name": "Full Name",
  "Level": "Level",
  "overall_investment": "Overall Investment",
  "wealth_name": "Wealth Name",
  "S/N": "S/N",
  "UID": "UID",
  "select_new_tier": "Select New Tier",
  "Affiliate Partner (AP)": "Affiliate Partner (AP)",
  'Certified Partner (CP)': 'Certified Partner (CP)',
  "enter_minimum_required_maintenance_amount":
      "Enter minimum required maintenance amount",
  "please_enter_minimum_required_maintenance_amount":
      "Please enter minimum required maintenance amount",
  "please_enter_valid_amount": "Please enter a valid amount",
  "tier_advancement_request_submitted": "Tier Advancement Request Submitted",
  "your_request_to_upgrade": "Your request to upgrade",
  "to": "to",
  "has_been_sent":
      "has been sent. The change will take effect after approval by platform.",

  /// Account
  'Verified': 'Verified',
  'Unverified': 'Unverified',
  'All_Accounts': 'All Accounts',
  'Funding_Account': 'Funding Account',
  'Commission_Account': 'Commission Account',
  'Total_Available_Balance': 'Total Available Balance',
  'Total_Balance': 'Total Balance',
  'Add_Funds': 'Add Funds',
  'Send': 'Send',
  'Transaction_History': 'Transaction History',
  'See_More': 'See More',

  'Transfer_Commission': 'Transfer Commission',
  'Account_ID': 'Account ID',
  'Enter_your_account': 'Enter your account',
  'Transfer_amount': 'Transfer amount',
  'Enter_amount': 'Enter amount',
  'Transfer': 'Transfer',
  'Details': 'Details',
  'Withdrawal_Balance': 'Withdrawal Balance',
  'Transferable_Balance': 'Transferable Balance',
  'Returns': 'Returns',
  'transfer_successful':
      "Transfer successful!\nYour commission has been added to your main account.",
  'invitation_bonus_earned':
      "You've earned an invitation bonus!\nThanks for inviting a new user to our platform!",
  'invitation_bonus_rejected':
      "Your invitation bonus was not approved as the invited user didn't meet the required criteria.",
  'investment_returns_added':
      "You've earned your returns from your investment. We've added it into your account.",
  'transfer_method': "Transfer Method",
  'amount_credited':
      "The amount has been successfully credited to your wallet.",
  'deposit_successful':
      "Your deposit was successful!\nThe funds are now available in your account.",
  'transfer_request_received':
      "Your transferred request has been received and is being processed.",
  'transfer_failed':
      "Transfer failed. Your request was rejected. Please check your details and try again.",
  'transfer_success_message':
      'Your transfer of \$@amount has been successfully processed. The amount has been sent to your account.',
  'redemption_processing':
      'Your redemption is being processed.\nIt may take a few business days to complete.',
  'redemption_success':
      'Redemption successful!\nThe amount has been added to your account.',
  'redemption_failed':
      'Redemption request rejected.\nPlease verify details or contact support.',
  'Transferred': 'Transferred',
  'reference_id': 'Reference ID',
  'date': 'Date',
  'request_date': 'Request Date',
  'type': 'Type',
  'remark': 'Remark',
  'reason': 'Reason',
  'sender': 'Sender',
  'address': 'Address',
  'recipient': 'Recipient',
  'network': 'Network',
  'redeem': 'redeem',
  'select_add_funds_method': 'Select Add Funds Method',
  'On_chain_deposit': 'On-chain deposit',
  'Use_blockchain_networks_to_move':
      'Use blockchain networks to move funds into your account.',
  'Receive_from_AURENIX_AI_users': 'Receive from AURENIX AI users',
  'Get_funds_by_UID_Email_QRcode':
      'Get funds by sharing your UID, Email or QR code.',
  'Buy_crypto': 'Buy crypto',
  'Instantly_purchase_crypto_with_Visa_MasterCard':
      'Instantly purchase crypto with Visa/MasterCard.',
  'select_currency': 'Select currency',
  'select_network': 'Select network',
  'save_code': 'Save Code',
  'share_code': 'Share Code',
  'Minimum_deposit': 'Minimum deposit',
  'Estimate_arrival': 'Estimate arrival',
  'Contract_address': 'Contract address',
  'add_funds_chain_recharge_select_network_tips':
      'Please make sure you select the correct network. Sending USDT to the wrong network may result in loss of funds.',
  'Wallet_Address:': 'Wallet Address:',
  'Buy_Crypto': 'Buy Crypto',
  'Receive_USDT': 'Receive USDT',
  'Pay_with_Visa_Master_Card': 'Pay with Visa/Master Card',
  'Confirm_Order': 'Confirm Order',
  'Buy_crypto_amount': 'Buy crypto amount',
  'Pay_with': 'Pay with',
  'select_sending_method': 'Select Sending Method',
  'on_chain_transfer': 'On-chain transfer',
  'Use_blockchain_networks_transfer':
      'Use blockchain networks to transfer funds into your account.',
  'Transfer_to_AURENIX_AI_users': 'Transfer to AURENIX AI users',
  'Transfer_funds_by_using_UID_and_Email':
      'Transfer funds by using UID and Email.',
  'Address': 'Address',
  'enter_transfer_address': 'Enter transfer address',
  'Transfer_Amount': 'Transfer Amount',
  'Enter_transfer_amount': 'Enter transfer amount',
  'Enter_voucher_code_if_any': 'Enter voucher code (if any)',
  'Enter_voucher_code': 'Enter voucher code',
  'Receive_amount': 'Receive amount',
  'Fees': 'Fees',
  'Withdraw': 'Withdraw',
  'Confirm_Withdrawal': 'Confirm Withdrawal',
  'transfer_out_amount': 'Transfer out amount',
  'send_chain_withdrawal_confirm_tips':
      "On-chain withdrawal are instant and irreversible. Please ensure the recipient's information is correct before confirming.",
  'account_address': 'Account Address',
  'I_have_read_and_agree_to_the': 'I have read and agree to the ',
  'add_funds_crypto_agree_content_last':
      '. I fully understand the risks involved in cryptocurrency transactions and acknowledge that purchases are final, non-refundable, and cannot be reversed once confirmed.',
  'Address_Book': 'Address Book',
  'No_record_found': 'No record found',
  'add_address': 'Add Address',
  'Select': 'Select',
  'enter_address': 'Enter_address',
  'select_your_network': 'Select your network',
  'Remark_optional': 'Remark (optional)',
  '20_character_remark': '20-character remark',
  'set_trusted_payee_to_skip':
      'Set as a trusted payee to skip verification next time.',
  'Edit_Remarks': 'Edit Remarks',
  'delete_payee': 'Delete Payee',
  'delete_payee_tips1':
      'Are you sure you want to delete this payee from your trusted list?',
  'delete_payee_tips2':
      'You will need to verify again if you make a transfer to this address in the future',
  'set_trusted_payee': 'Set Trusted Payee',
  'account_password': 'Account Password',
  'email_verification_code': 'Email Verification Code',
  'send_code': 'Send Code',
  'google_verification_code': 'Google Verification Code',
  'google_verification_code_tips':
      '6-digit code on your Google Authenticator app',

  'edit_remarks': 'Edit Remarks',
  'please_enter_address': 'Please enter address',
  'please_enter_remark': 'Please enter remark',
  'delete': 'Delete',
  'You_earned_commission':
      "You've earned a commission! Thank you for inviting a new user to our platform.",
  'Your_commission_was_not_approved':
      'Your commission was not approved. For assistance, please contact customer support.',
  'amount_successfully_credited':
      'The amount has been successfully credited to your funding account.',
  'withdraw_successful': 'Withdraw successful',
  'enter_email': 'Enter Email',
  'enter_uid': 'Enter UID',
  'send_amount': 'Send amount',
  'send_uid_email_confirm_tips':
      "Internal transfers are instant and irreversible. Please ensurethe recipient's information is correct before confrming.",
  'Confirm_Transfer': 'Confirm Transfer',
  'payee': 'Payee',
};
