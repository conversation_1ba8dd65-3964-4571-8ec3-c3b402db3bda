import 'package:aurenixai_app/api/address_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/address_book_res.dart';
import 'package:aurenixai_app/pages/account/children/address_book/widgets/delete_address_confirm_modal.dart';
import 'package:aurenixai_app/pages/account/children/address_book/widgets/edit_remark_modal.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddressBookPage extends StatefulWidget {
  const AddressBookPage({super.key});

  @override
  _AddressBookPageState createState() => _AddressBookPageState();
}

class _AddressBookPageState extends State<AddressBookPage> {
  bool isSelect = false;

  late EasyRefreshController _controller;
  TextEditingController nameController = TextEditingController();
  String currentName = '';

  /// 列表
  List<AddressBookRes> list = [];
  int pageNum = 1;
  bool isEnd = false;

  String? sendType = 'chain';

  @override
  void initState() {
    super.initState();

    final args = Get.parameters;
    String type = args['type'] ?? '';
    setState(() {
      sendType = type;
    });
    _controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  _onRefresh() async {
    try {
      await getList(true);
      if (!mounted) {
        return;
      }
    } catch (e) {}
    _controller.finishRefresh();
    _controller.resetFooter();
    _controller.finishLoad(
      isEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  _onLoad() async {
    await getList();
    if (!mounted) {
      return;
    }
    _controller.finishLoad(
      isEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  /// 获取列表
  getList([bool isRefresh = false]) async {
    try {
      if (isRefresh) {
        pageNum = 1;
      } else if (isEnd) {
        return;
      }
      final res = await AddressApi.getAddressBookList({
        "pageNum": pageNum,
        "pageSize": 10,
        "type": sendType == 'chain' ? '0' : '1',
      });
      setState(() {
        isEnd = res.isEnd;
        if (isRefresh) {
          list = res.list;
        } else {
          list.addAll(res.list);
        }
        pageNum++;
      });
    } catch (e) {}
  }

  bool get isEmpty {
    return isEnd && list.isEmpty;
  }

  void onAdd() async {
    if (sendType == 'chain') {
      Get.toNamed(Routes.addressBookAdd);
    } else {
      Get.toNamed(
        Routes.addressBookAddUidEmail,
        parameters: {'type': '$sendType'},
      );
    }
  }

  void onSetTrustedPpayee() async {
    List<String> idList = [];
    for (var item in list) {
      if (item.isCheck == true) {
        idList.add(item.id);
      }
    }
    Get.toNamed(
      Routes.addressSetTrustedPayee,
      parameters: {'idList': idList.join(',')},
    );
  }

  void onDelete(BuildContext context) async {
    List<String> idList = [];
    for (var item in list) {
      if (item.isCheck == true) {
        idList.add(item.id);
      }
    }
    DeleteAddressConfirmModal.showModal(
      context: context,
      idList: idList,
      onConfirm: () {
        _onRefresh();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text('Address_Book'.tr),
        actions: [
          if (list.isNotEmpty)
            isSelect
                ? GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      setState(() {
                        isSelect = false;
                      });
                    },
                    child: Text(
                      'cancel'.tr,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w400,
                        color: customTheme.textColor,
                      ),
                    ),
                  )
                : GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      setState(() {
                        for (var item in list) {
                          item.isCheck = false;
                        }
                        isSelect = true;
                      });
                    },
                    child: Text(
                      'Select'.tr,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w400,
                        color: customTheme.textColor,
                      ),
                    ),
                  ),
          SizedBox(width: 15.w),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onLoad: _onLoad,
              onRefresh: _onRefresh,
              refreshOnStart: true,
              refreshOnStartHeader: WidgetUtil.getRefreshOnStartHeader(context),
              header: WidgetUtil.getRefreshOnStartHeader(context),
              child: isEmpty
                  ? SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(height: 147.h),
                          Image.asset(
                            customThemeImage.sendChainIconAddressNoRecord,
                            width: 80.w,
                            height: 80.w,
                          ),
                          SizedBox(height: 14.h),
                          Text(
                            'No_record_found'.tr,
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w400,
                              color: customTheme.hintTextColor,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      itemCount: list.length,
                      itemBuilder: (context, index) {
                        return buildItem(
                          customTheme: customTheme,
                          customThemeImage: customThemeImage,
                          info: list[index],
                        );
                      },
                    ),
            ),
          ),
          isSelect
              ? WidgetUtil.getBottomWrapper(
                  context: context,
                  color: customTheme.pageBgColor,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: OutlineButton(
                          title: 'set_trusted_payee'.tr,
                          height: 51.w,
                          onPress: onSetTrustedPpayee,
                        ),
                      ),
                      SizedBox(width: 11.w),
                      Expanded(
                        child: PrimaryButton(
                          title: 'delete'.tr,
                          height: 51.w,
                          onPress: () => onDelete(context),
                        ),
                      ),
                    ],
                  ),
                )
              : WidgetUtil.getBottomButton(
                  context: context,
                  title: 'add_address'.tr,
                  onSubmit: onAdd,
                ),
        ],
      ),
    );
  }

  Widget buildItem({
    required CustomThemeSwatch customTheme,
    required CustomThemeImage customThemeImage,
    required AddressBookRes info,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.back(
          result: {sendType: sendType, 'id': info.id, 'address': info.address},
        );
      },
      child: Container(
        padding: EdgeInsets.only(top: 16.w),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: customTheme.dividerColor, width: 0.5.w),
          ),
        ),
        child: Row(
          children: [
            if (isSelect)
              GestureDetector(
                onTap: () {
                  setState(() {
                    info.isCheck = !(info.isCheck ?? false) ? true : false;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(top: 3.w),
                  child: Image.asset(
                    (info.isCheck ?? false) == true
                        ? customThemeImage.authChoose
                        : customThemeImage.authUnchoose,
                    width: 18.w,
                  ),
                ),
              ),
            if (isSelect) SizedBox(width: 20.w),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        info.remark ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: customTheme.textColor,
                        ),
                      ),
                      SizedBox(width: 10.w),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          EditRemarkModal.showModal(
                            context,
                            id: info.id,
                            remark: info.remark ?? '',
                            onConfirm: (remark) {
                              setState(() {
                                info.remark = remark;
                              });
                            },
                          );
                        },
                        child: Image.asset(
                          customThemeImage.sendChainIconEdit,
                          width: 22.w,
                          height: 22.w,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 11.h),
                  if (info.type == '0')
                    Text(
                      '${'Address'.tr}: ${info.address}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w400,
                        color: customTheme.hintTextColor,
                      ),
                    ),
                  if (info.type == '1')
                    Text(
                      '${info.addressType == '0' ? 'email'.tr : 'UID'.tr}: ${info.address}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w400,
                        color: customTheme.hintTextColor,
                      ),
                    ),
                  if (info.type == '0') SizedBox(height: 7.h),
                  if (info.type == '0')
                    Text(
                      '${'Network'.tr}: ${info.network}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w400,
                        color: customTheme.hintTextColor,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  SizedBox(height: 22.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
