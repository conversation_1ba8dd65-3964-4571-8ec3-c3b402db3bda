import 'dart:async';

import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/material.dart';

class RichTextPage extends StatefulWidget {
  final String? title;
  final String content;

  const RichTextPage({super.key, this.title, required this.content});

  @override
  _RichTextPageState createState() => _RichTextPageState();
}

class _RichTextPageState extends State<RichTextPage> {
  final Completer<String> configText = Completer<String>();
  WebViewController? controller;

  @override
  void initState() {
    super.initState();
    getContentText();
  }

  getContentText() async {
    try {
      String msg = await getHtml(widget.content);
      configText.complete(msg);
    } catch (e) {
      configText.completeError("Error");
    }
  }

  Future<String> getHtml(String msg) async {
    String cssText = await DefaultAssetBundle.of(
      context,
    ).loadString("assets/css/tinymce.content.min.css");
    String webCss = '';
    if (kIsWeb) {
      webCss = '.mce-content-body{font-size:20px;padding:0;}';
    }
    String text =
        '''
<!DOCTYPE html><html lang="zh">
<head><meta charset="utf-8"><style>$cssText</style><style>$webCss</style></head>
<body class="mce-content-body">
$msg
</body>
</html>
''';
    return text;
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? ''),
        backgroundColor: customTheme.pageBgColor,
        foregroundColor: customTheme.textColor,
      ),
      backgroundColor: customTheme.pageBgColor,
      body: FutureBuilder(
        future: configText.future,
        builder: (context, AsyncSnapshot<String> snapshot) {
          if (snapshot.hasData) {
            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: HtmlWidget(
                  snapshot.data ?? '',
                  textStyle: TextStyle(color: customTheme.textColor),
                ),
              ),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: customTheme.downColor,
                    size: 60,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(snapshot.error.toString()),
                  ),
                ],
              ),
            );
          }
          return Center(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 100),
              child: WidgetUtil.getLoadingIcon(context),
            ),
          );
        },
      ),
    );
  }
}
