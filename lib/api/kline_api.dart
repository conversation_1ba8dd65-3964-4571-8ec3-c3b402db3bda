//
// country/public/list_front

import 'package:aurenixai_app/models/kline_res.dart';
import 'package:aurenixai_app/utils/http_util.dart';
import 'package:aurenixai_app/utils/index.dart';

class KlineApi {
  static Future<List<KlineRes>> getKlineList(String symbol) async {
    final res = await HttpUtil.post('/core/v1/kline/list_front', {
      "symbol": symbol,
    });
    return (res as List<dynamic>)
        .map((e) => KlineRes.fromJson(CommonUtils.removeNullKeys(e)))
        .toList();
  }
}
