import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class NormalSelect extends StatelessWidget {
  final String? label;
  final double? labelFontSize;
  final TextStyle? labelStyle;
  final String? hintText;
  final String? content;
  final Widget Function(BuildContext context)? contentBuilder;
  final TextStyle? contentStyle;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Decoration? decoration;
  final double? height;
  final double? fontSize;
  final FontWeight? fontWeight;
  final VoidCallback? onTap;
  final Widget? prefix;
  final double? rowSpacing;
  final bool? rowMainAxisSizeMin;

  const NormalSelect({
    super.key,
    this.label,
    this.labelFontSize,
    this.labelStyle,
    this.hintText,
    this.content,
    this.contentBuilder,
    this.contentStyle,
    this.margin,
    this.padding,
    this.decoration,
    this.height,
    this.fontSize,
    this.fontWeight,
    this.onTap,
    this.prefix,
    this.rowSpacing,
    this.rowMainAxisSizeMin,
  });

  bool hasContent() {
    if (content != null && content!.isNotEmpty) {
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final customTheme = themeProvider.customTheme;
    final customThemeImage = themeProvider.customThemeImage;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8.h,
      children: [
        if (label != null)
          Text(
            label ?? '',
            style:
                labelStyle ??
                TextStyle(
                  fontSize: labelFontSize ?? fontSize ?? 13.sp,
                  color: customTheme.textColor,
                ),
          ),
        GestureDetector(
          onTap: onTap,
          behavior: HitTestBehavior.translucent,
          child: Container(
            height: height ?? 55.w,
            margin: margin,
            padding: padding ?? EdgeInsets.symmetric(horizontal: 19.w),
            decoration:
                decoration ??
                BoxDecoration(
                  borderRadius: BorderRadius.circular(3.w),
                  color: customTheme.inputBgColor,
                ),
            child: Row(
              spacing: rowSpacing ?? 19.w,
              mainAxisSize: rowMainAxisSizeMin == true
                  ? MainAxisSize.min
                  : MainAxisSize.max,
              children: [
                if (prefix != null) prefix!,
                Expanded(
                  child: contentBuilder != null
                      ? contentBuilder!(context)
                      : Text(
                          content ?? hintText ?? '',
                          style: hasContent()
                              ? contentStyle ??
                                    TextStyle(
                                      fontSize: fontSize ?? 15.sp,
                                      color: customTheme.textColor,
                                      fontWeight: FontWeight.w600,
                                    )
                              : TextStyle(
                                  fontSize: fontSize ?? 15.sp,
                                  color: customTheme.inputHintColor,
                                  fontWeight: FontWeight.w600,
                                ),
                        ),
                ),
                Image.asset(
                  customThemeImage.commonDown,
                  width: 20.w,
                  height: 20.w,
                  fit: BoxFit.fill,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
