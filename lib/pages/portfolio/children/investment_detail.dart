import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/models/fund_investment_record_res.dart';
import 'package:aurenixai_app/models/investment_log_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class InvestmentDetailPage extends StatefulWidget {
  const InvestmentDetailPage({
    super.key,
    required this.investment,
    required this.productName,
  });

  final FundInvestmentRecordRes investment;
  final String productName;

  @override
  State<InvestmentDetailPage> createState() => _InvestmentDetailPageState();
}

class _InvestmentDetailPageState extends State<InvestmentDetailPage> {
  late EasyRefreshController _controller;
  List<InvestmentLogRes> investmentList = [];
  int _pageNum = 1;
  bool _isEnd = false;
  Map<String, String> eventTypeMap = {};

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    // fund_investment_log.eventtype
    CommonApi.getDictList(parentKey: 'fund_investment_log.eventtype').then((
      list,
    ) {
      eventTypeMap = {for (var item in list) item.key: item.value};
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    try {
      _pageNum = 1;
      final res = await InvestmentApi.getFundInvestmentLogList({
        'pageNum': _pageNum,
        'pageSize': 10,
        'investmentId': widget.investment.id,
      });
      _pageNum++;
      investmentList = res.list;
      _isEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _controller.finishRefresh();
    if (_isEnd) {
      _controller.finishLoad(IndicatorResult.noMore);
    } else {
      _controller.resetFooter();
    }
  }

  Future<void> _onLoadMore() async {
    try {
      final res = await InvestmentApi.getFundInvestmentLogList({
        'pageNum': _pageNum,
        'pageSize': 10,
        'investmentId': widget.investment.id,
      });
      _pageNum++;
      investmentList.addAll(res.list);
      _isEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _controller.finishLoad(
      _isEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: Text('subscription_details'.tr)),
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        onLoad: _onLoadMore,
        refreshOnStart: true,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                margin: EdgeInsets.only(bottom: 14.5.h),
                decoration: BoxDecoration(color: customTheme.inputBgColor),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 19.h),
                    Text(
                      widget.productName,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8.5.h),
                    Divider(height: 0.5),
                    SizedBox(height: 15.5.h),
                    _line(
                      'total_returns_to_date'.tr,
                      '\$${widget.investment.totalReturns.toString().fmt}',
                    ),
                    _line(
                      'returns_credited_to_funding_account'.tr,
                      '\$${widget.investment.returnsToWallet.toString().fmt}',
                    ),
                    _line(
                      'returns_auto_reallocated'.tr,
                      '\$${widget.investment.returnsReinvested.toString().fmt}',
                    ),
                    _line(
                      'total_amount_redeemed'.tr,
                      '\$${widget.investment.totalRedeemed.toString().fmt}',
                    ),
                    if (widget.investment.stopLossEvent > 0)
                      _line(
                        'stop_loss_event'.tr,
                        '\$${widget.investment.stopLossEvent.toString().fmt}',
                      ),
                    _line(
                      'current_allocated_balance'.tr,
                      '\$${widget.investment.currentBalance.toString().fmt}',
                      highlight: widget.investment.stopLossEvent > 0
                          ? false
                          : true,
                      isWarn: widget.investment.stopLossEvent > 0,
                    ),
                    SizedBox(height: 17.h),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'subscription_details'.tr,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    _tableHead(),
                    ...List.generate(
                      investmentList.length,
                      (index) => _tableBodyLine(index),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _tableHead() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final textStyle = TextStyle(
      fontSize: 12.sp,
      color: customTheme.lightTextColor,
    );

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 13.5.h, bottom: 9.h),
          child: Row(
            spacing: 4.w,
            children: [
              SizedBox(
                width: 90.w,
                child: Text('date'.tr, style: textStyle),
              ),
              Expanded(child: Text('event'.tr, style: textStyle)),
              SizedBox(
                width: 60.w,
                child: Text(
                  'amount'.tr,
                  style: textStyle,
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),
        Divider(height: 0.5),
      ],
    );
  }

  _tableBodyLine(int index) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final textStyle = TextStyle(fontSize: 12.sp, color: customTheme.textColor);
    final item = investmentList[index];
    final eventDate = DateTime.fromMillisecondsSinceEpoch(
      item.eventDate.toInt(),
    );

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 16.5.h, bottom: 15.h),
          child: Row(
            spacing: 4.w,
            children: [
              SizedBox(
                width: 90.w,
                child: Text(eventDate.yyyyMMdd, style: textStyle),
              ),
              Expanded(
                child: Text(
                  eventTypeMap[item.eventType] ?? '-',
                  style: textStyle,
                ),
              ),
              SizedBox(
                width: 60.w,
                child: Text(
                  item.amount != null
                      ? '\$ ${item.amount.toString().fmt}'
                      : '-',
                  style: textStyle.copyWith(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),
        Divider(height: 0.5),
      ],
    );
  }

  _line(
    String title,
    String value, {
    bool highlight = false,
    bool isWarn = false,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        spacing: 4.w,
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                color: customTheme.lightTextColor,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13.sp,
              color: highlight
                  ? customTheme.primaryColor
                  : isWarn
                  ? customTheme.downColor
                  : customTheme.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
