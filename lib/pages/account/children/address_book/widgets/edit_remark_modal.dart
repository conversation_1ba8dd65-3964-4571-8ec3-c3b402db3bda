import 'package:aurenixai_app/api/address_api.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class EditRemarkModal extends StatefulWidget {
  final String id;
  final String? remark;

  /// 确认回调
  final void Function(String) onConfirm;

  const EditRemarkModal({
    super.key,
    required this.id,
    this.remark,
    required this.onConfirm,
  });

  static Future<void> showModal(
    BuildContext context, {
    required String id,
    required void Function(String) onConfirm,
    String? remark,
  }) async {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return EditRemarkModal(id: id, remark: remark, onConfirm: onConfirm);
      },
    );
  }

  @override
  State<EditRemarkModal> createState() => _EditRemarkModalState();
}

class _EditRemarkModalState extends State<EditRemarkModal> {
  final TextEditingController _remarkController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _remarkController.text = widget.remark ?? '';
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    if (_remarkController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_nickname'.tr);
      return;
    }

    try {
      ToastUtil.showLoading();
      await AddressApi.addressUpdateRemark({
        'id': widget.id,
        'remark': _remarkController.text,
      });
      ToastUtil.dismiss();
      widget.onConfirm(_remarkController.text);
      Navigator.of(context).pop();
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'edit_remarks'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: false,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTopBar(context),
            SizedBox(height: 22.5.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: NormalInput(
                label: 'remark'.tr,
                controller: _remarkController,
                hintText: 'enter_your_nickname'.tr,
              ),
            ),
            SizedBox(height: 40.h),
            WidgetUtil.getBottomWrapper(
              context: context,
              child: Row(
                spacing: 9.w,
                children: [
                  Expanded(
                    child: OutlineButton(
                      title: 'cancel'.tr,
                      fontSize: 13.sp,
                      onPress: () {
                        Navigator.pop(context);
                      },
                    ),
                  ),
                  Expanded(
                    child: PrimaryButton(
                      title: 'confirm'.tr,
                      fontSize: 13.sp,
                      onPress: onSubmit,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
