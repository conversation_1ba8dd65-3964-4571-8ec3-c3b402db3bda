import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/common_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerModal {
  static final ImagePicker picker = ImagePicker();

  static Future<XFile?> showModal(
    BuildContext context, {
    bool isVideo = false,
  }) async {
    final index = await CommonPicker.showModal(
      context,
      list: ['take_photo'.tr, 'album'.tr],
    );
    if (index != null) {
      ImageSource source = index == 0
          ? ImageSource.camera
          : ImageSource.gallery;
      if (isVideo) {
        return picker.pickVideo(source: source);
      }
      return picker.pickImage(source: source);
    }
    return null;
  }

  static Future<List<XFile>?> showMultipleModal(
    BuildContext context, {
    bool isVideo = false,
  }) async {
    final index = await CommonPicker.showModal(
      context,
      title: '',
      list: ['take_photo'.tr, 'album'.tr],
    );
    if (index != null) {
      ImageSource source = index == 0
          ? ImageSource.camera
          : ImageSource.gallery;
      if (isVideo) {
        final video = await picker.pickVideo(source: source);
        return video != null ? [video] : null;
      }
      return picker.pickMultiImage();
    }
    return null;
  }

  static Future<void> retrieveLostData(ValueChanged<XFile?> handleFile) async {
    final LostDataResponse response = await picker.retrieveLostData();
    if (response.isEmpty) {
      return;
    }
    if (response.file != null) {
      handleFile(response.file);
    } else if (response.exception != null) {
      ToastUtil.showError(
        response.exception!.message ?? 'upload_failed_please_try_again'.tr,
      );
    }
  }

  static Widget getAndroidRetrieveLostDataWidget(
    ValueChanged<XFile?> handleFile,
  ) {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      return FutureBuilder<void>(
        future: retrieveLostData(handleFile),
        builder: (BuildContext context, AsyncSnapshot<void> snapshot) {
          switch (snapshot.connectionState) {
            case ConnectionState.none:
            case ConnectionState.waiting:
            case ConnectionState.done:
              return const SizedBox.shrink();
            default:
              if (snapshot.hasError) {
                ToastUtil.showError('upload_failed_please_try_again'.tr);
              }
              return const SizedBox.shrink();
          }
        },
      );
    }
    return const SizedBox.shrink();
  }
}
