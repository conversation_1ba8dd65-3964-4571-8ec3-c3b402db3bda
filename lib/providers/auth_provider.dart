import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/user.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

/// 保存用户信息
class AuthProvider with ChangeNotifier {
  User? _user;

  bool get isLogin {
    return _user != null;
  }

  User? get user {
    return _user;
  }

  String get userId {
    return _user?.id ?? '';
  }

  String get userEmail {
    if (_user != null) {
      return _user!.email;
    }
    return '';
  }

  // String get realName {
  //   if (_user != null) {
  //     return _user!.realName ?? '';
  //   }
  //   return '';
  // }

  // bool get tradePwdFlag {
  //   return _user?.tradePwdFlag == '1';
  // }

  /// 是否实名认证
  bool get isKyc {
    if (_user != null) {
      // 0:未认证/认证失败,1:认证成功,2:免除kyc,4:审核中
      return _user!.identifyStatus == '1' || _user!.identifyStatus == '2';
    }
    return false;
  }

  void editIdentifyStatus(String status) {
    if (_user != null) {
      _user!.identifyStatus = status;
      notifyListeners();
    }
  }

  // /// 是否开启谷歌验证码
  // bool get isOpenGoogleCode {
  //   if (_user != null) {
  //     return _user!.googleStatus == '1';
  //   }
  //   return false;
  // }

  /// 设置token并获取用户详情
  setToken(String token) async {
    try {
      await LocalUtil.setUserToken(token);
      User user = await UserApi.getUserInfo();
      setUser(user);
      EventBusUtil.fireLogin(UserLoginEvent(userId: user.userId));
    } catch (e) {
      rethrow;
    }
  }

  /// 获取用户详情
  Future<User> getUserInfo() async {
    try {
      User user = await UserApi.getUserInfo();
      setUser(user);
      return user;
    } catch (e) {
      rethrow;
    }
  }

  /// 设置用户信息
  setUser(User info) {
    _user = info;
    notifyListeners();
  }

  /// 修改手机号
  modifyMobile(String mobile, String countryCode) {
    if (_user != null) {
      _user!.mobile = mobile;
      _user!.interCode = countryCode;
      notifyListeners();
    }
  }

  logout() {
    _user = null;
    LocalUtil.removeUserToken();
    notifyListeners();
    Get.offAllNamed(Routes.auth);
    EventBusUtil.fireUserLogout();
  }

  // /// 设置google验证码
  // setGoogleStatus(String googleStatus) async {
  //   if (_user != null) {
  //     _user!.googleStatus = googleStatus;
  //     notifyListeners();
  //   }
  // }
}
