import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/sms_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class NotificationBar extends StatefulWidget {
  const NotificationBar({super.key});

  @override
  _NotificationBarState createState() => _NotificationBarState();
}

class _NotificationBarState extends State<NotificationBar> {
  SmsRes? notice;

  @override
  void initState() {
    super.initState();
    getNotice();
  }

  Future<void> getNotice() async {
    try {
      final res = await CommonApi.getNoticeList({
        'pageNum': 1,
        'pageSize': 1,
        'isReadFlag': '0',
      });
      if (res.list.isNotEmpty) {
        notice = res.list.first;
        if (mounted) {
          setState(() {});
        }
      }
    } catch (e) {
      e.printError();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    if (notice == null) {
      return SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () {
        Get.toNamed(
          Routes.notificationDetail,
          parameters: {
            'id': notice!.id,
            'title': notice!.title,
            'date': notice!.updateDatetime.toString(),
          },
        );
        setState(() {
          notice = null;
        });
      },
      child: Container(
        margin: EdgeInsets.only(top: 4.w),
        padding: EdgeInsets.only(left: 12.5.w, right: 8.5.w),
        height: 35.w,
        decoration: BoxDecoration(
          color: customTheme.notificationBgColor,
          borderRadius: BorderRadius.circular(7.w),
        ),
        child: Row(
          spacing: 12.5.w,
          children: [
            Expanded(
              child: Text(
                notice!.title,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: customTheme.subTextColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  notice = null;
                });
              },
              child: Image.asset(
                customThemeImage.homeClose,
                width: 18.w,
                height: 18.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
