import 'dart:async';

import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/investment_summary.dart';
import 'package:aurenixai_app/models/user_node_level_res.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/line_card.dart';
import 'widgets/team_table.dart';

class PortfolioPage extends StatefulWidget {
  const PortfolioPage({super.key});

  @override
  _PortfolioPageState createState() => _PortfolioPageState();
}

class _PortfolioPageState extends State<PortfolioPage>
    with SingleTickerProviderStateMixin {
  late EasyRefreshController _controller;
  bool isPortfolioEmpty = false;
  List<InvestmentSummary> portfolioList = [];
  List<UserNodeLevelRes> teamList = [];
  bool isTeamLoading = true;
  bool isTeamEmpty = false;
  late TabController _tabController;
  int currentTabIndex = 0;
  late StreamSubscription<InvestmentSuccessEvent> investmentSuccessSubscription;
  late StreamSubscription<UserNodeUpgradeEvent> userNodeUpgradeSubscription;
  late StreamSubscription<RedeemApplyEvent> redeemApplySubscription;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _controller = EasyRefreshController(controlFinishRefresh: true);
    investmentSuccessSubscription = EventBusUtil.listenInvestmentSuccess((
      event,
    ) {
      _onRefresh();
    });
    userNodeUpgradeSubscription = EventBusUtil.listenUserNodeUpgrade((event) {
      _onRefresh();
    });
    redeemApplySubscription = EventBusUtil.listenRedeemApply((event) {
      _onRefresh();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _tabController.dispose();
    investmentSuccessSubscription.cancel();
    userNodeUpgradeSubscription.cancel();
    redeemApplySubscription.cancel();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    if (currentTabIndex == 0) {
      try {
        final res = await InvestmentApi.getFundInvestmentRecordList({
          'pageNum': 1,
          'pageSize': 100,
        });
        portfolioList = res.list;
        isPortfolioEmpty = res.isEnd && res.list.isEmpty;
      } catch (e) {}
      if (!mounted) {
        return;
      }
      setState(() {});
      _controller.finishRefresh();
    } else {
      try {
        setState(() {
          isTeamLoading = true;
          isTeamEmpty = false;
        });
        final res = await UserApi.getUserNodeLevelList({
          'pageNum': 1,
          'pageSize': 500,
        });
        teamList = res.list;
        isTeamEmpty = res.isEnd && res.list.isEmpty;
      } catch (e) {
        e.printError();
      }
      isTeamLoading = false;
      if (!mounted) {
        return;
      }
      setState(() {});
      _controller.finishRefresh();
    }
  }

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = CommonUtils.getStatusBarHeight(context);
    final showTeam = (context.watch<AuthProvider>().user?.nodeLevel ?? 1) >= 2;

    return Scaffold(
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        refreshOnStart: true,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: statusBarHeight + 19.h),
              _userCard(),
              SizedBox(height: 23.h),
              LineCard(),
              SizedBox(height: 10.h),
              if (showTeam) _twoContent() else _oneContent(),
            ],
          ),
        ),
      ),
    );
  }

  _twoContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _tabs(),
        SizedBox(height: 13.h),
        if (currentTabIndex == 0) ..._portfolioList(),
        if (currentTabIndex == 0)
          PrimaryButton(
            title: 'activate_strategy'.tr,
            margin: EdgeInsets.only(bottom: 22.h, top: 5.h),
            onPress: () {
              EventBusUtil.fireSwitchTab(SwitchTabEvent(tabIndex: 2));
            },
          ),
        if (currentTabIndex == 1) _teamContent(),
      ],
    );
  }

  _teamContent() {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 14.h),
          child: TextButton(
            onPressed: () {
              Get.toNamed(Routes.earningsTracker);
            },
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 8.5.w, vertical: 6.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3.w),
                side: BorderSide(color: customTheme.buttonBgColor),
              ),
            ),
            child: Text(
              "earning_tracker".tr,
              style: TextStyle(
                color: customTheme.buttonBgColor,
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        TeamTable(
          teamList: teamList,
          loading: isTeamLoading,
          isEmpty: isTeamEmpty,
        ),
      ],
    );
  }

  Widget _tabs() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    // 获取当前文字方向，如果是从右到左，则需要将TabBar的labelPadding设置为0
    final textDirection = Directionality.of(context);
    final labelPadding = textDirection == TextDirection.rtl
        ? EdgeInsets.only(left: 54.w)
        : EdgeInsets.only(right: 54.w);

    return Padding(
      padding: EdgeInsets.only(top: 7.h),
      child: TabBar(
        controller: _tabController,
        onTap: (index) {
          setState(() {
            currentTabIndex = index;
            if (index == 0 && portfolioList.isEmpty) {
              _onRefresh();
            } else if (index == 1 && teamList.isEmpty) {
              _onRefresh();
            }
          });
        },
        indicatorWeight: 3.w,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 6.w),
        isScrollable: true,
        dividerHeight: 0.5,
        tabAlignment: TabAlignment.start,
        dividerColor: customTheme.dividerColor,
        labelPadding: labelPadding,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.lightTextColor,
          fontWeight: FontWeight.w600,
        ),
        tabs: [
          Tab(child: Text('my_portfolio'.tr)),
          Tab(child: Text('team_analytics'.tr)),
        ],
      ),
    );
  }

  _oneContent() {
    return isPortfolioEmpty ? _empty() : _content();
  }

  _content() {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'my_portfolio'.tr,
          style: TextStyle(
            fontSize: 17.sp,
            fontWeight: FontWeight.w600,
            color: customTheme.textColor,
          ),
        ),
        SizedBox(height: 12.h),
        ..._portfolioList(),
        SizedBox(height: 5.h),
        PrimaryButton(
          title: 'Activate Strategy',
          onPress: () {
            EventBusUtil.fireSwitchTab(SwitchTabEvent(tabIndex: 2));
          },
        ),
        SizedBox(height: 22.h),
      ],
    );
  }

  Widget _userCard() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final authInfo = context.watch<AuthProvider>();
    final user = authInfo.user;
    final isKyc = authInfo.isKyc;

    return Container(
      height: 28.w,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.5.w),
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(6.5.w),
      ),
      child: Wrap(
        spacing: 8.w,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Text(
            'UID: ${user?.userId ?? '-'}',
            style: TextStyle(fontSize: 12.sp, color: customTheme.subTextColor),
          ),
          Text(
            isKyc ? 'verified'.tr : 'unverified'.tr,
            style: TextStyle(
              fontSize: 12.sp,
              color: isKyc ? customTheme.ff7ed032 : customTheme.downColor,
            ),
          ),
        ],
      ),
    );
  }

  _portfolioList() {
    return List.generate(
      portfolioList.length,
      (index) => _portfolioItem(index),
    );
  }

  Widget _portfolioItem(int index) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    final item = portfolioList[index];

    var edgeInsets = EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.w);
    var textStyle = TextStyle(fontSize: 12.sp, color: customTheme.textColor);
    var boxDecoration = BoxDecoration(
      color: customTheme.primaryColor,
      borderRadius: BorderRadius.circular(9.5.w),
    );

    var textStyle2 = TextStyle(
      fontSize: 15.sp,
      color: customTheme.textColor,
      fontWeight: FontWeight.bold,
    );
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 12.w),
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(3.w),
        gradient: LinearGradient(
          colors: [customTheme.ff363C64, customTheme.inputBgColor],
          stops: [0.0, 0.23],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        image: DecorationImage(
          image: AssetImage(customThemeImage.portfolioCardBg),
          alignment: Alignment.topCenter,
          fit: BoxFit.fitWidth,
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 16.5.w,
      ).copyWith(top: 18.w, bottom: 28.w),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          Get.toNamed(
            Routes.myInvestment,
            parameters: {'productId': item.productId, 'name': item.productName},
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.productName,
              style: TextStyle(
                fontSize: 18.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 11.5.w),
            Wrap(
              spacing: 4.w,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Container(
                  decoration: boxDecoration,
                  padding: edgeInsets,
                  child: Text('${item.activeCount} Active', style: textStyle),
                ),
                Container(
                  decoration: boxDecoration.copyWith(
                    color: customTheme.portfolioRedeemedBgColor,
                  ),
                  padding: edgeInsets,
                  child: Text(
                    '${item.redeemedCount} Redeemed',
                    style: textStyle,
                  ),
                ),
              ],
            ),
            SizedBox(height: 19.5.w),
            Divider(color: customTheme.dividerColor, height: 0.5),
            SizedBox(height: 18.5.w),
            Text(
              'Overall Investment',
              style: textStyle.copyWith(color: customTheme.labelColor),
            ),
            SizedBox(height: 6.w),
            Text(
              '\$${item.totalInvested.toString().fmt}',
              style: TextStyle(
                fontSize: 31.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 15.5.w),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 6.w,
                  children: [
                    Text(
                      'Overall Returns',
                      style: textStyle.copyWith(color: customTheme.labelColor),
                    ),
                    Text(
                      '\$${item.totalReturns.toString().fmt}',
                      style: textStyle2,
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  spacing: 6.w,
                  children: [
                    Text(
                      'Redeemed Amount',
                      style: textStyle.copyWith(color: customTheme.labelColor),
                    ),
                    Text(
                      '\$${item.totalRedeemed.toString().fmt}',
                      style: textStyle2,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _empty() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        children: [
          SizedBox(height: 160.w),
          Image.asset(customThemeImage.commonEmpty1, width: 330.w),
          SizedBox(height: 12.w),
          Text(
            'No record found',
            style: TextStyle(
              fontSize: 13.sp,
              color: customTheme.subTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 20.w),
        ],
      ),
    );
  }
}
