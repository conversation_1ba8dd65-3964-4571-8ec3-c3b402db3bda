import 'package:json_annotation/json_annotation.dart';

part 'question_option_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class QuestionOptionRes {
  QuestionOptionRes();

  late String title;
  late bool needExtra;
  String? extraPrompt;
  bool? extraRequired;
  String extraContent = '';
  bool isSelected = false;

  factory QuestionOptionRes.fromJson(Map<String, dynamic> json) =>
      _$QuestionOptionResFromJson(json);
  Map<String, dynamic> toJson() => _$QuestionOptionResToJson(this);
}
