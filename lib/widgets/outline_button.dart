import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class OutlineButton extends StatelessWidget {
  final VoidCallback? onPress;
  final String title;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? height;
  final double? borderRadius;
  final bool isInline;
  final Color? backgroundColor;
  final Color? color;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Widget? icon;

  const OutlineButton({
    super.key,
    this.onPress,
    required this.title,
    this.margin,
    this.padding,
    this.height,
    this.borderRadius,
    this.isInline = false,
    this.backgroundColor,
    this.color,
    this.fontSize,
    this.fontWeight,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    return Container(
      height: height ?? 51.w,
      width: !isInline ? double.infinity : null,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius ?? 3.w),
        border: Border.all(color: color ?? customTheme.buttonBgColor),
      ),
      clipBehavior: Clip.hardEdge,
      child: TextButton(
        onPressed: onPress,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          padding: padding ?? EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 3.w),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Offstage(offstage: icon == null, child: icon),
            Text(
              title,
              style: TextStyle(
                color: color ?? customTheme.buttonBgColor,
                fontSize: fontSize ?? 14.sp,
                fontWeight: fontWeight ?? FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
