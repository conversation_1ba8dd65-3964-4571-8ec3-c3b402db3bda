import 'dart:async';

import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/models/jour_list_item.dart';
import 'package:aurenixai_app/pages/account/children/funding/widgets/add_funds_modal.dart';
import 'package:aurenixai_app/pages/account/children/funding/widgets/sending_modal.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class FundingAccountPage extends StatefulWidget {
  const FundingAccountPage({super.key});

  @override
  _FundingAccountPageState createState() => _FundingAccountPageState();
}

class _FundingAccountPageState extends State<FundingAccountPage> {
  late StreamSubscription<BindGoogleSuccessEvent>
  _bindGoogleSuccessSubscription;
  late StreamSubscription<UnbindGoogleSuccessEvent>
  _unbindGoogleSuccessSubscription;
  late EasyRefreshController _controller;

  String isVerified = '0';

  AccountRes? accountData;

  List<JourListItem> jourList = [];

  bool isEnd = false;

  /// 用户是否设置google验证器(1是0否)
  bool userGoogleSecretFlag = false;

  ///是否需要设置google验证器(1是0否)
  bool needGoogleSecretFlag = false;

  @override
  void initState() {
    super.initState();
    _bindGoogleSuccessSubscription = EventBusUtil.listenBindGoogleSuccess((
      event,
    ) {
      _checkGoogleSecret();
    });
    _unbindGoogleSuccessSubscription = EventBusUtil.listenUnbindGoogleSuccess((
      event,
    ) {
      _checkGoogleSecret();
    });

    _controller = EasyRefreshController(controlFinishRefresh: true);
    _onRefresh();
  }

  @override
  void dispose() {
    _bindGoogleSuccessSubscription.cancel();
    _unbindGoogleSuccessSubscription.cancel();
    _controller.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    await Future.wait([_checkGoogleSecret(), _getMyAccount(), _getJourList()]);
    _controller.finishRefresh(IndicatorResult.success);
  }

  Future<void> _checkGoogleSecret() async {
    try {
      final res = await UserApi.userCheckGoogleSecret();
      setState(() {
        userGoogleSecretFlag = res.userGoogleSecretFlag == '1';
        needGoogleSecretFlag = res.needGoogleSecretFlag == '1';
      });
    } catch (e) {}
  }

  Future<void> _getMyAccount() async {
    try {
      final res = await AccountApi.getMyAccount();
      setState(() {
        accountData = res;
      });
    } catch (e) {}
  }

  Future<void> _getJourList() async {
    try {
      final res = await AccountApi.getJourList({
        "pageNum": 1,
        "pageSize": 10,
        "category": '0',
      });
      setState(() {
        jourList = res.list;
        isEnd = res.isEnd;
      });
    } catch (e) {}
  }

  bool get isEmpty {
    return isEnd && jourList.isEmpty;
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    final authInfo = context.watch<AuthProvider>();
    final user = authInfo.user;

    if (user == null) {
      return SizedBox.shrink();
    }
    return Scaffold(
      appBar: AppBar(title: Text('Funding_Account'.tr)),
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 19.w, left: 15.w, right: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF282835),
                  borderRadius: BorderRadius.circular(7.w),
                ),
                padding: EdgeInsets.symmetric(vertical: 6.w, horizontal: 13.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'UID: ${user.userId}',
                      style: TextStyle(fontSize: 12.w, color: Colors.white70),
                    ),
                    SizedBox(width: 8.w),
                    authInfo.isKyc
                        ? Text(
                            'Verified'.tr,
                            style: TextStyle(
                              fontSize: 12.w,
                              color: const Color(0xFF7ED032),
                            ),
                          )
                        : Text(
                            'Unverified'.tr,
                            style: TextStyle(
                              fontSize: 12.w,
                              color: const Color(0xFFD74545),
                            ),
                          ),
                  ],
                ),
              ),
              SizedBox(height: 16.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total_Balance'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                  Text(
                    'Withdrawal_Balance'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '\$${accountData?.amount ?? ''}',
                    style: TextStyle(
                      fontSize: 25.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  Text(
                    '\$${accountData?.availableAmount ?? ''}',
                    style: TextStyle(
                      fontSize: 25.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 26.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: OutlineButton(
                      title: 'Add_Funds'.tr,
                      height: 43.w,
                      onPress: () {
                        AddFundsModal.showModal(context);
                      },
                    ),
                  ),
                  SizedBox(width: 11.w),
                  Expanded(
                    child: Stack(
                      clipBehavior: Clip.none, // 允许溢出显示
                      children: [
                        PrimaryButton(
                          title: 'Send'.tr,
                          height: 43.w,
                          onPress: () {
                            if (needGoogleSecretFlag && !userGoogleSecretFlag) {
                              Get.toNamed(Routes.bindGoogle);
                            } else {
                              SendingModal.showModal(context);
                            }
                          },
                        ),
                        if (needGoogleSecretFlag && !userGoogleSecretFlag)
                          Positioned(
                            top: -20.w,
                            right: 0, // 贴右上角
                            child: Container(
                              height: 31.w,
                              padding: EdgeInsets.symmetric(horizontal: 6.w),
                              decoration: BoxDecoration(
                                color: customTheme.inputBgColor,
                                borderRadius: BorderRadius.circular(10.w),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(81),
                                    blurRadius: 8.w,
                                    offset: Offset(0, 0),
                                    blurStyle: BlurStyle.outer,
                                  ),
                                ],
                              ),
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Image.asset(
                                    customThemeImage.homeFail,
                                    width: 18.w,
                                    height: 18.w,
                                  ),
                                  SizedBox(width: 3.w),
                                  Text(
                                    'Google 2FA', // 或 KYC Fail
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: customTheme.textColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24.w),
              Text(
                'Transaction_History'.tr,
                style: TextStyle(
                  fontSize: 16.w,
                  fontWeight: FontWeight.w600,
                  color: customTheme.textColor,
                ),
              ),
              SizedBox(height: 14.w),
              Divider(color: customTheme.dividerColor, height: 0.5),
              SizedBox(height: 11.w),
              if (jourList.isNotEmpty)
                ...jourList.map(
                  (item) => _buildItem(
                    info: item,
                    customTheme: customTheme,
                    customThemeImage: customThemeImage,
                  ),
                ),
              if (isEmpty) _empty(),
              SizedBox(height: 10.w),

              if (jourList.length > 10)
                GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.transactionHistoryFunding);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'See_More'.tr,
                        style: TextStyle(
                          fontSize: 16.w,
                          fontWeight: FontWeight.w600,
                          color: customTheme.textColor,
                        ),
                      ),
                      Image.asset(
                        customThemeImage.commonIconRight,
                        width: 14.w,
                        height: 14.w,
                        fit: BoxFit.fill,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItem({
    required JourListItem info,
    required CustomThemeSwatch customTheme,
    required CustomThemeImage customThemeImage,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.transactionDetail, parameters: {'id': info.id});
      },
      child: Container(
        padding: EdgeInsets.only(top: 11.w, bottom: 11.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              info.description == '1'
                  ? customThemeImage.homeAdd
                  : customThemeImage.homeReduce,
              width: 22.w,
              height: 22.w,
              fit: BoxFit.fill,
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    DateTime.fromMillisecondsSinceEpoch(
                      info.createDatetime as int,
                    ).yyyyMMddHHmmEEE,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                  Text(
                    info.remark,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  if (info.code != null)
                    Text(
                      info.code ?? '',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(width: 10.w),
            Column(
              children: [
                Text(
                  '\$ ${info.transAmount}',
                  style: TextStyle(
                    fontSize: 15.w,
                    fontWeight: FontWeight.w600,
                    color: customTheme.textColor,
                  ),
                ),
                info.status == '0'
                    ? Text(
                        'Pending',
                        style: TextStyle(
                          fontSize: 12.w,
                          fontWeight: FontWeight.w400,
                          color: customTheme.primaryColor,
                        ),
                      )
                    : SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _empty() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Column(
      children: [
        SizedBox(height: 60.w),
        Image.asset(customThemeImage.commonEmpty1, width: 330.w),
        SizedBox(height: 12.w),
        Text(
          'no_record_found'.tr,
          style: TextStyle(
            fontSize: 13.sp,
            color: customTheme.subTextColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 60.w),
      ],
    );
  }
}
