import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/theme/theme_dark.dart' show ThemeDark;
import 'package:aurenixai_app/utils/theme/theme_light.dart' show ThemeLight;
import 'package:flutter/material.dart';
import 'package:get/get.dart';

const normalPrimaryColor = Color(0xFF012645);
final normalPrimarySwatch = CommonUtils.createMaterialColor(normalPrimaryColor);

class ThemeUtil {
  static Future<void> init(ThemeProvider themeProvider) async {
    try {
      final type = await LocalUtil.getThemeType();
      themeProvider.currentThemeType = type;
    } catch (e) {
      themeProvider.currentThemeType = ThemeType.dark;
    }
  }

  static ThemeType themeTypeFromString(String str) {
    return ThemeType.values.firstWhere(
      (e) => e.toString() == str,
      orElse: () => ThemeType.dark,
    );
  }

  static final Map<ThemeType, Map<String, dynamic>> defaultTheme = {
    ThemeType.light: ThemeLight.theme,
    ThemeType.dark: ThemeDark.theme,
  };

  static final Map<ThemeType, String> defaultThemeName = {
    ThemeType.light: "白天模式".tr,
    ThemeType.dark: '黑夜模式'.tr,
  };
}
