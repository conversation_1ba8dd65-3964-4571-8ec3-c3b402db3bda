import 'package:aurenixai_app/models/navigate.dart';
import 'package:aurenixai_app/pages/public/rich_text.dart';
import 'package:aurenixai_app/pages/public/webview.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/common_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class InsightsCard extends StatefulWidget {
  final List<Navigate> bannerList;
  const InsightsCard({super.key, required this.bannerList});

  @override
  _InsightsCardState createState() => _InsightsCardState();
}

class _InsightsCardState extends State<InsightsCard> {
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return widget.bannerList.isNotEmpty
        ? Padding(
            padding: EdgeInsets.only(bottom: 20.h),
            child: Column(
              spacing: 10.w,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  spacing: 6.w,
                  children: [
                    Text(
                      'insights'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    _pagination(),
                  ],
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(3.w),
                  child: SizedBox(
                    height: 115.w,
                    child: CarouselSlider(
                      items: widget.bannerList
                          .map(
                            (item) => ClipRRect(
                              borderRadius: BorderRadius.circular(3.w),
                              child: GestureDetector(
                                onTap: () {
                                  // item.action: 0 不能点击, 1、跳转至图文详情, 2 跳转至外链
                                  if (item.url == null || item.action == '0') {
                                    return;
                                  }
                                  if (item.action == '2') {
                                    // 外链
                                    if (item.url!.startsWith('http')) {
                                      Get.to(
                                        () => WebviewPage(
                                          title: item.name,
                                          url: item.url!,
                                        ),
                                      );
                                    }
                                    return;
                                  }
                                  if (item.action == '1') {
                                    Get.to(
                                      () => RichTextPage(
                                        title: item.name,
                                        content: item.url!,
                                      ),
                                    );
                                  }
                                },
                                child: CommonImage(
                                  item.pic,
                                  width: 345.w,
                                  height: 115.w,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                      options: CarouselOptions(
                        autoPlay: widget.bannerList.length > 1,
                        enableInfiniteScroll: widget.bannerList.length > 1,
                        viewportFraction: 1,
                        height: 115.w,
                        onPageChanged: (index, reason) {
                          setState(() {
                            currentIndex = index;
                          });
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _pagination() {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: 8.w,
      children: List.generate(widget.bannerList.length, (index) {
        bool isCurrent = index == currentIndex;
        return Container(
          height: 5.w,
          width: 5.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isCurrent
                ? customTheme.textColor
                : customTheme.textColor.withValues(alpha: 0.4),
          ),
        );
      }),
    );
  }
}
