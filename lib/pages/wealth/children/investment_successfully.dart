import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class InvestmentSuccessfullyPage extends StatefulWidget {
  const InvestmentSuccessfullyPage({super.key});

  @override
  _InvestmentSuccessfullyPageState createState() =>
      _InvestmentSuccessfullyPageState();
}

class _InvestmentSuccessfullyPageState
    extends State<InvestmentSuccessfullyPage> {
  void onViewMyInvestment() {
    Navigator.of(context).popUntil((route) {
      return !route.willHandlePopInternally &&
          route is ModalRoute &&
          !(route.settings.name?.contains(Routes.investmentSuccessfully) ??
              false) &&
          !(route.settings.name?.contains(Routes.investmentSignature) ??
              false) &&
          !(route.settings.name?.contains(Routes.innvestmentAmount) ?? false) &&
          !(route.settings.name?.contains(Routes.investment) ?? false);
    });
    EventBusUtil.fireSwitchTab(SwitchTabEvent(tabIndex: 1));
  }

  String getCycleText(num interestCycleDays) {
    if (interestCycleDays == 1) {
      return 'daily'.tr;
    } else if (interestCycleDays == 7) {
      return 'weekly'.tr;
    } else if (interestCycleDays == 30) {
      return 'monthly'.tr;
    } else {
      return 'per_xx_days'.trParams({'days': interestCycleDays.toString()});
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final amount = Get.parameters['amount'] ?? '';
    final productName = Get.parameters['name'] ?? '';
    final interestRatePerCycle = num.parse(
      Get.parameters['interestRatePerCycle'] ?? '0',
    );
    final interestCycleDays = num.parse(
      Get.parameters['interestCycleDays'] ?? '7',
    );
    final lockinPeriodDays = num.parse(
      Get.parameters['lockinPeriodDays'] ?? '30',
    );

    final createDatetime = int.tryParse(Get.parameters['createDatetime'] ?? '');
    final subscriptionDate = createDatetime != null
        ? DateTime.fromMillisecondsSinceEpoch(createDatetime)
        : null;

    final nextInterestDate = int.tryParse(
      Get.parameters['nextInterestDate'] ?? '',
    );
    final nextInvestmentReturnDate = nextInterestDate != null
        ? DateTime.fromMillisecondsSinceEpoch(nextInterestDate)
        : null;

    return Scaffold(
      appBar: AppBar(title: Text('details'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          children: [
            SizedBox(height: 37.h),
            Image.asset(
              customThemeImage.wealthMoney,
              width: 88.w,
              height: 90.w,
              fit: BoxFit.fill,
            ),
            SizedBox(height: 29.5.h),
            Text(
              'investment_confirmed'.tr,
              style: TextStyle(
                fontSize: 20.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.5.h),
            Text.rich(
              textAlign: TextAlign.center,
              TextSpan(
                style: TextStyle(fontSize: 14.sp, color: customTheme.textColor),
                children: [
                  TextSpan(text: "you_ve_successfully_invested".tr),
                  TextSpan(
                    text: " \$ ${amount.fmt} ",
                    style: TextStyle(color: customTheme.primaryColor),
                  ),
                  TextSpan(text: "${"in".tr} "),
                  TextSpan(
                    text: productName,
                    style: TextStyle(color: customTheme.primaryColor),
                  ),
                  // TextSpan(text: "."),
                ],
              ),
            ),
            SizedBox(height: 37.h),
            Container(
              decoration: BoxDecoration(
                color: customTheme.ff1f1f2a,
                borderRadius: BorderRadius.circular(3.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: Column(
                children: [
                  // 1、1天展示daily；2、7天展示weekly; 3、30天展示monthly；4、其它的就展示 per xx days
                  _line(
                    'projected_returns'.tr,
                    '~0–${interestRatePerCycle.toString().removeZero}% ${getCycleText(interestCycleDays)}*',
                  ),
                  Divider(height: 0.5),
                  // 1天显示1 day，其他显示xx days
                  _line(
                    'lock_in_period'.tr,
                    '$lockinPeriodDays ${lockinPeriodDays > 1 ? 'days'.tr : 'day'.tr}',
                  ),
                  Divider(height: 0.5),
                  _line(
                    'subscription_date'.tr,
                    subscriptionDate?.yyyyMMddHHmm ?? '-',
                  ),
                  Divider(height: 0.5),
                  _line(
                    'next_allocation_return_date'.tr,
                    nextInvestmentReturnDate?.yyyyMMddHHmm ?? '-',
                  ),
                ],
              ),
            ),
            SizedBox(height: 55.5.h),
            PrimaryButton(
              title: 'view_my_investment'.tr,
              onPress: onViewMyInvestment,
            ),
          ],
        ),
      ),
    );
  }

  Widget _line(String title, String value) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 17.h),
      child: Row(
        spacing: 4.w,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 12.sp, color: customTheme.labelColor),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13.sp,
              color: customTheme.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
