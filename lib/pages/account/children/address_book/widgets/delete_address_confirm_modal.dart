import 'package:aurenixai_app/api/address_api.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:provider/provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';

class DeleteAddressConfirmModal extends StatefulWidget {
  final List<String> idList;

  /// 确认回调
  final void Function() onConfirm;

  const DeleteAddressConfirmModal({
    super.key,
    required this.idList,
    required this.onConfirm,
  });

  static Future<T?> showModal<T>({
    required BuildContext context,
    required List<String> idList,
    required void Function() onConfirm,
  }) async {
    return showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return DeleteAddressConfirmModal(idList: idList, onConfirm: onConfirm);
      },
    );
  }

  @override
  State<DeleteAddressConfirmModal> createState() =>
      _DeleteAddressConfirmModalState();
}

class _DeleteAddressConfirmModalState extends State<DeleteAddressConfirmModal> {
  void onConfirm() async {
    try {
      ToastUtil.showLoading();
      await AddressApi.addressDeleteBatch({'ids': widget.idList});
      ToastUtil.dismiss();
      widget.onConfirm();
      Navigator.of(context).pop();
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.w)),
      insetPadding: EdgeInsets.symmetric(horizontal: 26.w),
      child: Container(
        width: 323.w,
        padding: EdgeInsets.fromLTRB(32.w, 22.w, 17.w, 28.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.w),
          color: customTheme.inputBgColor,
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(right: 10.w, top: 15.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'delete_payee'.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 34.w),
                  Text(
                    "delete_payee_tips1".tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.hintTextColor,
                    ),
                  ),

                  Text(
                    "delete_payee_tips2".tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                  SizedBox(height: 34.w),
                  Row(
                    spacing: 9.w,
                    children: [
                      Expanded(
                        child: OutlineButton(
                          title: 'cancel'.tr,
                          fontSize: 13.sp,
                          onPress: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      Expanded(
                        child: PrimaryButton(
                          title: 'confirm'.tr,
                          fontSize: 13.sp,
                          onPress: onConfirm,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: IconButton.styleFrom(
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                ),
                iconSize: 26.w,
                icon: Icon(Icons.close_rounded, color: customTheme.textColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
