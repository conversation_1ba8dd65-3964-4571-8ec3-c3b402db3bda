import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/models/jour_item_res.dart';
import 'package:aurenixai_app/models/jour_list_item.dart';
import 'package:aurenixai_app/models/page_info.dart';
import 'package:aurenixai_app/utils/http_util.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:get/get.dart';

class AccountApi {
  static Future<AccountRes?> getMyAccount() async {
    try {
      final res = await HttpUtil.post("/core/v1/account/my_account");
      if (res != null) {
        return AccountRes.fromJson(CommonUtils.removeNullKeys(res));
      }
      return null;
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 分页查询我的账户流水
  static Future<PageInfo<JourListItem>> getJourList(
    Map<String, dynamic> data,
  ) async {
    try {
      final res = await HttpUtil.post('/core/v1/jour/page_front', data);
      return PageInfo.fromJson<JourListItem>(res, JourListItem.fromJson);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 分页查询我的账户历史流水
  static Future<PageInfo<JourListItem>> getJourHistoryList(
    Map<String, dynamic> data,
  ) async {
    try {
      final res = await HttpUtil.post('/core/v1/jour/page_front_history', data);
      return PageInfo.fromJson<JourListItem>(res, JourListItem.fromJson);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取流水详情
  static Future<JourItemRes> getActivityDetail(String id) async {
    try {
      final res = await HttpUtil.post("/core/v1/jour/detail_front/$id");
      return JourItemRes.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 新增提币订单
  static Future<void> withdrawCreate(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/withdraw/create", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 转账申请
  static Future<void> transferOrderApply(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/transfer_order/apply", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 佣金钱包转入现金钱包
  static Future<void> commissionToFundling(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/account/commission_to_fundling", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取流水详情
  static Future<JourItemRes[]> getWithdrawRuleList(
    Map<String, dynamic> data,
  ) async {
    try {
      final res = await HttpUtil.post(
        "/core/v1/withdraw_rule/public/list_front",
        data,
      );
      return JourItemRes.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }
}
