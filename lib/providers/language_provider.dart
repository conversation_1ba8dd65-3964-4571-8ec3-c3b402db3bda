import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

/// 语言和地区选择
class LanguageProvider with ChangeNotifier {
  Locale? _locale;
  Region? _region;
  List<Region> _regionList = [];

  Locale? get locale {
    return _locale;
  }

  Region? get region {
    return _region;
  }

  List<Region> get regionList {
    return _regionList;
  }

  setRegionList(List<Region> regionList) {
    _regionList = regionList;
    notifyListeners();
  }

  Future<List<Region>> getRegionList() async {
    final regionList = await CommonApi.getRegionList();
    setRegionList(regionList);
    return regionList;
  }

  /// 设置语言和地区
  setLanguageAndRegion(Locale locale, Region region) {
    _locale = locale;
    _region = region;
    LocalUtil.setLocale(locale);
    LocalUtil.setRegion(region);
    Get.updateLocale(locale);
    notifyListeners();
  }

  /// 清除语言和地区
  clear() {
    _locale = null;
    _region = null;
    LocalUtil.removeLocale();
    LocalUtil.removeRegion();
    notifyListeners();
  }
}
