<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Flutter Web App</title>
  <meta name="description" content="A new Flutter project.">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- 防止缩放 -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">

  <!-- 引入 flutter.js -->
  <script src="flutter.js" defer></script>

  <!-- 可选：设置启动时的 loading 样式 -->
  <style>
    body {
      margin: 0;
      background: #ffffff;
      overflow: hidden;
    }

    #flutter-loader {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: white;
      z-index: 9999;
    }

    .loader {
      width: 40px;
      height: 40px;
      border: 5px solid #ccc;
      border-top-color: #0175c2;
      border-radius: 50%;
      animation: spin 1s infinite linear;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  </style>
</head>

<body>
  <div id="flutter-loader">
    <div class="loader"></div>
  </div>

  <script>
    window.addEventListener('load', function (ev) {
      // 使用新版 FlutterLoader API
      const loader = new FlutterLoader();
      loader.load({
        entrypointUrl: "main.dart.js",  // 默认入口文件
      }).then(function (engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function (appRunner) {
        return appRunner.runApp();
      });
    });
  </script>
</body>

</html>