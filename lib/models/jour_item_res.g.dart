// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jour_item_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JourItemRes _$JourItemResFromJson(Map<String, dynamic> json) => JourItemRes()
  ..id = json['id'] as String
  ..transAmount = json['transAmount'] as num
  ..status = json['status'] as String
  ..createDatetime = json['createDatetime'] as num
  ..description = json['description'] as String?
  ..remark = json['remark'] as String?
  ..code = json['code'] as String?
  ..network = json['network'] as String?
  ..coupon = json['coupon'] as String?
  ..reason = json['reason'] as String?
  ..address = json['address'] as String?
  ..category = json['category'] as String?
  ..categoryValue = json['categoryValue'] as String?
  ..transferMethod = json['transferMethod'] as String?
  ..bizCategory = json['bizCategory'] as String?
  ..type = json['type'] as String?
  ..bizType = json['bizType'] as String?;

Map<String, dynamic> _$JourItemResToJson(JourItemRes instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'transAmount': instance.transAmount,
      'status': instance.status,
      'remark': instance.remark,
      'createDatetime': instance.createDatetime,
      'code': instance.code,
      'network': instance.network,
      'coupon': instance.coupon,
      'reason': instance.reason,
      'address': instance.address,
      'category': instance.category,
      'categoryValue': instance.categoryValue,
      'transferMethod': instance.transferMethod,
      'bizCategory': instance.bizCategory,
      'type': instance.type,
      'bizType': instance.bizType,
    };
