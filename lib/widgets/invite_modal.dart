import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:qr_flutter/qr_flutter.dart';

class InviteModal extends StatefulWidget {
  const InviteModal({super.key});

  static Future<T?> showModal<T>({required BuildContext context}) async {
    return showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return InviteModal();
      },
    );
  }

  @override
  State<InviteModal> createState() => _InviteModalState();
}

class _InviteModalState extends State<InviteModal> {
  bool isCodeCopied = false;
  bool isLinkCopied = false;

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final user = context.read<AuthProvider>().user;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.w)),
      insetPadding: EdgeInsets.symmetric(horizontal: 26.w),
      child: Container(
        width: 323.w,
        padding: EdgeInsets.fromLTRB(27.w, 22.w, 17.w, 28.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: customTheme.inputBgColor,
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(right: 10.w, top: 9.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Invite via QR Code',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 33.w),
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        width: 175.w,
                        height: 175.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.w),
                          gradient: LinearGradient(
                            colors: [
                              customTheme.ff40e2fe,
                              customTheme.primaryColor,
                            ],
                            begin: Alignment.bottomRight,
                            end: Alignment.topLeft,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: customTheme.primaryColor,
                              blurRadius: 4.w,
                              offset: Offset(0, 0),
                              blurStyle: BlurStyle.outer,
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 175.w - 4,
                        height: 175.w - 4,
                        decoration: BoxDecoration(
                          color: customTheme.inputBgColor,
                          borderRadius: BorderRadius.circular(10.w),
                        ),
                        padding: EdgeInsets.all(17.w),
                        child: QrImageView(
                          data: 'https://www.google.com',
                          version: QrVersions.auto,
                          size: 139.w,
                          padding: EdgeInsets.all(9.5.w),
                          backgroundColor: customTheme.textColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 23.w),
                  Text(
                    'UID: ${user?.userId}',
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 19.5.w),
                  Container(
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: customTheme.pageBgColor,
                      borderRadius: BorderRadius.circular(3.w),
                    ),
                    padding: EdgeInsets.only(left: 12.5.w, right: 11.w),
                    child: Row(
                      spacing: 4.w,
                      children: [
                        Expanded(
                          child: Text(
                            'Invitation Code: ${user?.inviteCode}',
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: customTheme.textColor,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Clipboard.setData(
                              ClipboardData(text: user?.inviteCode ?? ''),
                            ).then((value) {
                              setState(() {
                                isCodeCopied = true;
                              });
                              Future.delayed(Duration(seconds: 2), () {
                                isCodeCopied = false;
                                if (mounted) {
                                  setState(() {});
                                }
                              });
                            });
                          },
                          child: Image.asset(
                            isCodeCopied
                                ? customThemeImage.authVerification
                                : customThemeImage.homeCopy,
                            width: 20.w,
                            height: 20.w,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 23.w),
                  PrimaryButton(
                    title: 'Copy Link',
                    borderRadius: 9.w,
                    width: 167.w,
                    icon: Padding(
                      padding: EdgeInsets.only(right: 5.5.w),
                      child: Image.asset(
                        isLinkCopied
                            ? customThemeImage.authVerification
                            : customThemeImage.homeLink,
                        width: 22.w,
                        height: 22.w,
                      ),
                    ),
                    onPress: () {
                      Clipboard.setData(
                        ClipboardData(text: 'https://www.google.com'),
                      ).then((value) {
                        setState(() {
                          isLinkCopied = true;
                        });
                        Future.delayed(Duration(seconds: 2), () {
                          if (mounted) {
                            setState(() {
                              isLinkCopied = false;
                            });
                          }
                        });
                      });
                    },
                  ),
                ],
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: IconButton.styleFrom(
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                ),
                iconSize: 26.w,
                icon: Icon(Icons.close_rounded, color: customTheme.textColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
