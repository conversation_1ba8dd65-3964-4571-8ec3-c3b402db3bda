import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/pages/account/widgets/confirm_google_code_modal.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SendChainConfirmPage extends StatefulWidget {
  const SendChainConfirmPage({super.key});

  @override
  _SendChainConfirmPageState createState() => _SendChainConfirmPageState();
}

class _SendChainConfirmPageState extends State<SendChainConfirmPage> {
  String? addressBookId;
  String? address;
  String? amount;
  String? addressType;
  String? trustFlag;
  String? emailVerificationCode;
  String? googleVerificationCode;
  String? currency;
  String? network;

  @override
  void initState() {
    super.initState();

    final args = Get.parameters;
    setState(() {
      addressBookId = args['addressBookId'] ?? '';
      address = args['address'] ?? '';
      amount = args['amount'] ?? '';
      currency = args['currency'] ?? '';
      network = args['network'] ?? '';
      trustFlag = args['trustFlag'] ?? '0';
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  void onConfirm() async {
    // 已授信
    if (trustFlag == '1') {
      _sendAmount();
    } else {
      ConfirmGoogleCodeModal.showModal(
        context,
        title: 'confirm_Withdrawal'.tr,
        tips:
            'To proceed with your withdrawal, please verify your identity by completing the following steps:'
                .tr,
        onConfirm: ({required emailCode, required googleCode}) {
          setState(() {
            emailVerificationCode = emailCode;
            googleVerificationCode = googleCode;
          });
          _sendAmount();
        },
      );
    }
  }

  void _sendAmount() async {
    try {
      ToastUtil.showLoading();
      await AccountApi.withdrawCreate({
        'currency': currency,
        'addressBookId': addressBookId,
        'address': address,
        'amount': amount,
        'addressType': addressType,
        'emailCode': emailVerificationCode,
        'googleCode': googleVerificationCode,
      });
      ToastUtil.dismiss();
      Get.back(result: {'success': true});
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('Confirm_Withdrawal'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                children: [
                  SizedBox(height: 25.w),
                  Image.asset(
                    customThemeImage.accountTransferUsdtddz,
                    width: 60.w,
                    height: 60.w,
                    fit: BoxFit.fill,
                  ),
                  SizedBox(height: 18.w),
                  Text(
                    'transfer_out_amount'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                  SizedBox(height: 12.w),
                  Text(
                    '$amount $currency',
                    style: TextStyle(
                      fontSize: 23.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 10.w),
                  Text(
                    'You will receive ≈ $amount $currency',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: customTheme.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 25.h),
                  Container(
                    decoration: BoxDecoration(
                      color: customTheme.ff1f1f2a,
                      borderRadius: BorderRadius.circular(7.w),
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: 6.w,
                      horizontal: 13.w,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        buildItem(
                          label: 'Currency'.tr,
                          value: '$currency',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Network'.tr,
                          value: '$network',
                          customTheme: customTheme,
                        ),

                        buildItem(
                          label: 'account_address'.tr,
                          value: '$address',
                          customTheme: customTheme,
                        ),

                        buildItem(
                          label: 'Fees'.tr,
                          value: '1 USDT',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Date'.tr,
                          value: DateTime.now().yyyyMMddHHmm,
                          customTheme: customTheme,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 17.h),
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: 11.w,
                      horizontal: 10.w,
                    ),
                    decoration: BoxDecoration(
                      color: customTheme.addFundsRechargeTipsBg,
                      borderRadius: BorderRadius.circular(9.w),
                      border: Border.all(
                        width: 1.w,
                        color: customTheme.primaryColor,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Image.asset(
                          customThemeImage.addFundsChainRechargeTips,
                          width: 23.w,
                          height: 23.w,
                          fit: BoxFit.fill,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            'send_chain_withdrawal_confirm_tips'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: customTheme.textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'Confirm_Withdrawal'.tr,
            onSubmit: onConfirm,
          ),
        ],
      ),
    );
  }

  Widget buildItem({
    required String label,
    required String value,
    required CustomThemeSwatch customTheme,
  }) {
    return Container(
      padding: EdgeInsets.only(top: 11.w, bottom: 11.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: customTheme.hintTextColor,
            ),
          ),
          SizedBox(width: 40.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: customTheme.textColor,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
