import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  _AccountPageState createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  late EasyRefreshController _controller;

  String isVerified = '0';

  AccountRes? accountData;

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(controlFinishRefresh: true);
    _onRefresh();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    await Future.wait([_getMyAccount()]);
    _controller.finishRefresh(IndicatorResult.success);
  }

  Future<void> _getMyAccount() async {
    try {
      final res = await AccountApi.getMyAccount();
      setState(() {
        accountData = res;
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    EdgeInsets padding = MediaQuery.of(context).padding;
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    final authInfo = context.watch<AuthProvider>();
    final user = authInfo.user;

    if (user == null) {
      return SizedBox.shrink();
    }
    return Scaffold(
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            top: padding.top + 19.w,
            left: 15.w,
            right: 15.w,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF282835),
                  borderRadius: BorderRadius.circular(7.w),
                ),
                padding: EdgeInsets.symmetric(vertical: 6.w, horizontal: 13.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'UID: ${user.userId}',
                      style: TextStyle(fontSize: 12.w, color: Colors.white70),
                    ),
                    SizedBox(width: 8.w),
                    authInfo.isKyc
                        ? Text(
                            'Verified'.tr,
                            style: TextStyle(
                              fontSize: 12.w,
                              color: const Color(0xFF7ED032),
                            ),
                          )
                        : Text(
                            'Unverified'.tr,
                            style: TextStyle(
                              fontSize: 12.w,
                              color: const Color(0xFFD74545),
                            ),
                          ),
                  ],
                ),
              ),
              SizedBox(height: 24.w),
              Text(
                'All_Accounts'.tr,
                style: TextStyle(
                  fontSize: 17.w,
                  fontWeight: FontWeight.w600,
                  color: customTheme.textColor,
                ),
              ),
              SizedBox(height: 18.w),
              buildItem1(customTheme, customThemeImage),
              SizedBox(height: 13.w),
              if (accountData?.rewardFlag == '1')
                buildItem2(customTheme, customThemeImage),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildItem1(
    CustomThemeSwatch customTheme,
    CustomThemeImage customThemeImage,
  ) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.funding);
      },
      child: Container(
        padding: EdgeInsets.only(
          top: 16.w,
          bottom: 22.w,
          left: 18.w,
          right: 18.w,
        ),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(customThemeImage.accountListBg1),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Funding_Account'.tr,
              style: TextStyle(
                fontSize: 14.w,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 13.w),
            Divider(height: 1.w),
            SizedBox(height: 9.w),
            Text(
              'Total_Available_Balance'.tr,
              style: TextStyle(fontSize: 12.w, color: Colors.white60),
            ),
            SizedBox(height: 6.w),
            Text(
              '\$${accountData?.amount ?? ''}',
              style: TextStyle(
                fontSize: 32.w,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            Container(
              padding: EdgeInsets.all(9.w),
              decoration: BoxDecoration(
                color: Colors.white10,
                borderRadius: BorderRadius.circular(2.w),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Withdrawal_Balance'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                  Text(
                    '\$${accountData?.availableAmount ?? ''}',
                    style: TextStyle(
                      fontSize: 19.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildItem2(
    CustomThemeSwatch customTheme,
    CustomThemeImage customThemeImage,
  ) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.commission);
      },
      child: Container(
        padding: EdgeInsets.only(
          top: 16.w,
          bottom: 22.w,
          left: 18.w,
          right: 18.w,
        ),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(customThemeImage.accountListBg2),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Commission_Account'.tr,
              style: TextStyle(
                fontSize: 14.w,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 13.w),
            Divider(height: 1.w),
            SizedBox(height: 9.w),
            Text(
              'Total_Balance'.tr,
              style: TextStyle(fontSize: 12.w, color: Colors.white60),
            ),
            SizedBox(height: 6.w),
            Text(
              '\$${accountData?.rewardAmount ?? ''}',
              style: TextStyle(
                fontSize: 32.w,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            Container(
              padding: EdgeInsets.all(9.w),
              decoration: BoxDecoration(
                color: Colors.white10,
                borderRadius: BorderRadius.circular(2.w),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Transferable_Balance'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                  Text(
                    '\$${accountData?.rewardAvailableAmount ?? ''}',
                    style: TextStyle(
                      fontSize: 19.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
