import 'package:aurenixai_app/models/address_book_res.dart';
import 'package:aurenixai_app/models/page_info.dart';
import 'package:aurenixai_app/utils/http_util.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:get/get.dart';

class AddressApi {
  /// 分页查询地址簿
  static Future<PageInfo<AddressBookRes>> getAddressBookList(
    Map<String, dynamic> data,
  ) async {
    try {
      final res = await HttpUtil.post('/core/v1/address_book/page_front', data);
      return PageInfo.fromJson<AddressBookRes>(res, AddressBookRes.fromJson);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取地址详情
  static Future<AddressBookRes> getAddressDetail(String id) async {
    try {
      final res = await HttpUtil.post("/core/v1/address_book/detail_front/$id");
      return AddressBookRes.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 新增地址
  static Future<void> addressAdd(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/address_book/add", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 修改备注
  static Future<void> addressUpdateRemark(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/address_book/update_remark", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 设为可信地址
  static Future<void> addressSetTrusted(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/address_book/set_trusted", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 批量删除
  static Future<void> addressDeleteBatch(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/address_book/delete_batch", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 新增转账地址
  static Future<void> addressTransferAdd(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/address_book/transfer_add", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 修改转账地址
  static Future<void> addressTransferModify(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/address_book/transfer_modify", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }
}
