import 'dart:async';

import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

class RichTextConfig extends StatefulWidget {
  final String title;
  final String configKey;
  final String configType;
  final String content;

  const RichTextConfig({
    super.key,
    required this.title,
    required this.configKey,
    required this.configType,
    this.content = '',
  });

  const RichTextConfig.content({
    super.key,
    required this.title,
    required this.content,
    this.configKey = '',
    this.configType = '',
  });

  @override
  _RichTextConfigState createState() => _RichTextConfigState();
}

class _RichTextConfigState extends State<RichTextConfig> {
  final Completer<String> configText = Completer<String>();

  @override
  void initState() {
    if (widget.configKey != '' && widget.configType != '') {
      getConfigText();
    } else {
      getContentText();
    }

    super.initState();
  }

  getConfigText() async {
    try {
      ToastUtil.showLoading();
      final res = await CommonApi.getConfig(
        type: widget.configType,
        key: widget.configKey,
      );

      ToastUtil.dismiss();
      String msg = await getHtml(res.data[widget.configKey]!);
      configText.complete(msg);
    } catch (e) {
      ToastUtil.dismiss();
      configText.completeError("Error");
    }
  }

  getContentText() async {
    try {
      ToastUtil.showLoading();
      String msg = await getHtml(widget.content);
      ToastUtil.dismiss();
      configText.complete(msg);
    } catch (e) {
      ToastUtil.dismiss();
      configText.completeError("Error");
    }
  }

  Future<String> getHtml(String msg) async {
    String cssText = await DefaultAssetBundle.of(
      context,
    ).loadString("assets/css/tinymce.content.min.css");
    String webCss = '';
    if (kIsWeb) {
      webCss = '.mce-content-body{font-size:20px;padding:0;}';
    }
    String text =
        '''
<!DOCTYPE html><html lang="zh">
<head><meta charset="utf-8"><style>$cssText</style><style>$webCss</style></head>
<body class="mce-content-body">
$msg
</body>
</html>
''';
    return text;
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: customTheme.pageBgColor,
        foregroundColor: customTheme.textColor,
      ),
      backgroundColor: customTheme.pageBgColor,
      body: FutureBuilder(
        future: configText.future,
        builder: (context, AsyncSnapshot<String> snapshot) {
          if (snapshot.hasData) {
            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: HtmlWidget(
                  snapshot.data ?? '',
                  textStyle: TextStyle(color: customTheme.textColor),
                ),
              ),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: customTheme.downColor,
                    size: 60,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(snapshot.error.toString()),
                  ),
                ],
              ),
            );
          }
          return Center(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 100),
              child: WidgetUtil.getLoadingIcon(context),
            ),
          );
        },
      ),
    );
  }
}
