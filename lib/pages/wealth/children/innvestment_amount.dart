import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class InnvestmentAmountPage extends StatefulWidget {
  const InnvestmentAmountPage({super.key});

  @override
  _InnvestmentAmountPageState createState() => _InnvestmentAmountPageState();
}

class _InnvestmentAmountPageState extends State<InnvestmentAmountPage> {
  final TextEditingController _amountController = TextEditingController();

  String name = '';
  // 最小投资额
  num minAmount = 0;
  bool isError = false;
  String errorText = '';

  @override
  void initState() {
    super.initState();
    name = Get.parameters['name'] ?? '';
    minAmount = double.parse(Get.parameters['minAmount'] ?? '0');
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void onConfirm() async {
    final amount = _amountController.text;
    if (amount.isEmpty) {
      setState(() {
        isError = true;
        errorText = 'please_enter_investment_amount'.tr;
      });
    } else if (!amount.isNum) {
      setState(() {
        isError = true;
        errorText = 'please_enter_valid_investment_amount'.tr;
      });
    } else if (double.parse(amount) < minAmount) {
      // 不能小于最小投资金额，看标的配置
      setState(() {
        isError = true;
        errorText = 'please_increase_your_investment_amount'.tr;
      });
    } else {
      setState(() {
        isError = false;
        errorText = '';
      });
      Get.toNamed(
        Routes.investmentSignature,
        parameters: {
          'productId': Get.parameters['productId'] ?? '',
          'name': Get.parameters['name'] ?? '',
          'interestRatePerCycle': Get.parameters['interestRatePerCycle'] ?? '',
          'interestCycleDays': Get.parameters['interestCycleDays'] ?? '',
          'lockinPeriodDays': Get.parameters['lockinPeriodDays'] ?? '',
          'amount': amount,
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final currentThemeType = context.read<ThemeProvider>().currentThemeType;
    final user = context.read<AuthProvider>().user;

    return Scaffold(
      appBar: AppBar(title: Text(name)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 8.h,
                children: [
                  Text(
                    'enter_investment_amount'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 1.h, bottom: 3.5.h),
                    child: Text(
                      'investment_amount_description'.tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: customTheme.inputBgColor,
                      borderRadius: BorderRadius.circular(3.w),
                      border: Border.all(
                        color: isError
                            ? customTheme.downColor
                            : Colors.transparent,
                      ),
                    ),
                    padding: EdgeInsets.only(
                      left: 19.w,
                      right: 19.w,
                      top: 20.h,
                      bottom: 11.5.h,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 8.5.h,
                      children: [
                        Row(
                          spacing: 4.w,
                          children: [
                            Text(
                              '\$',
                              style: TextStyle(
                                fontSize: 23.sp,
                                color: customTheme.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Expanded(
                              child: TextField(
                                controller: _amountController,
                                keyboardType: TextInputType.number,
                                keyboardAppearance:
                                    currentThemeType == ThemeType.dark
                                    ? Brightness.dark
                                    : Brightness.light,
                                style: TextStyle(
                                  fontSize: 23.sp,
                                  color: customTheme.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: '0.00',
                                  hintStyle: TextStyle(
                                    fontSize: 23.sp,
                                    color: customTheme.hintTextColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  contentPadding: EdgeInsets.zero,
                                ),
                                cursorColor: customTheme.textColor,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          '${'minimum'.tr}: \$ ${minAmount.toString().fmt}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: customTheme.hintTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isError)
                    Padding(
                      padding: EdgeInsets.only(top: 3.h, bottom: 10.h),
                      child: Text(
                        errorText,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: customTheme.downColor,
                        ),
                      ),
                    ),
                  Padding(
                    padding: EdgeInsets.only(top: 3.5.h),
                    child: Text(
                      '${'available_balance'.tr}: \$ ${user?.availableAmount.fmt}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.textColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'confirm'.tr,
            onSubmit: onConfirm,
          ),
        ],
      ),
    );
  }
}
