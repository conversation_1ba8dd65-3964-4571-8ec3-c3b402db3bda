import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/models/coupon_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class CouponPage extends StatefulWidget {
  const CouponPage({super.key});

  @override
  CouponPageState createState() => CouponPageState();
}

class CouponPageState extends State<CouponPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<CouponRes> activeCoupons = [];
  int _activePageNum = 1;
  bool _activeIsEnd = false;
  List<CouponRes> expiredCoupons = [];
  int _expiredPageNum = 1;
  bool _expiredIsEnd = false;
  late EasyRefreshController _activeController;
  late EasyRefreshController _expiredController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _activeController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    _expiredController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _activeController.dispose();
    _expiredController.dispose();
    super.dispose();
  }

  Future<void> _onRefreshActiveCoupons() async {
    try {
      _activePageNum = 1;
      final res = await CommonApi.getCouponList({
        'pageNum': _activePageNum,
        'pageSize': 10,
        'status': '0',
      });
      _activePageNum++;
      activeCoupons = res.list;
      _activeIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _activeController.finishRefresh();
    if (_activeIsEnd) {
      _activeController.finishLoad(IndicatorResult.noMore);
    } else {
      _activeController.resetFooter();
    }
  }

  Future<void> _onLoadMoreActiveCoupons() async {
    try {
      final res = await CommonApi.getCouponList({
        'pageNum': _activePageNum,
        'pageSize': 10,
        'status': '0',
      });
      _activePageNum++;
      activeCoupons.addAll(res.list);
      _activeIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _activeController.finishLoad(
      _activeIsEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  Future<void> _onRefreshExpiredCoupons() async {
    try {
      _expiredPageNum = 1;
      final res = await CommonApi.getCouponList({
        'pageNum': _expiredPageNum,
        'pageSize': 10,
        'statusList': ['1', '2'],
      });
      _expiredPageNum++;
      expiredCoupons = res.list;
      _expiredIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _expiredController.finishRefresh();
    if (_expiredIsEnd) {
      _expiredController.finishLoad(IndicatorResult.noMore);
    } else {
      _expiredController.resetFooter();
    }
  }

  Future<void> _onLoadMoreExpiredCoupons() async {
    try {
      final res = await CommonApi.getCouponList({
        'pageNum': _expiredPageNum,
        'pageSize': 10,
        'statusList': ['1', '2'],
      });
      _expiredPageNum++;
      expiredCoupons.addAll(res.list);
      _expiredIsEnd = res.isEnd;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _expiredController.finishLoad(
      _expiredIsEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('coupon_code'.tr)),
      body: Column(
        children: [
          _tabs(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CouponsTab(
                  items: activeCoupons,
                  controller: _activeController,
                  onRefresh: _onRefreshActiveCoupons,
                  onLoadMore: _onLoadMoreActiveCoupons,
                  itemBuilder: (index) => CouponItem(
                    context: context,
                    coupon: activeCoupons[index],
                  ),
                  isEnd: _activeIsEnd,
                ),
                CouponsTab(
                  items: expiredCoupons,
                  controller: _expiredController,
                  onRefresh: _onRefreshExpiredCoupons,
                  onLoadMore: _onLoadMoreExpiredCoupons,
                  isEnd: _expiredIsEnd,
                  itemBuilder: (index) => CouponItem(
                    context: context,
                    coupon: expiredCoupons[index],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _tabs() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    // 获取当前文字方向，如果是从右到左，则需要将TabBar的labelPadding设置为0
    final textDirection = Directionality.of(context);
    final labelPadding = textDirection == TextDirection.rtl
        ? EdgeInsets.only(left: 54.w)
        : EdgeInsets.only(right: 54.w);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: TabBar(
        controller: _tabController,
        indicatorWeight: 3.w,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 6.w),
        isScrollable: true,
        dividerHeight: 0.5,
        tabAlignment: TabAlignment.start,
        dividerColor: customTheme.dividerColor,
        labelPadding: labelPadding,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.lightTextColor,
          fontWeight: FontWeight.w600,
        ),
        tabs: [
          Tab(child: Text('active'.tr)),
          Tab(child: Text('expired'.tr)),
        ],
      ),
    );
  }
}

class CouponItem extends StatefulWidget {
  const CouponItem({super.key, required this.context, required this.coupon});

  final BuildContext context;
  final CouponRes coupon;

  @override
  State<CouponItem> createState() => _CouponItemState();
}

class _CouponItemState extends State<CouponItem> {
  bool isCopied = false;

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return GestureDetector(
      onTap: () {
        debugPrint('tap');
      },
      child: Container(
        padding: EdgeInsets.only(
          left: 4.w,
          top: 9.h,
          right: 7.5.w,
          bottom: 9.h,
        ),
        constraints: BoxConstraints(minHeight: 135.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          image: DecorationImage(
            image: AssetImage(customThemeImage.servicesCouponBg),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 67.w,
              child: Center(
                child: Text(
                  'coupon_code'.tr,
                  style: TextStyle(
                    fontSize: 12.5.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            // 添加一个竖线虚线分割线
            Container(
              width: 0.5,
              height: 90.5.h,
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              child: CustomPaint(
                painter: DashedLinePainter(
                  color: customTheme.dividerColor,
                  strokeWidth: 0.5,
                  dashWidth: 3,
                  dashSpace: 3,
                ),
              ),
            ),
            SizedBox(width: 8.5.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    spacing: 6.w,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Text(
                          widget.coupon.name,
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: customTheme.textColor,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (widget.coupon.status == '0')
                        Container(
                          decoration: BoxDecoration(
                            color: customTheme.primaryColor,
                            borderRadius: BorderRadius.circular(3.r),
                          ),
                          height: 24.h,
                          child: TextButton(
                            onPressed: () {
                              Clipboard.setData(
                                ClipboardData(text: widget.coupon.code),
                              ).then((value) {
                                setState(() {
                                  isCopied = true;
                                });
                                Future.delayed(Duration(seconds: 2), () {
                                  isCopied = false;
                                  if (mounted) {
                                    setState(() {});
                                  }
                                });
                              });
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(horizontal: 5.w),
                              minimumSize: Size(0, 24.h),
                            ),
                            child: isCopied
                                ? Image.asset(
                                    customThemeImage.authVerification,
                                    width: 18.w,
                                    height: 18.w,
                                    fit: BoxFit.contain,
                                  )
                                : Text(
                                    'copy_code'.tr,
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      color: customTheme.textColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                      if (widget.coupon.status == '2')
                        Container(
                          decoration: BoxDecoration(
                            color: customTheme.ff878C9f,
                            borderRadius: BorderRadius.circular(3.r),
                          ),
                          padding: EdgeInsets.symmetric(horizontal: 7.5.w),
                          height: 24.h,
                          alignment: Alignment.center,
                          child: Text(
                            'used'.tr,
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: customTheme.textColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    '${'code'.tr}: ${widget.coupon.code}',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: customTheme.subTextColor,
                    ),
                  ),
                  Text(
                    '${'status'.tr}: Active',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: customTheme.subTextColor,
                    ),
                  ),
                  Text(
                    'applicable_description'.tr,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: customTheme.subTextColor,
                    ),
                  ),
                  Text(
                    '${'valid_until'.tr}: ${DateTime.fromMillisecondsSinceEpoch(widget.coupon.expiredDatetime.toInt()).yyyyMMdd}',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: customTheme.subTextColor,
                    ),
                  ),
                  SizedBox(height: 9.h),
                  Text(
                    'applicable_description1'.tr,
                    style: TextStyle(
                      fontSize: 9.sp,
                      color: customTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CouponsTab extends StatefulWidget {
  final List<CouponRes> items;
  final EasyRefreshController controller;
  final Future<void> Function() onRefresh;
  final Future<void> Function() onLoadMore;
  final Widget Function(int) itemBuilder;
  final bool isEnd;

  const CouponsTab({
    required this.items,
    required this.controller,
    required this.onRefresh,
    required this.onLoadMore,
    required this.itemBuilder,
    required this.isEnd,
    super.key,
  });
  @override
  State<CouponsTab> createState() => _CouponsTabState();
}

class _CouponsTabState extends State<CouponsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    super.build(context);

    return EasyRefresh(
      controller: widget.controller,
      onRefresh: widget.onRefresh,
      onLoad: widget.onLoadMore,
      refreshOnStart: true,
      header: WidgetUtil.getRefreshOnStartHeader(context),
      child: widget.isEnd && widget.items.isEmpty
          ? _empty()
          : ListView.separated(
              padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 15.w),
              itemCount: widget.items.length,
              itemBuilder: (context, index) {
                return widget.itemBuilder(index);
              },
              separatorBuilder: (context, index) {
                return SizedBox(height: 12.h);
              },
            ),
    );
  }

  Widget _empty() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 40.h),
      child: Column(
        spacing: 13.5.w,
        children: [
          SizedBox(height: 100.h),
          Center(
            child: Image.asset(
              customThemeImage.commonEmptyCoupon,
              width: 80.w,
              height: 80.w,
            ),
          ),
          Text(
            'no_record_found'.tr,
            style: TextStyle(fontSize: 13.sp, color: customTheme.subTextColor),
          ),
        ],
      ),
    );
  }
}

// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 3.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final double dashLength = dashWidth;
    final double spaceLength = dashSpace;
    final double totalLength = dashLength + spaceLength;

    double currentY = 0;
    while (currentY < size.height) {
      final double endY = (currentY + dashLength).clamp(0.0, size.height);
      canvas.drawLine(Offset(0, currentY), Offset(0, endY), paint);
      currentY += totalLength;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
