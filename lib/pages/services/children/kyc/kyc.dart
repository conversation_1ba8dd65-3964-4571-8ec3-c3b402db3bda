import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/aml_modal.dart';

class KycPage extends StatefulWidget {
  const KycPage({super.key});

  @override
  _KycPageState createState() => _KycPageState();
}

class _KycPageState extends State<KycPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final isReviewing =
        context.watch<AuthProvider>().user?.identifyStatus == '4';

    return Scaffold(
      appBar: AppBar(title: Text('kyc_verification'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: isReviewing ? 85.h : 44.5.h),
            Center(
              child: isReviewing
                  ? Image.asset(
                      customThemeImage.kycVerification,
                      width: 86.w,
                      height: 108.5.w,
                      fit: BoxFit.fill,
                    )
                  : Image.asset(
                      customThemeImage.kycIcon,
                      width: 110.5.w,
                      height: 85.5.w,
                      fit: BoxFit.fill,
                    ),
            ),
            SizedBox(height: 41.h),
            Center(
              child: Text(
                isReviewing
                    ? "kyc_review_tip".tr
                    : 'kyc_verification_required'.tr,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: isReviewing ? 90.5.h : 20.5.h),
            if (!isReviewing)
              Text(
                'kyc_verification_required_description'.tr,
                style: TextStyle(
                  fontSize: 15.sp,
                  color: customTheme.subTextColor,
                ),
              ),
            if (!isReviewing) SizedBox(height: 18.h),
            if (!isReviewing)
              Text(
                'kyc_verification_required_description_2'.tr,
                style: TextStyle(
                  fontSize: 15.sp,
                  color: customTheme.subTextColor,
                ),
              ),
            if (!isReviewing) SizedBox(height: 37.5.h),
            if (!isReviewing)
              Wrap(
                spacing: 3.5.w,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Image.asset(
                    customThemeImage.kycWarn,
                    width: 15.w,
                    height: 15.w,
                    fit: BoxFit.fill,
                  ),
                  Text(
                    'kyc_note'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            if (!isReviewing) SizedBox(height: 7.h),
            if (!isReviewing)
              Text(
                'kyc_note_description'.tr,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: customTheme.subTextColor,
                ),
              ),
            if (!isReviewing) SizedBox(height: 152.5.h),
            if (!isReviewing)
              PrimaryButton(
                title: 'verify_now'.tr,
                onPress: () {
                  Get.toNamed(Routes.kycVerify);
                },
              ),
            if (isReviewing)
              Center(
                child: GestureDetector(
                  onTap: () {
                    AmlModal.showModal(context);
                  },
                  child: RichText(
                    text: TextSpan(
                      text: 'learn_more_about_aml'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: customTheme.primaryColor,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ),
            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }
}
