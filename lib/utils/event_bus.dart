import 'dart:async';

import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/models/websocket_res.dart';
import 'package:event_bus_plus/event_bus_plus.dart';
import 'package:flutter/material.dart' show ValueChanged, Locale;

final eventBus = EventBus();

class UserLoginEvent extends AppEvent {
  final String userId;

  const UserLoginEvent({required this.userId});

  @override
  List<Object?> get props => [userId];
}

/// 用户退出登录事件
class UserLogoutEvent extends AppEvent {
  const UserLogoutEvent();

  @override
  List<Object?> get props => [];
}

/// 切换语言事件
class SwitchLanguageEvent extends AppEvent {
  final Locale locale;
  final Region region;
  const SwitchLanguageEvent({required this.locale, required this.region});

  @override
  List<Object?> get props => [locale, region];
}

/// 切换Tab事件
class SwitchTabEvent extends AppEvent {
  final int tabIndex;

  const SwitchTabEvent({required this.tabIndex});

  @override
  List<Object?> get props => [tabIndex];
}

/// KYC申请事件
class KycApplyEvent extends AppEvent {
  const KycApplyEvent();

  @override
  List<Object?> get props => [];
}

/// 绑定Google成功事件
class BindGoogleSuccessEvent extends AppEvent {
  const BindGoogleSuccessEvent();

  @override
  List<Object?> get props => [];
}

/// 解绑Google成功事件
class UnbindGoogleSuccessEvent extends AppEvent {
  const UnbindGoogleSuccessEvent();

  @override
  List<Object?> get props => [];
}

/// 接收WebSocket消息事件
class ReceiveWebSocketMessageEvent extends AppEvent {
  final WebSocketRes webSocketRes;
  const ReceiveWebSocketMessageEvent({required this.webSocketRes});

  @override
  List<Object?> get props => [webSocketRes];
}

/// 投资成功事件
class InvestmentSuccessEvent extends AppEvent {
  const InvestmentSuccessEvent();
  @override
  List<Object?> get props => [];
}

/// 赎回提交事件
class RedeemApplyEvent extends AppEvent {
  const RedeemApplyEvent();
  @override
  List<Object?> get props => [];
}

/// 修改手机号事件
class ModifyMobileEvent extends AppEvent {
  const ModifyMobileEvent({required this.mobile, required this.countryCode});
  final String mobile;
  final String countryCode;

  @override
  List<Object?> get props => [mobile, countryCode];
}

/// 用户身份提交升级事件
class UserNodeUpgradeEvent extends AppEvent {
  const UserNodeUpgradeEvent({required this.userId});
  final String userId;

  @override
  List<Object?> get props => [userId];
}

class EventBusUtil {
  /// 发送登录事件
  static fireLogin(UserLoginEvent event) {
    eventBus.fire(event);
  }

  /// 监听登录事件
  static StreamSubscription<UserLoginEvent> listenLogin(
    ValueChanged<UserLoginEvent> onData,
  ) {
    return eventBus.on<UserLoginEvent>().listen(onData);
  }

  /// 发送用户退出登录事件
  static fireUserLogout([UserLogoutEvent? event]) {
    eventBus.fire(event ?? UserLogoutEvent());
  }

  /// 监听用户退出登录事件
  static StreamSubscription<UserLogoutEvent> listenUserLogout(
    ValueChanged<UserLogoutEvent> onData,
  ) {
    return eventBus.on<UserLogoutEvent>().listen(onData);
  }

  /// 发送切换语言事件
  static fireSwitchLanguage(SwitchLanguageEvent event) {
    eventBus.fire(event);
  }

  /// 监听切换语言事件
  static StreamSubscription<SwitchLanguageEvent> listenSwitchLanguage(
    ValueChanged<SwitchLanguageEvent> onData,
  ) {
    return eventBus.on<SwitchLanguageEvent>().listen(onData);
  }

  /// 发送切换Tab事件
  static fireSwitchTab(SwitchTabEvent event) {
    eventBus.fire(event);
  }

  /// 监听切换Tab事件
  static StreamSubscription<SwitchTabEvent> listenSwitchTab(
    ValueChanged<SwitchTabEvent> onData,
  ) {
    return eventBus.on<SwitchTabEvent>().listen(onData);
  }

  /// 发送KYC申请事件
  static fireKycApply(KycApplyEvent event) {
    eventBus.fire(event);
  }

  /// 监听KYC申请事件
  static StreamSubscription<KycApplyEvent> listenKycApply(
    ValueChanged<KycApplyEvent> onData,
  ) {
    return eventBus.on<KycApplyEvent>().listen(onData);
  }

  /// 发送绑定Google成功事件
  static fireBindGoogleSuccess(BindGoogleSuccessEvent event) {
    eventBus.fire(event);
  }

  /// 监听绑定Google成功事件
  static StreamSubscription<BindGoogleSuccessEvent> listenBindGoogleSuccess(
    ValueChanged<BindGoogleSuccessEvent> onData,
  ) {
    return eventBus.on<BindGoogleSuccessEvent>().listen(onData);
  }

  /// 发送解绑Google成功事件
  static fireUnbindGoogleSuccess(UnbindGoogleSuccessEvent event) {
    eventBus.fire(event);
  }

  /// 监听解绑Google成功事件
  static StreamSubscription<UnbindGoogleSuccessEvent> listenUnbindGoogleSuccess(
    ValueChanged<UnbindGoogleSuccessEvent> onData,
  ) {
    return eventBus.on<UnbindGoogleSuccessEvent>().listen(onData);
  }

  /// 发送接收WebSocket消息事件
  static fireReceiveWebSocketMessage(ReceiveWebSocketMessageEvent event) {
    eventBus.fire(event);
  }

  /// 监听接收WebSocket消息事件
  static StreamSubscription<ReceiveWebSocketMessageEvent>
  listenReceiveWebSocketMessage(
    ValueChanged<ReceiveWebSocketMessageEvent> onData,
  ) {
    return eventBus.on<ReceiveWebSocketMessageEvent>().listen(onData);
  }

  /// 发送投资成功事件
  static fireInvestmentSuccess(InvestmentSuccessEvent event) {
    eventBus.fire(event);
  }

  /// 监听投资成功事件
  static StreamSubscription<InvestmentSuccessEvent> listenInvestmentSuccess(
    ValueChanged<InvestmentSuccessEvent> onData,
  ) {
    return eventBus.on<InvestmentSuccessEvent>().listen(onData);
  }

  /// 发送赎回提交事件
  static fireRedeemApply(RedeemApplyEvent event) {
    eventBus.fire(event);
  }

  /// 监听赎回提交事件
  static StreamSubscription<RedeemApplyEvent> listenRedeemApply(
    ValueChanged<RedeemApplyEvent> onData,
  ) {
    return eventBus.on<RedeemApplyEvent>().listen(onData);
  }

  /// 发送修改手机号事件
  static fireModifyMobile(ModifyMobileEvent event) {
    eventBus.fire(event);
  }

  /// 监听修改手机号事件
  static StreamSubscription<ModifyMobileEvent> listenModifyMobile(
    ValueChanged<ModifyMobileEvent> onData,
  ) {
    return eventBus.on<ModifyMobileEvent>().listen(onData);
  }

  /// 发送用户身份提交升级事件
  static fireUserNodeUpgrade(UserNodeUpgradeEvent event) {
    eventBus.fire(event);
  }

  /// 监听用户身份提交升级事件
  static StreamSubscription<UserNodeUpgradeEvent> listenUserNodeUpgrade(
    ValueChanged<UserNodeUpgradeEvent> onData,
  ) {
    return eventBus.on<UserNodeUpgradeEvent>().listen(onData);
  }
}
