class CustomThemeImage {
  /// 默认加载图(正方形、横图、竖图)
  final String defaultSquareImg;
  final String defaultHRectangleImg;
  final String defaultVRectangleImg;

  /// tabs 图标
  final String tabsHome;
  final String tabsHomeIn;
  final String tabsPortfolio;
  final String tabsPortfolioIn;
  final String tabsWealth;
  final String tabsWealthIn;
  final String tabsAccount;
  final String tabsAccountIn;
  final String tabsServices;
  final String tabsServicesIn;

  /// Common
  final String commonEmpty;
  final String commonEmpty1;
  final String commonEmptyCoupon;
  final String commonDown;
  final String commonIconRight;
  final String commonSuccessTip;
  final String commonErrorTip;
  final String commonWarnTip;
  final String commonIconScreen;

  /// 登录页
  final String authLogo;
  final String authBgTop;
  final String authBgBottom;
  final String authSignInLogo;
  final String authUnchoose;
  final String authChoose;
  final String authSignUpLogo;
  final String authVerified;
  final String authSucTip;
  final String authLoading;
  final String authVerification;
  final String authSelect;
  final String authNight;
  final String authSunny;

  /// KYC
  final String kycIcon;
  final String kycClose;
  final String kycPlaceholder;
  final String kycVerification;
  final String kycWarn;
  final String kycSelect;
  final String kycUnselect;

  /// Home
  final String homeMessage;
  final String homeLogo;
  final String homeClose;
  final String homeShare;
  final String homeAdd;
  final String homeReduce;
  final String homeChat;
  final String homeBalanceBg;
  final String homeUp;
  final String homeDown;
  final String homeFail;
  final String homeWarn;
  final String homeCopy;
  final String homeLink;
  final String homeCalendar;
  final String homeEnvelope;

  /// Portfolio
  final String portfolioCardBg;
  final String portfolioArrowRight;
  final String portfolioTip;

  /// Services
  final String servicesEdit;
  final String servicesScan;
  final String servicesCoupon;
  final String servicesPersonal;
  final String servicesAuthenticator;
  final String servicesService;
  final String servicesAP;
  final String servicesCP;
  final String servicesSL;
  final String servicesRD;
  final String servicesED;
  final String servicesGD;
  final String servicesPro;
  final String servicesPreferred;
  final String servicesElite;
  final String servicesPremier;
  final String servicesSignature;
  final String servicesPlatinum;
  final String servicesCouponBg;
  final String servicesPhone;
  final String servicesLock;

  /// Wealth
  final String wealthMoney;

  /// Google
  final String googleSecure;
  final String googleSecurity;
  final String googleSuccess;
  final String googleSuccess1;
  final String googleUnbind;

  /// account
  final String accountListBg1;
  final String accountListBg2;
  final String accountDetailTips;

  final String accountRedeemSb;
  final String accountRedeemWc;
  final String accountRedeemDd;

  final String accountTransferUsdtcg;
  final String accountTransferUsdtddz;
  final String accountTransferUsdtsb;
  final String accountTransferEmail;

  final String accountRechargeEmail;
  final String accountRechargeYhk;
  final String accountRechargeUsdt;
  final String accountRechargeUid;

  final String accountCommissionZq;
  final String accountCommissionYqjlsb;
  final String accountCommissionYqjl;
  final String accountCommissionZz;

  final String accountCurrencyUsdt;
  final String accountCurrencyUsdc;

  final String addFundsChainRechargeTips;
  final String addFundsChainRechargeCopy;

  final String sendChainIconCopy;
  final String sendChainIconScan;
  final String sendChainIconAddress;
  final String sendChainIconEdit;
  final String sendChainIconAddressNoRecord;
  final String sendChainIconAddressCheck;

  CustomThemeImage({
    required this.defaultSquareImg,
    required this.defaultHRectangleImg,
    required this.defaultVRectangleImg,
    required this.tabsHome,
    required this.tabsHomeIn,
    required this.tabsPortfolio,
    required this.tabsPortfolioIn,
    required this.tabsWealth,
    required this.tabsWealthIn,
    required this.tabsAccount,
    required this.tabsAccountIn,
    required this.tabsServices,
    required this.tabsServicesIn,
    required this.commonEmpty,
    required this.commonEmpty1,
    required this.commonEmptyCoupon,
    required this.commonDown,
    required this.commonIconRight,
    required this.commonSuccessTip,
    required this.commonErrorTip,
    required this.commonWarnTip,
    required this.commonIconScreen,
    required this.authLogo,
    required this.authBgTop,
    required this.authBgBottom,
    required this.authSignInLogo,
    required this.authUnchoose,
    required this.authChoose,
    required this.authSignUpLogo,
    required this.authVerified,
    required this.authSucTip,
    required this.authLoading,
    required this.authVerification,
    required this.authSelect,
    required this.authNight,
    required this.authSunny,
    required this.kycIcon,
    required this.kycClose,
    required this.kycPlaceholder,
    required this.kycVerification,
    required this.kycWarn,
    required this.kycSelect,
    required this.kycUnselect,
    required this.homeMessage,
    required this.homeLogo,
    required this.homeClose,
    required this.homeShare,
    required this.homeAdd,
    required this.homeReduce,
    required this.homeChat,
    required this.homeBalanceBg,
    required this.homeUp,
    required this.homeDown,
    required this.homeFail,
    required this.homeWarn,
    required this.homeCopy,
    required this.homeLink,
    required this.homeCalendar,
    required this.homeEnvelope,
    required this.portfolioCardBg,
    required this.portfolioArrowRight,
    required this.portfolioTip,
    required this.servicesEdit,
    required this.servicesScan,
    required this.servicesCoupon,
    required this.servicesPersonal,
    required this.servicesAuthenticator,
    required this.servicesService,
    required this.servicesAP,
    required this.servicesCP,
    required this.servicesSL,
    required this.servicesRD,
    required this.servicesED,
    required this.servicesGD,
    required this.servicesPro,
    required this.servicesPreferred,
    required this.servicesElite,
    required this.servicesPremier,
    required this.servicesSignature,
    required this.servicesPlatinum,
    required this.servicesCouponBg,
    required this.servicesPhone,
    required this.servicesLock,
    required this.wealthMoney,
    required this.googleSecure,
    required this.googleSecurity,
    required this.googleSuccess,
    required this.googleSuccess1,
    required this.googleUnbind,
    required this.accountListBg1,
    required this.accountListBg2,
    required this.accountDetailTips,

    required this.accountRedeemSb,
    required this.accountRedeemWc,
    required this.accountRedeemDd,

    required this.accountTransferUsdtcg,
    required this.accountTransferUsdtddz,
    required this.accountTransferUsdtsb,
    required this.accountTransferEmail,

    required this.accountRechargeEmail,
    required this.accountRechargeUsdt,
    required this.accountRechargeYhk,
    required this.accountRechargeUid,

    required this.accountCommissionZq,
    required this.accountCommissionYqjlsb,
    required this.accountCommissionYqjl,
    required this.accountCommissionZz,
    required this.accountCurrencyUsdt,
    required this.accountCurrencyUsdc,
    required this.addFundsChainRechargeTips,
    required this.addFundsChainRechargeCopy,

    required this.sendChainIconCopy,
    required this.sendChainIconScan,
    required this.sendChainIconAddress,
    required this.sendChainIconEdit,
    required this.sendChainIconAddressNoRecord,
    required this.sendChainIconAddressCheck,
  });
}
