import 'dart:async';

import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SignUpSuccessfullyPage extends StatefulWidget {
  const SignUpSuccessfullyPage({super.key});

  @override
  _SignUpSuccessfullyPageState createState() => _SignUpSuccessfullyPageState();
}

class _SignUpSuccessfullyPageState extends State<SignUpSuccessfullyPage> {
  @override
  Widget build(BuildContext context) {
    final customeThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;

    // 使用工具类获取AppBar高度
    final appBarHeight = CommonUtils.getAppBarHeight(context);

    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.transparent),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              customeThemeImage.authBgBottom,
              width: MediaQuery.of(context).size.width,
              height: 313.h,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
            ),
          ),
          Positioned.fill(
            left: 15.w,
            right: 15.w,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: appBarHeight + 44.h),
                  Image.asset(customeThemeImage.authVerified, width: 180.w),
                  SizedBox(height: 29.5.h),
                  Text(
                    'account_created_successfully'.tr,
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 28.h),
                  Text(
                    "you_re_all_set_just_one_more_step_complete_your_identity_verification_kyc_to_access_all_features_and_keep_your_account_secure"
                        .tr,
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: customTheme.subTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 94.5.h),
                  PrimaryButton(
                    title: 'start_verification'.tr,
                    onPress: () {
                      Get.offAllNamed(
                        Routes.home,
                        parameters: {"tabIndex": "4"},
                      )?.then((value) {
                        debugPrint('-----start_verification----');
                        Timer.run(() {
                          Get.toNamed(Routes.kyc);
                        });
                      });
                    },
                  ),
                  SizedBox(height: 28.5.h),
                  GestureDetector(
                    onTap: () {
                      Get.offAllNamed(
                        Routes.home,
                        parameters: {"tabIndex": "0"},
                      );
                    },
                    child: Text(
                      'skip_for_now'.tr,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.primaryColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 40.w),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
