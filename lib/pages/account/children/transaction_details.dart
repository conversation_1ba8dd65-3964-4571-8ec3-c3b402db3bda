import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/models/jour_item_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class TransactionDetailPage extends StatefulWidget {
  const TransactionDetailPage({super.key});

  @override
  _TransactionDetailPageState createState() => _TransactionDetailPageState();
}

class _TransactionDetailPageState extends State<TransactionDetailPage> {
  JourItemRes? info;

  /// 后台变更：manual_change,投资：fund,账户收益：account_income,奖励：reward,取现：withdraw,转账：transfer
  String? bizCategory;

  /// 充值:add_funds,扣减:reduce,策略激活:activate_strategy,策略回报:strategy_returns,赎回:redeem,推荐收益:recommended_income,奖励:reward,取现成功：withdraw_success,转账支出：transfer_out,转账收入：transfer_in,佣金：commission
  String? bizType;

  // 转账方式
  Map<String, String> transferMethodDict = {'Email': 'email'.tr, 'UID': 'UID'};

  @override
  void initState() {
    super.initState();
    getInit();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> getInit() async {
    final args = Get.parameters;
    String id = args['id'] ?? '';
    if (id.isNotEmpty) {
      try {
        ToastUtil.showLoading();
        final res = await AccountApi.getActivityDetail(id);
        ToastUtil.dismiss();
        setState(() {
          bizCategory = res.bizCategory;
          bizType = res.bizType;
          info = res;
        });
      } catch (e) {
        ToastUtil.dismiss();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('Details'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(top: 19.w, left: 15.w, right: 15.w),
        child: info != null
            ? _buildDetail(customTheme, customThemeImage)
            : SizedBox.shrink(),
      ),
    );
  }

  /// bizType:
  /// 充值:add_funds,扣减:reduce,策略激活:activate_strategy,策略回报:strategy_returns,
  /// 赎回:redeem,推荐收益:recommended_income,奖励:reward, 取现成功：withdraw_success,
  /// 转账支出：transfer_out,转账收入：transfer_in,佣金：commission
  ///
  /// bizCategory:
  /// 后台变更：manual_change,投资：fund,账户收益：account_income,奖励：reward,取现：withdraw,转账：transfer
  ///
  /// status
  /// 0: 等待中, 1:已完成, 2:已失败
  ///
  /// category
  /// 0: 充值明细 1:转账明细 2:佣金 3:周期奖励 4:赎回
  ///
  bool get senderShow {
    return info?.category == '0' &&
        (info?.transferMethod == 'Email' || info?.transferMethod == 'UID');
  }

  bool get addressShow {
    return info?.category == '0' && bizType == 'add_funds';
  }

  bool get requestDateShow {
    return info?.category == '4' ||
        (info?.bizCategory == 'withdraw' && info?.bizType == 'withdraw');
  }

  bool get dateShow {
    return info?.category == '1' ||
        info?.category == '2' ||
        info?.category == '3' ||
        info?.bizCategory == 'reward';
  }

  bool get recipientShow {
    return info?.category == '1';
  }

  bool get networkShow {
    return (info?.bizCategory == 'withdraw' && info?.bizType == 'withdraw');
  }

  bool get transferMethodShow {
    return info?.bizType == 'transfer_out' || info?.bizType == 'transfer_in';
  }

  bool get couponShow {
    return info?.status == '0' || info?.status == '2';
  }

  bool get reasonShow {
    return info?.status == '2';
  }

  Widget _buildDetail(
    CustomThemeSwatch customTheme,
    CustomThemeImage customThemeImage,
  ) {
    Map<String, dynamic> imageUrl = {
      'add_funds': customThemeImage.accountRechargeUid,
      'reduce': customThemeImage.accountTransferEmail,
      'activate_strategy': customThemeImage.accountRedeemWc,
      'strategy_returns': customThemeImage.accountRedeemWc,
      'redeem': customThemeImage.accountRedeemWc,
      'recommended_income': customThemeImage.accountCommissionZq,
      'reward': customThemeImage.accountCommissionYqjl,
      'withdraw_success': customThemeImage.accountTransferUsdtcg,
      'transfer_out': customThemeImage.accountCommissionZz,
      'transfer_in': customThemeImage.accountCommissionZz,
      'commission': customThemeImage.accountCommissionZq,
    };

    Map<String, dynamic> describe = {
      'add_funds': 'amount_successfully_credited'.tr,
      'reduce': 'amount_successfully_credited'.tr,
      'activate_strategy': 'amount_successfully_credited'.tr,
      'strategy_returns': 'amount_successfully_credited'.tr,
      'redeem': info?.status == '0'
          ? 'redemption_processing'.tr
          : info?.status == '0'
          ? 'redemption_success'.tr
          : 'redemption_failed'.tr,
      'recommended_income': info?.status == '2'
          ? 'Your_commission_was_not_approved'.tr
          : 'You_earned_commission'.tr,
      'reward': 'amount_successfully_credited'.tr,
      'withdraw_success': 'withdraw_successful',
      'transfer_out': info?.status == '2'
          ? 'transfer_failed'.tr
          : 'transfer_success_message'.trParams({
              'amount': '${info?.transAmount ?? ''}',
            }),
      'transfer_in': info?.status == '2'
          ? 'transfer_failed'.tr
          : 'transfer_success_message'.trParams({
              'amount': '${info?.transAmount ?? ''}',
            }),
      'commission': 'amount_successfully_credited'.tr,
    };

    return Column(
      children: [
        SizedBox(height: 25.w),
        Image.asset(
          imageUrl[bizType],
          width: 60.w,
          height: 60.w,
          fit: BoxFit.fill,
        ),
        SizedBox(height: 8.w),
        Text(
          info?.categoryValue ?? '',
          style: TextStyle(
            fontSize: 12.w,
            fontWeight: FontWeight.w400,
            color: customTheme.lightTextColor,
          ),
        ),
        SizedBox(height: 12.w),
        Text(
          '\$ ${info?.transAmount ?? ''}',
          style: TextStyle(
            fontSize: 23.w,
            fontWeight: FontWeight.w600,
            color: customTheme.textColor,
          ),
        ),
        SizedBox(height: 34.w),
        Text(
          describe[bizType],
          style: TextStyle(
            fontSize: 13.w,
            fontWeight: FontWeight.w400,
            color: customTheme.textColor,
            height: 19.w / 13.w,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 40.w),
        Container(
          decoration: BoxDecoration(
            color: customTheme.inputBgColor,
            borderRadius: BorderRadius.circular(7.w),
          ),
          padding: EdgeInsets.symmetric(vertical: 6.w, horizontal: 13.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              buildItem(
                label: 'reference_id'.tr,
                value: info!.id,
                customTheme: customTheme,
              ),

              if (senderShow)
                buildItem(
                  label: 'sender'.tr,
                  value: 'XXXXX',
                  customTheme: customTheme,
                ),

              if (addressShow)
                buildItem(
                  label: 'address'.tr,
                  value: info?.address ?? '',
                  customTheme: customTheme,
                ),
              if (recipientShow)
                buildItem(
                  label: 'recipient'.tr,
                  value: info?.address ?? '',
                  customTheme: customTheme,
                ),

              if (requestDateShow)
                buildItem(
                  label: 'request_date'.tr,
                  value: CommonUtils.dateTimeForHourFormat(
                    info?.createDatetime,
                  ),
                  customTheme: customTheme,
                ),

              if (dateShow)
                buildItem(
                  label: 'date'.tr,
                  value: CommonUtils.dateTimeForHourFormat(
                    info?.createDatetime,
                  ),
                  customTheme: customTheme,
                ),
              buildItem(
                label: 'type'.tr,
                value: info?.type ?? '',
                customTheme: customTheme,
              ),
              if (networkShow)
                buildItem(
                  label: 'network'.tr,
                  value: info?.network ?? '',
                  customTheme: customTheme,
                ),
              if (transferMethodShow)
                buildItem(
                  label: 'transfer_method'.tr,
                  value: transferMethodDict[info?.transferMethod ?? ''] ?? '',
                  customTheme: customTheme,
                ),
              buildItem(
                label: 'remark'.tr,
                value: info?.remark ?? '',
                customTheme: customTheme,
              ),
              if (couponShow)
                buildItem(
                  label: 'coupon'.tr,
                  value: info?.coupon ?? '',
                  customTheme: customTheme,
                ),
              if (reasonShow)
                buildItem(
                  label: 'reason'.tr,
                  value: info?.reason ?? '',
                  customTheme: customTheme,
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildItem({
    required String label,
    required String value,
    required CustomThemeSwatch customTheme,
  }) {
    return Container(
      padding: EdgeInsets.only(top: 11.w, bottom: 11.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.w,
              fontWeight: FontWeight.w400,
              color: customTheme.hintTextColor,
            ),
          ),
          SizedBox(width: 40.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12.w,
                fontWeight: FontWeight.w600,
                color: customTheme.textColor,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
