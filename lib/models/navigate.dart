import 'package:json_annotation/json_annotation.dart';

part 'navigate.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class Navigate {
  Navigate();

  late String id;
  late String name;
  late String type;
  late String location;
  late String action;
  String? url;
  late String pic;
  late String status;
  late num orderNo;

  factory Navigate.fromJson(Map<String, dynamic> json) =>
      _$NavigateFromJson(json);
  Map<String, dynamic> toJson() => _$NavigateToJson(this);
}
