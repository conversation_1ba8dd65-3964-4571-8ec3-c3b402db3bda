import 'package:json_annotation/json_annotation.dart';

part 'user_node_level_list_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class UserNodeLevelListRes {
  UserNodeLevelListRes();

  late String id;
  late String userId;
  late String userName;
  late String userReferee;
  late String userRefereeName;
  late String userRefereeInviteNo;
  late String nodeId;
  late String nodeType;
  late String nodeName;
  late num performance;
  late num diffRate;

  factory UserNodeLevelListRes.fromJson(Map<String, dynamic> json) =>
      _$UserNodeLevelListResFromJson(json);
  Map<String, dynamic> toJson() => _$UserNodeLevelListResToJson(this);
}
