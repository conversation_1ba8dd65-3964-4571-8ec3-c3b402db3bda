import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'question_option_res.dart';

part 'question_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class QuestionRes {
  QuestionRes();

  late String id;
  late String title;
  late String type;
  String? content;
  late List<QuestionOptionRes> questions;

  factory QuestionRes.fromJson(Map<String, dynamic> json) =>
      _$QuestionResFromJson(json);
  Map<String, dynamic> toJson() => _$QuestionResToJson(this);
}
