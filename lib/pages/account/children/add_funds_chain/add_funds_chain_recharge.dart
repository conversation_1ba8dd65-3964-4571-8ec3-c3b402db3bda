import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';

class AddFundsChainRechargePage extends StatefulWidget {
  const AddFundsChainRechargePage({super.key});

  @override
  _AddFundsChainRechargePageState createState() =>
      _AddFundsChainRechargePageState();
}

class _AddFundsChainRechargePageState extends State<AddFundsChainRechargePage>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void onSaveCode() async {
    Get.back();
  }

  void onShareCode() async {
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    return Scaffold(
      appBar: AppBar(title: Text('Add_Funds'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                children: [
                  Text(
                    'network'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.subTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Tron (TRC20)',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 18.h),
                  Container(
                    padding: EdgeInsets.all(2.w), // 边框厚度
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFF5F7EFC), Color(0xFF40E2FE)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15.w),
                    ),
                    child: Container(
                      width: 205.w,
                      height: 205.w,
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(15.w),
                      ),
                      child: Center(
                        child: QrImageView(
                          data: 'https://www.google.com',
                          version: QrVersions.auto,
                          size: 166.w,
                          padding: EdgeInsets.all(11.w),
                          backgroundColor: customTheme.textColor,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 21.h),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 20.w,
                    ),
                    decoration: BoxDecoration(
                      color: customTheme.inputBgColor,
                      borderRadius: BorderRadius.circular(15.w),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          customTheme
                              .addFundsRechargeAddressBg1, // rgba(43,43,56,0)
                          customTheme.addFundsRechargeAddressBg2, // #282835
                        ],
                        stops: const [0.0, 1.0],
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              width: 2.w,
                              height: 13.h,
                              decoration: BoxDecoration(
                                color: customTheme.primaryColor,
                                borderRadius: BorderRadius.circular(1.w),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Wallet_Address:'.tr,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: customTheme.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                'TDSb9RM79Jhja9Zqt9FQ2ZpFw3nH7vVnQg',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: customTheme.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Image.asset(
                              customThemeImage.addFundsChainRechargeCopy,
                              width: 22.w,
                              height: 22.w,
                              fit: BoxFit.fill,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 11.h),
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: 11.w,
                      horizontal: 10.w,
                    ),
                    decoration: BoxDecoration(
                      color: customTheme.addFundsRechargeTipsBg,
                      borderRadius: BorderRadius.circular(9.w),
                      border: Border.all(
                        width: 1.w,
                        color: customTheme.primaryColor,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Image.asset(
                          customThemeImage.addFundsChainRechargeTips,
                          width: 23.w,
                          height: 23.w,
                          fit: BoxFit.fill,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            'add_funds_chain_recharge_select_network_tips'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: customTheme.textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 11.h),
                  Container(
                    decoration: BoxDecoration(
                      color: customTheme.ff1f1f2a,
                      borderRadius: BorderRadius.circular(7.w),
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: 11.w,
                      horizontal: 17.w,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        buildItem(
                          label: 'Minimum_deposit'.tr,
                          value: '0.01 USDT',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Estimate_arrival'.tr,
                          value: '~ 1 minutes',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Contract_address'.tr,
                          value: '0xa...h89k',
                          customTheme: customTheme,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomWrapper(
            context: context,
            color: customTheme.pageBgColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: OutlineButton(
                    title: 'save_code'.tr,
                    height: 51.w,
                    onPress: onSaveCode,
                  ),
                ),
                SizedBox(width: 11.w),
                Expanded(
                  child: PrimaryButton(
                    title: 'share_code'.tr,
                    height: 51.w,
                    onPress: onShareCode,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildItem({
    required String label,
    required String value,
    required CustomThemeSwatch customTheme,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.w,
              fontWeight: FontWeight.w400,
              color: customTheme.hintTextColor,
            ),
          ),
          SizedBox(width: 40.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12.w,
                fontWeight: FontWeight.w600,
                color: customTheme.textColor,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
