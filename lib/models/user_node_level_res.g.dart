// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_node_level_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserNodeLevelRes _$UserNodeLevelResFromJson(Map<String, dynamic> json) =>
    UserNodeLevelRes()
      ..id = json['id'] as String
      ..userId = json['userId'] as String
      ..userName = json['userName'] as String
      ..nodeId = json['nodeId'] as String
      ..nodeType = json['nodeType'] as String
      ..name = json['name'] as String
      ..pic = json['pic'] as String?
      ..rewardsRate = json['rewardsRate'] as num
      ..rewardsRateType = json['rewardsRateType'] as String
      ..performance = json['performance'] as num
      ..maintainPerformance = json['maintainPerformance'] as num
      ..applyStatus = json['applyStatus'] as String
      ..createDatetime = json['createDatetime'] as num
      ..fundInvestmentRecordUserLevelPageResList =
          (json['fundInvestmentRecordUserLevelPageResList'] as List<dynamic>)
              .map(
                (e) => FundInvestmentRecordUserLevelRes.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList();

Map<String, dynamic> _$UserNodeLevelResToJson(UserNodeLevelRes instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'nodeId': instance.nodeId,
      'nodeType': instance.nodeType,
      'name': instance.name,
      'pic': instance.pic,
      'rewardsRate': instance.rewardsRate,
      'rewardsRateType': instance.rewardsRateType,
      'performance': instance.performance,
      'maintainPerformance': instance.maintainPerformance,
      'applyStatus': instance.applyStatus,
      'createDatetime': instance.createDatetime,
      'fundInvestmentRecordUserLevelPageResList':
          instance.fundInvestmentRecordUserLevelPageResList,
    };
