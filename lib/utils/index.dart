import 'dart:async';

import 'package:dart_date/dart_date.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CommonUtils {
  /// 创建Material风格的color
  static MaterialColor createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map swatch = <int, Color>{};
    final int r = (color.r * 255.0).round() & 0xff,
        g = (color.g * 255.0).round() & 0xff,
        b = (color.b * 255.0).round() & 0xff;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.toARGB32(), swatch as Map<int, Color>);
  }

  /// 删除值为null的key
  static Map<String, dynamic> removeNullKeys(Map<String, dynamic> map) {
    map.keys.where((k) => (map[k] == null)).toList().forEach(map.remove);
    return map;
  }

  /// 计算flexbar当前滚动的比例0~1之间
  /// 0表示完全展示，1表示已经完全滚动到头部了
  static double calcFlexibleSpaceDeltaRate(BuildContext context) {
    final FlexibleSpaceBarSettings settings = context
        .dependOnInheritedWidgetOfExactType<FlexibleSpaceBarSettings>()!;
    final double deltaExtent = settings.maxExtent - settings.minExtent;
    final double t = clampDouble(
      1.0 - (settings.currentExtent - settings.minExtent) / deltaExtent,
      0.0,
      1.0,
    );
    return t;
  }

  static String timeFormat(num? num) {
    if (num == null || num.isNaN) return '';
    String text = DateTime.fromMillisecondsSinceEpoch(
      int.parse(num.toString()),
    ).format('HH:mm:ss');
    return text;
  }

  static String dateTimeFormat(num? num) {
    if (num == null || num.isNaN) return '';
    String text = DateTime.fromMillisecondsSinceEpoch(
      int.parse(num.toString()),
    ).format('yyyy-MM-dd HH:mm:ss');
    return text;
  }

  static String dateTimeForHourFormat(num? num) {
    if (num == null || num.isNaN) return '';
    String text = DateTime.fromMillisecondsSinceEpoch(
      int.parse(num.toString()),
    ).format('yyyy-MM-dd HH:mm');
    return text;
  }

  static String dateFormat(
    num? num, [
    String pattern = 'yyyy-MM-dd',
    String locale = 'en_US',
  ]) {
    if (num == null || num.isNaN) return '';
    String text = DateTime.fromMillisecondsSinceEpoch(
      int.parse(num.toString()),
    ).format(pattern, locale);
    return text;
  }

  static String dateYearsMonthDayFormat(
    num? num, [
    String pattern = 'yyyy/MM/dd',
    String locale = 'en_US',
  ]) {
    if (num == null || num.isNaN) return '';
    String text = DateTime.fromMillisecondsSinceEpoch(
      int.parse(num.toString()),
    ).format(pattern, locale);
    return text;
  }

  static String formatImg(String url) {
    return url;
  }

  static bool hasMatch(String? value, String pattern) {
    return (value == null) ? false : RegExp(pattern).hasMatch(value);
  }

  /// 格式化小数点位数
  static String toFixed(num value, [int fractionDigits = 2, bool min = false]) {
    if (fractionDigits == 0) {
      return value.toInt().toString();
    }
    String _value = value.toString();
    int dotIndex = _value.lastIndexOf('.');
    // 如果没有小数点
    if (dotIndex < 0) {
      if (min) {
        return _value;
      }
      return value.toStringAsFixed(fractionDigits);
    }
    String _rValue = '';
    if (_value.length - dotIndex - 1 < fractionDigits) {
      // 如果小数点后面位数没有那么多，直接toStringAsFixed
      _rValue = value
          .toStringAsFixed(fractionDigits)
          .substring(0, dotIndex + fractionDigits + 1);
    } else {
      // 否则直接截取对应位数
      _rValue = _value.substring(0, dotIndex + fractionDigits + 1);
    }
    if (min) {
      return _rValue
          .replaceAll(RegExp(r'0+$'), '')
          .replaceAll(RegExp(r'\.$'), '');
    }
    return _rValue;
  }

  /// 超过多少字符省略号
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text; // 文本长度未超过限制，直接返回原文本
    } else {
      return '${text.substring(0, maxLength)}...'; // 文本长度超过限制，添加省略号并返回截断后的文本
    }
  }

  /// 隐藏银行卡号
  static String maskBankno(
    String text, {
    int remainLength = 4,
    int starLength = 6,
  }) {
    if (text.length <= remainLength) {
      return text;
    } else {
      String stars = List.generate(starLength, (index) => '*').join('');
      return '$stars${text.substring(text.length - remainLength)}';
    }
  }

  /// 手机号，邮箱加信号
  static String maskMobileEmail(String text, {int remainLength = 8}) {
    final strs = text.split('@');
    if (strs[0].length <= remainLength) {
      return text;
    } else {
      final length = (strs[0].length / 3).ceil();
      String stars = List.generate(length, (index) => '*').join('');
      return '${text.substring(0, length)}$stars${text.substring(length * 2)}';
    }
  }

  static Function debounce(Function func, int milliseconds) {
    Timer? timer;
    return () {
      timer?.cancel();
      timer = Timer(Duration(milliseconds: milliseconds), () {
        func();
      });
    };
  }

  static bool isBizError(dynamic error, {required String errorCode}) {
    if (error is DioException &&
        error.response != null &&
        error.response!.statusCode == 200 &&
        error.response!.data["errorCode"] == errorCode) {
      return true;
    }
    return false;
  }

  /// 获取AppBar的总高度（包含状态栏）
  static double getAppBarHeight(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final toolbarHeight = kToolbarHeight;
    return statusBarHeight + toolbarHeight;
  }

  /// 获取工具栏高度（不包含状态栏）
  static double getToolbarHeight() {
    return kToolbarHeight;
  }

  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取安全区域高度
  static double getSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top +
        MediaQuery.of(context).padding.bottom;
  }
}
