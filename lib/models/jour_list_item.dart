import 'package:json_annotation/json_annotation.dart';

part 'jour_list_item.g.dart';

@JsonSerializable()
class JourListItem {
  JourListItem();

  late String id;
  late String description;
  late num transAmount;
  late String status;
  late String remark;
  late num createDatetime;
  String? code;

  factory JourListItem.fromJson(Map<String, dynamic> json) =>
      _$JourListItemFromJson(json);
  Map<String, dynamic> toJson() => _$JourListItemToJson(this);
}
