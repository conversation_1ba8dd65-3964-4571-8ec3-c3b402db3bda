import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'translations/en_us.dart';
import 'translations/zh_cn.dart';
import 'translations/ko_kr.dart';
import 'translations/ja_jp.dart';
import 'translations/de_de.dart';
import 'translations/es_es.dart';
import 'translations/fr_fr.dart';
import 'translations/id_id.dart';
import 'translations/ar_ae.dart';
import 'translations/vi_vn.dart';

class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': enUS,
    'zh_CN': zhCN,
    'ko_KR': koKR,
    'ja_JP': jaJP,
    'de_DE': deDE,
    'es_ES': esES,
    'fr_FR': frFR,
    'id_ID': idID,
    'ar_AE': arAE,
    'vi_VN': viVN,
  };

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // 英语
    Locale('zh', 'CN'), // 中文
    Locale('ko', 'KR'), // 韩语
    Locale('ja', 'JP'), // 日语
    Locale('de', 'DE'), // 德语
    Locale('es', 'ES'), // 西班牙语
    Locale('fr', 'FR'), // 法语
    Locale('id', 'ID'), // 印尼语
    Locale('ar', 'AE'), // 阿拉伯语
    Locale('vi', 'VN'), // 越南语
  ];

  static Map<String, String> supportedLocalesString = {
    'en_US': 'English',
    'zh_CN': '简体中文',
    'ko_KR': '한국어',
    'ja_JP': '日本語',
    'de_DE': 'Deutsch',
    'es_ES': 'Español',
    'fr_FR': 'Français',
    'id_ID': 'Bahasa Indonesia',
    'ar_AE': 'العربية',
    'vi_VN': 'Tiếng Việt',
  };

  static const fallbackLocale = Locale('en', 'US');
}
