import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/jour_list_item.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class TransactionHistoryCommissionPage extends StatefulWidget {
  const TransactionHistoryCommissionPage({super.key});

  @override
  _TransactionHistoryCommissionPageState createState() =>
      _TransactionHistoryCommissionPageState();
}

class _TransactionHistoryCommissionPageState
    extends State<TransactionHistoryCommissionPage> {
  late EasyRefreshController _controller;
  TextEditingController nameController = TextEditingController();
  String currentName = '';

  /// 列表
  List<JourListItem> list = [];
  int pageNum = 1;
  bool isEnd = false;

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  _onRefresh() async {
    try {
      await getList(true);
      if (!mounted) {
        return;
      }
    } catch (e) {}
    _controller.finishRefresh();
    _controller.resetFooter();
    _controller.finishLoad(
      isEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  _onLoad() async {
    await getList();
    if (!mounted) {
      return;
    }
    _controller.finishLoad(
      isEnd ? IndicatorResult.noMore : IndicatorResult.success,
    );
  }

  /// 获取列表
  getList([bool isRefresh = false]) async {
    try {
      if (isRefresh) {
        pageNum = 1;
      } else if (isEnd) {
        return;
      }
      final res = await AccountApi.getJourHistoryList({
        "pageNum": pageNum,
        "pageSize": 10,
        "category": '1',
      });
      setState(() {
        isEnd = res.isEnd;
        if (isRefresh) {
          list = res.list;
        } else {
          list.addAll(res.list);
        }
        pageNum++;
      });
    } catch (e) {}
  }

  bool get isEmpty {
    return isEnd && list.isEmpty;
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    // final params = Get.parameters;
    // String? type = params['type'];
    return Scaffold(
      appBar: AppBar(title: Text('Transaction_History'.tr)),
      body: EasyRefresh(
        controller: _controller,
        onLoad: _onLoad,
        onRefresh: _onRefresh,
        refreshOnStart: true,
        refreshOnStartHeader: WidgetUtil.getRefreshOnStartHeader(context),
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 19.w, left: 15.w, right: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _buildItemGroup(customTheme, customThemeImage),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildItemGroup(
    CustomThemeSwatch customTheme,
    CustomThemeImage customThemeImage,
  ) {
    // 按月份分组
    final Map<String, List<JourListItem>> grouped = {};

    for (var item in list) {
      final key = DateTime.fromMillisecondsSinceEpoch(
        item.createDatetime as int,
      ).yyyyMMddHHmmEEE; // e.g. April, 2025

      if (!grouped.containsKey(key)) {
        grouped[key] = [];
      }
      grouped[key]!.add(item);
    }

    // 构建 UI 列表
    List<Widget> widgets = [];

    grouped.forEach((month, items) {
      widgets.addAll([
        Text(
          month,
          style: TextStyle(
            fontSize: 16.w,
            fontWeight: FontWeight.w600,
            color: customTheme.textColor,
          ),
        ),
        SizedBox(height: 14.w),
        Divider(color: customTheme.dividerColor, height: 0.5),
        SizedBox(height: 11.w),
      ]);

      widgets.addAll(
        items
            .map(
              (item) => _buildItem(
                info: item,
                customTheme: customTheme,
                customThemeImage: customThemeImage,
              ),
            )
            .toList(),
      );
      widgets.add(SizedBox(height: 20.w));
    });

    return widgets;
  }

  Widget _buildItem({
    required JourListItem info,
    required CustomThemeSwatch customTheme,
    required CustomThemeImage customThemeImage,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.transactionDetail, parameters: {'id': info.id});
      },
      child: Container(
        padding: EdgeInsets.only(top: 11.w, bottom: 11.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              info.description == '1'
                  ? customThemeImage.homeAdd
                  : customThemeImage.homeReduce,
              width: 22.w,
              height: 22.w,
              fit: BoxFit.fill,
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Thu, 10 Apr 2025',
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                  Text(
                    info.remark,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  if (info.code != null)
                    Text(
                      info.code ?? '',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(width: 10.w),
            Column(
              children: [
                Text(
                  '\$ ${info.transAmount}',
                  style: TextStyle(
                    fontSize: 15.w,
                    fontWeight: FontWeight.w600,
                    color: customTheme.textColor,
                  ),
                ),
                info.status == '0'
                    ? Text(
                        'Pending',
                        style: TextStyle(
                          fontSize: 12.w,
                          fontWeight: FontWeight.w400,
                          color: customTheme.primaryColor,
                        ),
                      )
                    : SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
