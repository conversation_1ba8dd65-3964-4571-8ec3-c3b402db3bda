import 'package:aurenixai_app/l10n/translations.dart';
import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class LanguagePickResult {
  final Locale locale;
  final Region region;

  LanguagePickResult(this.locale, this.region);
}

class LanguagePickModal extends StatefulWidget {
  final LanguagePickResult? initialLanguage;
  const LanguagePickModal({super.key, this.initialLanguage});

  static Future<LanguagePickResult?> showModal(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final languageProvider = context.read<LanguageProvider>();
    if (languageProvider.locale == null || languageProvider.region == null) {
      return Future.value(null);
    }

    return showModalBottomSheet<LanguagePickResult?>(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          child: LanguagePickModal(
            initialLanguage: LanguagePickResult(
              languageProvider.locale!,
              languageProvider.region!,
            ),
          ),
        );
      },
    );
  }

  @override
  State<LanguagePickModal> createState() => _LanguagePickModalState();
}

class _LanguagePickModalState extends State<LanguagePickModal> {
  late ScrollController leftController;
  late ScrollController rightController;

  final List<Locale> locales = AppTranslations.supportedLocales;
  List<Region> regions = [];
  List<int> _languagePickIndex = [0, 0];

  @override
  void initState() {
    // 根据_regions的interName的首字母分组，并排序
    List<Region> _regions = context.read<LanguageProvider>().regionList;
    final groupedRegions = <String, List<Region>>{};
    for (final region in _regions) {
      final key = region.interName[0];
      groupedRegions.putIfAbsent(key, () => []).add(region);
    }
    final entries = groupedRegions.entries.toList();
    entries.sort((a, b) => a.key.compareTo(b.key));
    final sortedRegions = entries.map((entry) => entry.value).toList();
    for (var element in sortedRegions) {
      regions.addAll(element);
    }
    leftController = ScrollController();
    rightController = ScrollController();
    final initialLanguage = widget.initialLanguage;
    int leftInitialItem = 0;
    int rightInitialItem = 0;
    if (initialLanguage != null) {
      leftInitialItem = locales.indexWhere(
        (locale) => locale.languageCode == initialLanguage.locale.languageCode,
      );
      rightInitialItem = regions.indexWhere(
        (region) => region.id == initialLanguage.region.id,
      );
    } else {
      leftInitialItem = 0;
      rightInitialItem = 0;
    }
    _languagePickIndex = [leftInitialItem, rightInitialItem];
    // 滚动到初始位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (leftController.hasClients) {
        leftController.jumpTo(leftInitialItem * (23.w + 20.w));
      }
      if (rightController.hasClients) {
        rightController.jumpTo(rightInitialItem * (23.w + 20.w));
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    leftController.dispose();
    rightController.dispose();
    super.dispose();
  }

  String getTranslations(String name) {
    final locale = locales[_languagePickIndex[0]];
    return Get
        .translations["${locale.languageCode}_${locale.countryCode}"]![name]!;
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(top: 13.5.w, bottom: 30.w),
      child: Center(
        child: Container(
          width: 82.w,
          height: 5.w,
          decoration: BoxDecoration(
            color: customTheme.modalBarColor,
            borderRadius: BorderRadius.circular(2.5.w),
          ),
        ),
      ),
    );
  }

  Widget buildLanguageColumn(ScrollController scrollController) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Expanded(
      child: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          children: List<Widget>.generate(locales.length, (int index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                setState(() {
                  _languagePickIndex[0] = index;
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 11.5.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppTranslations.supportedLocalesString[locales[index]
                              .toString()] ??
                          locales[index].languageCode,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: _languagePickIndex[0] == index
                            ? customTheme.primaryColor
                            : customTheme.textColor,
                        fontWeight: _languagePickIndex[0] == index
                            ? FontWeight.w600
                            : FontWeight.w400,
                      ),
                    ),
                    if (_languagePickIndex[0] == index)
                      Image.asset(customThemeImage.authSelect, width: 14.w),
                  ],
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget buildRegionColumn(ScrollController scrollController) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    String currentLetter = regions[0].interName[0].toUpperCase();
    bool isFirst = true;

    return Expanded(
      child: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          children: List<Widget>.generate(regions.length, (int index) {
            if (regions[index].interName[0].toUpperCase() != currentLetter) {
              currentLetter = regions[index].interName[0].toUpperCase();
              isFirst = true;
            } else if (index > 0) {
              isFirst = false;
            }
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                setState(() {
                  _languagePickIndex[1] = index;
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 11.5.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 24.5.w,
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: currentLetter == 'I' ? 3.w : 0,
                        ),
                        child: isFirst
                            ? Text(
                                currentLetter,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: customTheme.hintTextColor,
                                ),
                              )
                            : SizedBox(),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        getTranslations(regions[index].interName),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: _languagePickIndex[1] == index
                              ? customTheme.primaryColor
                              : customTheme.textColor,
                          fontWeight: _languagePickIndex[1] == index
                              ? FontWeight.w600
                              : FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (_languagePickIndex[1] == index)
                      Image.asset(customThemeImage.authSelect, width: 14.w),
                  ],
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Row(
            children: [
              SizedBox(width: 15.w),
              Expanded(
                child: Text(
                  getTranslations('language'),
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: Text(
                  getTranslations('country_region'),
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(width: 15.w),
            ],
          ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(width: 15.w),
                buildLanguageColumn(leftController),
                Container(
                  width: 1.w,
                  color: customTheme.dividerColor,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                ),
                if (regions.isNotEmpty) buildRegionColumn(rightController),
                SizedBox(width: 15.w),
              ],
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: getTranslations('submit'),
            wrapperColor: customTheme.pageBgColor,
            onSubmit: () {
              LanguageProvider languageProvider = context
                  .read<LanguageProvider>();
              languageProvider.setLanguageAndRegion(
                locales[_languagePickIndex[0]],
                regions[_languagePickIndex[1]],
              );
              EventBusUtil.fireSwitchLanguage(
                SwitchLanguageEvent(
                  locale: locales[_languagePickIndex[0]],
                  region: regions[_languagePickIndex[1]],
                ),
              );
              Get.back(
                result: LanguagePickResult(
                  locales[_languagePickIndex[0]],
                  regions[_languagePickIndex[1]],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
