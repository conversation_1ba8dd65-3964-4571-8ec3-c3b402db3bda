import 'package:json_annotation/json_annotation.dart';

part 'jour_item_res.g.dart';

@JsonSerializable()
class JourItemRes {
  JourItemRes();

  late String id;
  late num transAmount;
  late String status;
  late num createDatetime;
  String? description;
  String? remark;
  String? code;
  String? network;
  String? coupon;
  String? reason;
  String? address;
  String? type;
  String? category;
  String? categoryValue;
  String? bizCategory;
  String? transferMethod;
  String? bizType;

  factory JourItemRes.fromJson(Map<String, dynamic> json) =>
      _$JourItemResFromJson(json);

  Map<String, dynamic> toJson() => _$JourItemResToJson(this);
}
