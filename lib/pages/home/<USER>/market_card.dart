import 'package:aurenixai_app/pages/home/<USER>/market_pick.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import 'market_card_empty_item.dart';
import 'market_card_item.dart';

class MarketCardItemModel {
  final String name;
  final String symbol;
  final String typeName;

  MarketCardItemModel({
    required this.name,
    required this.symbol,
    required this.typeName,
  });
}

class MarketCard extends StatefulWidget {
  const MarketCard({super.key});

  @override
  _MarketCardState createState() => _MarketCardState();
}

class _MarketCardState extends State<MarketCard> {
  final List<MarketCardItemModel> _marketList = [
    MarketCardItemModel(
      name: 'Bitcoin',
      symbol: 'BTC/USD',
      typeName: 'Cryptocurrency',
    ),
    MarketCardItemModel(name: 'AAPL', symbol: 'AAPL', typeName: 'Stocks'),
  ];

  List<MarketCardItemModel> marketList = [];

  @override
  void initState() {
    super.initState();
    LocalUtil.getChooseMarketList().then((symbolList) {
      if (symbolList != null) {
        marketList.addAll(
          symbolList.map(
            (e) => _marketList.firstWhere((element) => element.symbol == e),
          ),
        );
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Column(
      spacing: 20.w,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (marketList.isNotEmpty)
          Text(
            'Markets',
            style: TextStyle(
              fontSize: 16.sp,
              color: customTheme.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        GridView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true, // 让GridView自适应内容高度
          physics: NeverScrollableScrollPhysics(), // 禁止内部滚动，交给外层滚动
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 167 / 195,
            mainAxisSpacing: 11.w,
            crossAxisSpacing: 11.w,
          ),
          itemCount: marketList.isEmpty
              ? 0
              : marketList.length < 2
              ? 2
              : marketList.length,
          itemBuilder: (context, index) {
            if (index < marketList.length) {
              return MarketCardItem(
                title: marketList[index].name,
                typeName: marketList[index].typeName,
                symbol: marketList[index].symbol,
                onTap: () {
                  setState(() {
                    marketList.removeAt(index);
                    LocalUtil.setChooseMarketList(
                      marketList.map((e) => e.symbol).toList(),
                    );
                  });
                },
              );
            }
            return MarketCardEmptyItem(
              type: '1',
              onTap: () {
                MarketPickModal.showModal(
                  context,
                  currentList: marketList,
                ).then((value) {
                  if (value != null) {
                    setState(() {
                      marketList.add(value);
                      LocalUtil.setChooseMarketList(
                        marketList.map((e) => e.symbol).toList(),
                      );
                    });
                  }
                });
              },
            );
          },
        ),
        if (marketList.isEmpty)
          MarketCardEmptyItem(
            type: '0',
            onTap: () {
              MarketPickModal.showModal(context, currentList: marketList).then((
                value,
              ) {
                if (value != null) {
                  setState(() {
                    marketList.add(value);
                    LocalUtil.setChooseMarketList(
                      marketList.map((e) => e.symbol).toList(),
                    );
                  });
                }
              });
            },
          ),
      ],
    );
  }
}
