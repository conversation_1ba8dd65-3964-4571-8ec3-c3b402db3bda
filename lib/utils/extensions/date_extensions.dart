import 'package:get/get.dart';
import 'package:intl/intl.dart';

extension DateExtionsionUtils on DateTime {
  /// 日期格式化
  String get yMMMM {
    final locale = Get.locale;
    return DateFormat('yMMMM', locale.toString()).format(this);
  }

  /// Thu, 10 Apr 2025 08:00
  String get yyyyMMddHHmmEEE {
    final locale = Get.locale;
    return DateFormat('EEE, dd MMM yyyy HH:mm', locale.toString()).format(this);
  }

  /// 22 May 2025
  String get yyyyMMdd {
    final locale = Get.locale;
    return DateFormat('dd MMM yyyy', locale.toString()).format(this);
  }

  /// 1 May 2025 17:15
  String get yyyyMMddHHmm {
    final locale = Get.locale;
    return DateFormat('dd MMM yyyy HH:mm', locale.toString()).format(this);
  }

  /// 2025-05
  String get yyyyMM {
    final locale = Get.locale;
    return DateFormat('yyyy-MM', locale.toString()).format(this);
  }

  /// 30/4/2025
  String get ddMMyyyy {
    final locale = Get.locale;
    return DateFormat('dd/MM/yyyy', locale.toString()).format(this);
  }
}
