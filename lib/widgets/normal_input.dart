import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart'
    show ThemeType;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class NormalInput extends StatelessWidget {
  final double? width;
  final String? hintText;
  final String? label;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final bool obscureText;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Decoration? decoration;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final BoxConstraints? prefixIconConstraints;
  final String? readonlyText;
  final int? minLines;
  final int? maxLines;
  final int? maxLength;
  final double? height;
  final double? labelFontSize;
  final double? fontSize;
  final FontWeight? fontWeight;
  final ValueChanged<String>? onChanged;
  final bool autoHeight;
  final Widget? suffix;
  final Widget? prefix;
  final Widget Function(BuildContext context)? contentBuilder;
  final Color? cursorColor;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? contentStyle;
  final bool hideCounter;
  final TextStyle? counterStyle;
  final Color? borderColor;
  final TextAlign? textAlign;
  final Color? color;
  final bool disabled;
  final void Function(String)? onSubmitted;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final EdgeInsetsGeometry? contentPadding;
  final VoidCallback? onTap;

  const NormalInput({
    super.key,
    this.width,
    this.label,
    this.hintText,
    this.keyboardType,
    this.controller,
    this.margin,
    this.suffixIcon,
    this.prefixIcon,
    this.prefixIconConstraints,
    this.obscureText = false,
    this.readonlyText,
    this.height,
    this.padding,
    this.decoration,
    this.onChanged,
    this.labelFontSize,
    this.fontSize,
    this.fontWeight,
    this.autoHeight = false,
    this.contentBuilder,
    this.suffix,
    this.minLines,
    this.maxLines = 1,
    this.maxLength,
    this.cursorColor,
    this.labelStyle,
    this.hintStyle,
    this.contentStyle,
    this.hideCounter = false,
    this.counterStyle,
    this.borderColor,
    this.textAlign,
    this.color,
    this.disabled = false,
    this.onSubmitted,
    this.textInputAction,
    this.focusNode,
    this.contentPadding,
    this.onTap,
    this.prefix,
  });

  bool get showCounter {
    return !hideCounter && maxLength != null && maxLength! > 0;
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final customTheme = themeProvider.customTheme;
    final currentThemeType = themeProvider.currentThemeType;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8.h,
      children: [
        if (label != null)
          Text(
            label ?? '',
            style:
                labelStyle ??
                TextStyle(
                  fontSize: labelFontSize ?? 13.sp,
                  color: customTheme.textColor,
                ),
          ),
        if (contentBuilder != null)
          contentBuilder!(context)
        else
          Container(
            height: autoHeight ? null : height ?? 55.w,
            width: width ?? double.infinity,
            alignment: Alignment.centerLeft,
            margin: margin,
            padding: padding ?? EdgeInsets.symmetric(horizontal: 19.w),
            decoration:
                decoration ??
                BoxDecoration(
                  borderRadius: BorderRadius.circular(3.w),
                  color: customTheme.inputBgColor,
                ),
            child: Row(
              children: [
                if (prefix != null) prefix!,
                Expanded(
                  child: readonlyText != null
                      ? Text(
                          readonlyText!,
                          style:
                              contentStyle ??
                              TextStyle(
                                fontSize: fontSize ?? 15.sp,
                                color: color ?? customTheme.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                          textAlign: textAlign ?? TextAlign.left,
                        )
                      : TextField(
                          onTap: onTap,
                          focusNode: focusNode,
                          readOnly: disabled,
                          controller: controller,
                          keyboardType: keyboardType,
                          obscureText: obscureText,
                          keyboardAppearance: currentThemeType == ThemeType.dark
                              ? Brightness.dark
                              : Brightness.light,
                          style:
                              contentStyle ??
                              TextStyle(
                                fontSize: fontSize ?? 15.sp,
                                color: color ?? customTheme.textColor,
                                fontWeight: fontWeight ?? FontWeight.w600,
                              ),
                          onChanged: onChanged,
                          minLines: minLines,
                          maxLines: maxLines,
                          textAlign: textAlign ?? TextAlign.left,
                          decoration: InputDecoration(
                            hintText: hintText,
                            hintStyle:
                                hintStyle ??
                                TextStyle(color: customTheme.inputHintColor),
                            border: InputBorder.none,
                            suffixIcon: suffixIcon,
                            prefixIcon: prefixIcon,
                            prefixIconConstraints: prefixIconConstraints,
                            contentPadding: contentPadding,
                          ),
                          cursorColor: cursorColor ?? customTheme.textColor,
                          textInputAction: textInputAction,
                          maxLength: maxLength,
                          onSubmitted: onSubmitted,
                          buildCounter: showCounter
                              ? (
                                  context, {
                                  required currentLength,
                                  required isFocused,
                                  maxLength,
                                }) {
                                  return Text(
                                    '$currentLength/$maxLength',
                                    style:
                                        counterStyle ??
                                        TextStyle(
                                          fontSize: 13.sp,
                                          color: customTheme.inputHintColor,
                                        ),
                                  );
                                }
                              : (
                                  context, {
                                  required currentLength,
                                  required isFocused,
                                  maxLength,
                                }) {
                                  return null;
                                },
                        ),
                ),
                if (suffix != null) suffix!,
              ],
            ),
          ),
      ],
    );
  }
}
