import 'package:json_annotation/json_annotation.dart';

part 'investment_summary.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class InvestmentSummary {
  InvestmentSummary();

  late String productId;
  late num totalInvested;
  late num totalRedeemed;
  late num totalReturns;
  late num activeCount;
  late num redeemedCount;
  late String productName;

  factory InvestmentSummary.fromJson(Map<String, dynamic> json) =>
      _$InvestmentSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$InvestmentSummaryToJson(this);
}
