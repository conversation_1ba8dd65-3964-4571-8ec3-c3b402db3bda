import 'package:aurenixai_app/api/address_api.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/common_picker.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/normal_select.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddressBookAddPage extends StatefulWidget {
  const AddressBookAddPage({super.key});

  @override
  _AddressBookAddPageState createState() => _AddressBookAddPageState();
}

class _AddressBookAddPageState extends State<AddressBookAddPage>
    with SingleTickerProviderStateMixin {
  TextEditingController addressController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  List<String> currencyList = ['USDT', 'USDC'];
  int currencyIndex = 0;

  List<String> networkList = ['TRC20'];
  int networkIndex = 0;

  bool isAgree = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    addressController.dispose();
    remarkController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    if (addressController.text.isEmpty) {
      ToastUtil.showToast('please_enter_address'.tr);
      return;
    }
    try {
      ToastUtil.showLoading();
      await AddressApi.addressAdd({
        'symbol': currencyList[currencyIndex],
        'address': addressController.text,
        'network': networkList[networkIndex],
        'remark': remarkController.text,
        'trustFlag': isAgree ? '1' : '0',
      });

      ToastUtil.dismiss();
      Get.back(result: {'isAdd': true});
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    List<String> currencyImage = [
      customThemeImage.accountCurrencyUsdt,
      customThemeImage.accountCurrencyUsdc,
    ];
    return Scaffold(
      appBar: AppBar(title: Text('add_address'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 6.h),
                  NormalSelect(
                    label: 'Currency'.tr,
                    hintText: 'select_currency'.tr,
                    content: currencyList[currencyIndex],
                    prefix: Image.asset(
                      currencyImage[currencyIndex],
                      width: 22.w,
                      height: 22.w,
                      fit: BoxFit.fill,
                    ),
                    onTap: () {
                      CommonPicker.showModal(
                            context,
                            title: 'select_currency'.tr,
                            list: currencyList,
                            contentBuilder: (context, index) => Row(
                              children: [
                                Image.asset(
                                  currencyImage[index],
                                  width: 22.w,
                                  height: 22.w,
                                  fit: BoxFit.fill,
                                ),
                                SizedBox(width: 7.w),
                                Text(
                                  currencyList[index],
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    color: customTheme.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          )
                          .then((index) {
                            if (index != null) {
                              setState(() {
                                currencyIndex = index;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                  ),
                  SizedBox(height: 18.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "Address".tr,
                        hintText: 'enter_address'.tr,
                        controller: addressController,
                        padding: EdgeInsets.only(left: 15.w, right: 100.w),
                      ),
                      Positioned(
                        bottom: 18.w,
                        right: 40.w,
                        width: 20.w,
                        height: 20.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          child: Image.asset(
                            customThemeImage.sendChainIconCopy,
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.fill,
                          ),
                          onTap: () {},
                        ),
                      ),
                      Positioned(
                        bottom: 18.w,
                        right: 12.w,
                        width: 20.w,
                        height: 20.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          child: Image.asset(
                            customThemeImage.sendChainIconScan,
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.fill,
                          ),
                          onTap: () {},
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 18.h),
                  NormalSelect(
                    label: 'Network'.tr,
                    hintText: 'select_your_network'.tr,
                    content: networkList[networkIndex],
                    onTap: () {
                      CommonPicker.showModal(
                            context,
                            title: 'select_network'.tr,
                            list: networkList,
                            contentBuilder: (context, index) => Row(
                              children: [
                                Text(
                                  networkList[index],
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    color: customTheme.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          )
                          .then((index) {
                            if (index != null) {
                              setState(() {
                                networkIndex = index;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                  ),
                  SizedBox(height: 18.h),
                  NormalInput(
                    label: "Remark_optional".tr,
                    hintText: '20_character_remark'.tr,
                    controller: remarkController,
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomWrapper(
            context: context,
            color: customTheme.pageBgColor,
            decoration: const BoxDecoration(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 12.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          isAgree = !isAgree;
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.only(top: 3.w),
                        child: Image.asset(
                          isAgree
                              ? customThemeImage.authChoose
                              : customThemeImage.authUnchoose,
                          width: 18.w,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        'set_trusted_payee_to_skip'.tr,
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: customTheme.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 35.h),
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: 7.w,
                    horizontal: 15.w,
                  ),
                  decoration: BoxDecoration(
                    color: customTheme.inputBgColor,
                    border: Border(
                      top: BorderSide(
                        color: customTheme.dividerColor,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: PrimaryButton(
                    title: 'Confirm'.tr,
                    height: 45.w,
                    onPress: onSubmit,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
