import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class TeamActionSuccessfullyPage extends StatefulWidget {
  const TeamActionSuccessfullyPage({super.key});

  @override
  _TeamActionSuccessfullyPageState createState() =>
      _TeamActionSuccessfullyPageState();
}

class _TeamActionSuccessfullyPageState
    extends State<TeamActionSuccessfullyPage> {
  String name = '';
  String levelName = '';

  @override
  void initState() {
    super.initState();
    name = Get.parameters['name'] ?? '';
    levelName = Get.parameters['levelName'] ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('details'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          children: [
            SizedBox(height: 73.h),
            Image.asset(
              customThemeImage.googleSuccess1,
              width: 80.w,
              height: 80.w,
              fit: BoxFit.fill,
            ),
            SizedBox(height: 37.h),
            Text(
              'tier_advancement_request_submitted'.tr,
              style: TextStyle(
                fontSize: 20.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 26.5.h),
            Text.rich(
              textAlign: TextAlign.justify,
              TextSpan(
                style: TextStyle(fontSize: 14.sp, color: customTheme.textColor),
                children: [
                  TextSpan(text: "your_request_to_upgrade".tr),
                  TextSpan(
                    text: " $name ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: customTheme.primaryColor,
                    ),
                  ),
                  TextSpan(text: "${'to'.tr} "),
                  TextSpan(
                    text: "$levelName ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: customTheme.primaryColor,
                    ),
                  ),
                  TextSpan(text: "has_been_sent".tr),
                ],
              ),
            ),
            SizedBox(height: 29.h),
          ],
        ),
      ),
    );
  }
}
