import 'package:aurenixai_app/pages/services/utils/identity_util.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/common_image.dart';
import 'package:aurenixai_app/widgets/invite_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class UserCard extends StatefulWidget {
  const UserCard({super.key});

  @override
  _UserCardState createState() => _UserCardState();
}

class _UserCardState extends State<UserCard> {
  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    final authInfo = context.watch<AuthProvider>();
    final user = authInfo.user;

    if (user == null) {
      return SizedBox.shrink();
    }

    final level = user.levelName;
    final levelPic = user.levelPic;

    return Row(
      spacing: 6.w,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 5.5.w,
            children: [
              Row(
                spacing: 8.5.w,
                children: [
                  Text(
                    '${"Hello".tr}, ${user.nickname}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Stack(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 2.5.h),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: IdentityUtil.getLevelGradient(
                              level,
                              context,
                            ),
                            borderRadius: BorderRadius.circular(8.5.w),
                          ),
                          height: 16.5.w,
                          padding: EdgeInsets.only(left: 23.5.w, right: 5.5.w),
                          alignment: Alignment.center,
                          child: Text(
                            level,
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: IdentityUtil.getLevelColor(level, context),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        left: 3.w,
                        top: 0,
                        child: CommonImage(
                          levelPic,
                          height: 19.5.w,
                          type: CommonImageType.square,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Wrap(
                spacing: 8.w,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'UID: ${user.userId}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.subTextColor,
                    ),
                  ),
                  Text(
                    authInfo.isKyc ? 'verified'.tr : 'unverified'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: authInfo.isKyc
                          ? customTheme.ff7ed032
                          : customTheme.downColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (user.nodeLevel >= 2)
          GestureDetector(
            onTap: () {
              InviteModal.showModal(context: context);
            },
            child: Image.asset(
              customThemeImage.homeShare,
              width: 22.w,
              height: 22.w,
            ),
          ),
      ],
    );
  }
}
