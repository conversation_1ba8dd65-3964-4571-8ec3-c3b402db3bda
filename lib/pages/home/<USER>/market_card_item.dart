import 'dart:async';

import 'package:aurenixai_app/api/kline_api.dart';
import 'package:aurenixai_app/models/kline_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:chart_sparkline/chart_sparkline.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class MarketCardItem extends StatefulWidget {
  const MarketCardItem({
    super.key,
    required this.title,
    required this.typeName,
    required this.symbol,
    required this.onTap,
    this.isAdd = false,
  });
  final String title;
  final String typeName;
  final String symbol;
  final VoidCallback onTap;
  final bool isAdd;

  @override
  _MarketCardItemState createState() => _MarketCardItemState();
}

class _MarketCardItemState extends State<MarketCardItem> {
  bool showDelete = false;
  late StreamSubscription<ReceiveWebSocketMessageEvent> klineSubscription;
  List<KlineRes> klineList = [];
  KlineRes? latestKline;

  @override
  void initState() {
    super.initState();
    getKlineData();
    klineSubscription = EventBusUtil.listenReceiveWebSocketMessage((event) {
      if (event.webSocketRes.ch == 'kline.data' &&
          event.webSocketRes.data != null &&
          event.webSocketRes.data is KlineRes) {
        final data = event.webSocketRes.data as KlineRes;
        if (data.symbol == widget.symbol) {
          // 获取最新价格
          if (klineList.isNotEmpty && latestKline != null) {
            // 判断latestKline的date和data的date的日期关系
            final latestDate = DateTime.fromMillisecondsSinceEpoch(
              latestKline!.date.toInt(),
            );
            final newDate = DateTime.fromMillisecondsSinceEpoch(
              data.date.toInt(),
            );

            final isSameDay =
                latestDate.year == newDate.year &&
                latestDate.month == newDate.month &&
                latestDate.day == newDate.day;

            // 比较日期大小
            final isNewDateLater = data.date > latestKline!.date;

            setState(() {
              if (isNewDateLater) {
                if (isSameDay) {
                  // 1、如果同一天，则把klineList的最后一个元素更新为data，同时更新latestKline
                  klineList[klineList.length - 1] = data;
                  latestKline = data;
                } else {
                  // 2、如果data.date比latestKline.date大，且不是同一天，则data添加到klineList的末尾，同时更新latestKline
                  klineList.add(data);
                  latestKline = data;
                }
              }
              // 3、如果是比data.date的日期小，则不处理
            });
          }
        }
      }
    });
  }

  @override
  void dispose() {
    klineSubscription.cancel();
    super.dispose();
  }

  getKlineData() async {
    try {
      final res = await KlineApi.getKlineList(widget.symbol);
      setState(() {
        klineList = res.reversed.toList();
        latestKline = klineList.last;
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Widget _buildChart() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final color = latestKline != null && latestKline!.changePct.toDouble() >= 0
        ? customTheme.upColor
        : customTheme.downColor;

    // 最简单的测试数据
    final data = klineList.map((e) => e.rate.toDouble()).toList();

    return SizedBox(
      height: 50.w,
      child: Sparkline(
        data: data,
        lineColor: color,
        lineWidth: 1.w,
        fillMode: FillMode.below,
        fillGradient: LinearGradient(
          colors: [color.withValues(alpha: 0.26), color.withValues(alpha: 0.0)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    Widget content = Container(
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(3.w),
      ),
      padding: EdgeInsets.symmetric(horizontal: 17.w, vertical: 15.5.w),
      child: latestKline != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (!widget.isAdd && showDelete)
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          widget.onTap();
                        },
                        child: Container(
                          width: 22.w,
                          height: 22.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: customTheme.dividerColor,
                          ),
                          alignment: Alignment.center,
                          child: Icon(
                            Icons.close_rounded,
                            color: customTheme.textColor,
                            size: 15.w,
                          ),
                        ),
                      ),
                    if (widget.isAdd)
                      GestureDetector(
                        onTap: widget.onTap,
                        child: Container(
                          width: 22.w,
                          height: 22.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: customTheme.primaryColor,
                          ),
                          alignment: Alignment.center,
                          child: Icon(
                            Icons.add_rounded,
                            color: customTheme.textColor,
                            size: 15.w,
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 3.w),
                Text(
                  '\$ ${latestKline?.rate.toString().fmt}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: customTheme.subTextColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 10.w),
                _buildChart(),
                SizedBox(height: 14.w),
                Row(
                  spacing: 4.5.w,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Image.asset(
                      (latestKline!.changePct.toDouble()) >= 0
                          ? customThemeImage.homeUp
                          : customThemeImage.homeDown,
                      width: 12.w,
                      height: 12.w,
                    ),
                    Text(
                      '${(latestKline!.changePct).toStringAsFixed(2)}%',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: (latestKline!.changePct.toDouble()) >= 0
                            ? customTheme.upColor
                            : customTheme.downColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 7.w),
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    widget.typeName,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.hintTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            )
          : Center(
              child: CircularProgressIndicator(color: customTheme.textColor),
            ),
    );

    if (!widget.isAdd) {
      return GestureDetector(
        onLongPress: () {
          setState(() {
            showDelete = !showDelete;
          });
        },
        onTap: () {
          setState(() {
            showDelete = false;
          });
        },
        child: content,
      );
    }
    return content;
  }
}
