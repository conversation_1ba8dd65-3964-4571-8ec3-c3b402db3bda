import 'package:get/get.dart';
import 'package:intl/intl.dart';

extension StringExtionsionUtils on String {
  /// 是否是邮箱
  bool get isEmail => RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  ).hasMatch(this);

  /// 是否是手机号
  bool get isPhone => RegExp(r'^1[3-9]\d{9}$').hasMatch(this);

  /// 数字格式化
  String get fmt {
    if (isNum) {
      var formatter = NumberFormat(',###.##');
      double number = double.parse(this);
      return formatter.format(number);
    }
    return this;
  }

  /// 去掉小数点后面的0
  String get removeZero {
    if (contains('.')) {
      return replaceAllMapped(RegExp(r'\.?0*$'), (match) => '');
    }
    return this;
  }

  /// mask email
  String get maskEmail {
    // 判断下长度
    return '${substring(0, 1)}*****${substring(length - 4)}';
  }
}
