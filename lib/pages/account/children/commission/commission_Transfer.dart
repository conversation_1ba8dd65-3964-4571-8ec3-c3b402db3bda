import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CommissionTransferPage extends StatefulWidget {
  const CommissionTransferPage({super.key});

  @override
  _CommissionTransferPageState createState() => _CommissionTransferPageState();
}

class _CommissionTransferPageState extends State<CommissionTransferPage>
    with SingleTickerProviderStateMixin {
  TextEditingController amountController = TextEditingController();

  AccountRes? accountData;

  @override
  void initState() {
    super.initState();
    _getMyAccount();
  }

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  Future<void> _getMyAccount() async {
    try {
      final res = await AccountApi.getMyAccount();
      setState(() {
        accountData = res;
      });
    } catch (e) {}
  }

  void onSubmit() async {
    Get.back();
    if (amountController.text.isEmpty) {
      ToastUtil.showToast('please_enter_amount'.tr);
      return;
    }
    ToastUtil.showLoading();
    try {
      await AccountApi.commissionToFundling({'amount': amountController.text});
      ToastUtil.dismiss();
      Get.back();
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Commission_Account'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 8.h,
                children: [
                  SizedBox(height: 27.h),
                  // NormalInput(
                  //   label: "Account_ID".tr,
                  //   hintText: 'Enter_your_account'.tr,
                  //   controller: amountController,
                  // ),
                  // SizedBox(height: 20.5.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "Transfer_amount".tr,
                        hintText: 'Enter_amount'.tr,
                        controller: amountController,
                        padding: EdgeInsets.only(left: 15.w, right: 80.w),
                      ),
                      Positioned(
                        bottom: 15.w,
                        right: 15.w,
                        child: PrimaryButton(
                          title: 'Max'.tr,
                          width: 50.w,
                          height: 28.w,
                          onPress: () {
                            setState(() {
                              amountController.text =
                                  accountData?.rewardAvailableAmount ?? '0';
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'Transfer'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }
}
