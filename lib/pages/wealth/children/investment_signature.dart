import 'dart:async';

import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/pages/public/rich_text_config.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/aws_upload_util.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:flutter/gestures.dart' show TapGestureRecognizer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:signature/signature.dart';

class InvestmentSignaturePage extends StatefulWidget {
  const InvestmentSignaturePage({super.key});

  @override
  State<InvestmentSignaturePage> createState() =>
      _InvestmentSignaturePageState();
}

class _InvestmentSignaturePageState extends State<InvestmentSignaturePage> {
  // initialize the signature controller
  late final SignatureController _controller;

  @override
  void initState() {
    super.initState();
    final customTheme = context.read<ThemeProvider>().customTheme;

    /// 强制竖屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    _controller = SignatureController(
      penStrokeWidth: 10,
      strokeCap: StrokeCap.butt,
      strokeJoin: StrokeJoin.miter,
      penColor: customTheme.textColor,
      exportBackgroundColor: customTheme.inputBgColor,
      exportPenColor: customTheme.textColor,
      onDrawStart: () => debugPrint('onDrawStart called!'),
      onDrawEnd: () => debugPrint('onDrawEnd called!'),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  void onConfirm() async {
    String imageUrl = '';
    try {
      final signature = await _controller.toPngBytes();
      if (signature == null) {
        ToastUtil.showToast('please_provide_your_digital_signature'.tr);
        return;
      }
      debugPrint('signature: $signature');
      ToastUtil.showLoading();
      imageUrl = await AwsUploadUtil().uploadByUint8List(bytes: signature);
    } catch (e) {
      ToastUtil.dismiss();
      ToastUtil.showToast('failed_to_upload_signature'.tr);
      return;
    }
    try {
      final res = await InvestmentApi.investmentProduct({
        'productId': Get.parameters['productId'] ?? '',
        'initialAmount': Get.parameters['amount'] ?? '',
        'sign': imageUrl,
      });
      ToastUtil.dismiss();
      Get.offAndToNamed(
        Routes.investmentSuccessfully,
        parameters: {
          'name': Get.parameters['name'] ?? '',
          'interestRatePerCycle': Get.parameters['interestRatePerCycle'] ?? '',
          'interestCycleDays': Get.parameters['interestCycleDays'] ?? '',
          'lockinPeriodDays': Get.parameters['lockinPeriodDays'] ?? '',
          'amount': Get.parameters['amount'] ?? '',
          'createDatetime': res.createDatetime.toString(),
          'nextInterestDate': res.nextInterestDate.toString(),
        },
      );
      EventBusUtil.fireInvestmentSuccess(InvestmentSuccessEvent());
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text('signature_required'.tr),
        actions: [
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: customTheme.buttonBgColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3.r),
                side: BorderSide(color: customTheme.buttonBgColor),
              ),
            ),
            onPressed: () {
              _controller.clear();
            },
            child: Text('clear'.tr),
          ),
          SizedBox(width: 4.w),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: customTheme.textColor,
              backgroundColor: customTheme.buttonBgColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3.r),
              ),
            ),
            onPressed: onConfirm,
            child: Text('confirm'.tr),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text.rich(
                TextSpan(
                  style: TextStyle(fontSize: 13, color: customTheme.textColor),
                  children: [
                    TextSpan(text: "${"signature_description".tr} "),
                    TextSpan(
                      text: "investment_terms_and_conditions".tr,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: customTheme.primaryColor,
                        decoration: TextDecoration.underline,
                        decorationColor: customTheme.primaryColor,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          try {
                            Timer.run(() {
                              SystemChrome.setPreferredOrientations([
                                DeviceOrientation.portraitUp,
                                DeviceOrientation.portraitDown,
                              ]);
                            });
                            await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RichTextConfig(
                                  title: 'investment_terms_and_conditions'.tr,
                                  configKey: 'term',
                                  configType: 'system',
                                ),
                              ),
                            );
                            Timer.run(() {
                              SystemChrome.setPreferredOrientations([
                                DeviceOrientation.landscapeLeft,
                                DeviceOrientation.landscapeRight,
                              ]);
                            });
                          } catch (e) {}
                        },
                    ),
                    TextSpan(text: "."),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(3.r),
                  child: Signature(
                    controller: _controller,
                    backgroundColor: customTheme.inputBgColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
