import 'package:json_annotation/json_annotation.dart';

part 'websocket_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class WebSocketRes {
  WebSocketRes();

  late String ch;
  late String status;
  dynamic data;

  factory WebSocketRes.fromJson(
    Map<String, dynamic> json,
    dynamic Function(Map<String, dynamic> json) fromJson,
  ) => _$WebSocketResFromJson(json, fromJson);
  Map<String, dynamic> toJson(
    Map<String, dynamic> Function(dynamic value) toJson,
  ) => _$WebSocketResToJson(this, toJson);
}
