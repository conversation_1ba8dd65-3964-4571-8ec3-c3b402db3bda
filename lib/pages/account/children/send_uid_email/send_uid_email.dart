import 'package:aurenixai_app/api/account_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/account_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SendUidEmailPage extends StatefulWidget {
  const SendUidEmailPage({super.key});

  @override
  _SendUidEmailPageState createState() => _SendUidEmailPageState();
}

class _SendUidEmailPageState extends State<SendUidEmailPage>
    with SingleTickerProviderStateMixin {
  TextEditingController emailController = TextEditingController();
  TextEditingController uidController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  late TabController _tabController;
  int currentTabIndex = 0;
  String? addressBookId;
  String? trustFlag;

  AccountRes? accountData;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _getMyAccount();
  }

  @override
  void dispose() {
    emailController.dispose();
    uidController.dispose();
    amountController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _getMyAccount() async {
    try {
      final res = await AccountApi.getMyAccount();
      setState(() {
        accountData = res;
      });
    } catch (e) {}
  }

  void onSubmit() async {
    final result = await Get.toNamed(
      Routes.sendUidEmailConfirm,
      parameters: {
        'addressBookId': addressBookId ?? '',
        'address': currentTabIndex == 0
            ? emailController.text
            : uidController.text,
        'amount': amountController.text,
        'addressType': currentTabIndex == 0 ? '0' : '1',
        'trustFlag': trustFlag ?? '0',
      },
    );
    if (result != null && result['success'] == true) {
      Get.back(result: {'success': true});
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    return Scaffold(
      appBar: AppBar(title: Text('Send'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _tabs(),
                  SizedBox(height: 20.h),
                  currentTabIndex == 0
                      ? Stack(
                          children: [
                            NormalInput(
                              label: "email".tr,
                              hintText: 'enter_email'.tr,
                              controller: emailController,
                              padding: EdgeInsets.only(
                                left: 15.w,
                                right: 100.w,
                              ),
                            ),
                            Positioned(
                              bottom: 18.w,
                              right: 12.w,
                              width: 20.w,
                              height: 20.w,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                child: Image.asset(
                                  customThemeImage.sendChainIconAddress,
                                  width: 20.w,
                                  height: 20.w,
                                  fit: BoxFit.fill,
                                ),
                                onTap: () async {
                                  final result = await Get.toNamed(
                                    Routes.addressBook,
                                    parameters: {'type': 'email'},
                                  );
                                  setState(() {
                                    addressBookId = result['id'];
                                    emailController.text = result['address'];
                                    trustFlag = result['trustFlag'];
                                  });
                                },
                              ),
                            ),
                            Positioned(
                              bottom: 18.w,
                              right: 40.w,
                              width: 20.w,
                              height: 20.w,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                child: Image.asset(
                                  customThemeImage.sendChainIconScan,
                                  width: 20.w,
                                  height: 20.w,
                                  fit: BoxFit.fill,
                                ),
                                onTap: () {},
                              ),
                            ),
                            Positioned(
                              bottom: 18.w,
                              right: 68.w,
                              width: 20.w,
                              height: 20.w,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                child: Image.asset(
                                  customThemeImage.sendChainIconCopy,
                                  width: 20.w,
                                  height: 20.w,
                                  fit: BoxFit.fill,
                                ),
                                onTap: () {},
                              ),
                            ),
                          ],
                        )
                      : Stack(
                          children: [
                            NormalInput(
                              label: "UID".tr,
                              hintText: 'enter_uid'.tr,
                              controller: uidController,
                              padding: EdgeInsets.only(
                                left: 15.w,
                                right: 100.w,
                              ),
                            ),
                            Positioned(
                              bottom: 18.w,
                              right: 12.w,
                              width: 20.w,
                              height: 20.w,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                child: Image.asset(
                                  customThemeImage.sendChainIconAddress,
                                  width: 20.w,
                                  height: 20.w,
                                  fit: BoxFit.fill,
                                ),
                                onTap: () async {
                                  final result = await Get.toNamed(
                                    Routes.addressBook,
                                    parameters: {'type': 'uid'},
                                  );
                                  setState(() {
                                    addressBookId = result['id'];
                                    uidController.text = result['address'];
                                    trustFlag = result['trustFlag'];
                                  });
                                },
                              ),
                            ),
                            Positioned(
                              bottom: 18.w,
                              right: 40.w,
                              width: 20.w,
                              height: 20.w,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                child: Image.asset(
                                  customThemeImage.sendChainIconScan,
                                  width: 20.w,
                                  height: 20.w,
                                  fit: BoxFit.fill,
                                ),
                                onTap: () {},
                              ),
                            ),
                            Positioned(
                              bottom: 18.w,
                              right: 68.w,
                              width: 20.w,
                              height: 20.w,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                child: Image.asset(
                                  customThemeImage.sendChainIconCopy,
                                  width: 20.w,
                                  height: 20.w,
                                  fit: BoxFit.fill,
                                ),
                                onTap: () {},
                              ),
                            ),
                          ],
                        ),
                  SizedBox(height: 20.5.h),
                  Stack(
                    children: [
                      NormalInput(
                        label: "Transfer_amount".tr,
                        hintText: 'Enter_amount'.tr,
                        controller: amountController,
                        padding: EdgeInsets.only(left: 15.w, right: 80.w),
                      ),
                      Positioned(
                        bottom: 15.w,
                        right: 15.w,
                        child: PrimaryButton(
                          title: 'Max'.tr,
                          width: 50.w,
                          height: 28.w,
                          onPress: () {
                            setState(() {
                              amountController.text =
                                  accountData?.availableAmount ?? '0';
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'Minimum: \$ 1'.tr,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'Transfer'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }

  Widget _tabs() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    // 获取当前文字方向，如果是从右到左，则需要将TabBar的labelPadding设置为0
    final textDirection = Directionality.of(context);
    final labelPadding = textDirection == TextDirection.rtl
        ? EdgeInsets.only(left: 54.w)
        : EdgeInsets.only(right: 54.w);

    return Padding(
      padding: EdgeInsets.only(top: 7.h),
      child: TabBar(
        controller: _tabController,
        onTap: (index) {
          setState(() {
            currentTabIndex = index;
            emailController.text = '';
            uidController.text = '';
            amountController.text = '';
          });
        },
        indicatorWeight: 3.w,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 6.w),
        isScrollable: true,
        dividerHeight: 0.5,
        tabAlignment: TabAlignment.start,
        dividerColor: customTheme.dividerColor,
        labelPadding: labelPadding,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.sp,
          color: customTheme.lightTextColor,
          fontWeight: FontWeight.w600,
        ),
        tabs: [
          Tab(child: Text('email'.tr)),
          Tab(child: Text('UID'.tr)),
        ],
      ),
    );
  }
}
