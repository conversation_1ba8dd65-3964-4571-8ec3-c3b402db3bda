import 'dart:async' show Completer;

import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';

class InvestmentTip extends StatefulWidget {
  const InvestmentTip({super.key});

  static Future<T?> showModal<T>({required BuildContext context}) async {
    return showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return InvestmentTip();
      },
    );
  }

  @override
  State<InvestmentTip> createState() => _InvestmentTipState();
}

class _InvestmentTipState extends State<InvestmentTip> {
  final Completer<String> noticeText = Completer<String>();

  @override
  void initState() {
    super.initState();
    getNotice();
  }

  Future<void> getNotice() async {
    try {
      final res = await CommonApi.getConfig(type: 'system', key: 'notice');
      String msg = await getHtml(res.data["notice"]!);
      noticeText.complete(msg);
    } catch (e) {
      e.printError();
      noticeText.completeError("Error");
    }
  }

  Future<String> getHtml(String msg) async {
    String cssText = await DefaultAssetBundle.of(
      context,
    ).loadString("assets/css/tinymce.content.min.css");
    String text =
        '''
<!DOCTYPE html><html lang="zh">
<head><meta charset="utf-8"><style>$cssText</style></head>
<body class="mce-content-body">
$msg
</body>
</html>
''';
    return text;
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.w)),
      insetPadding: EdgeInsets.symmetric(horizontal: 26.w),
      child: Container(
        width: 323.w,
        padding: EdgeInsets.fromLTRB(23.5.w, 18.w, 14.w, 29.5.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.w),
          color: customTheme.inputBgColor,
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 16.w, right: 9.5.w),
              child: FutureBuilder(
                future: noticeText.future,
                builder: (context, AsyncSnapshot<String> snapshot) {
                  if (snapshot.hasData) {
                    return ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: 308.h,
                        minHeight: 200.h,
                      ),
                      child: SingleChildScrollView(
                        child: HtmlWidget(
                          snapshot.data ?? '',
                          textStyle: TextStyle(color: customTheme.textColor),
                        ),
                      ),
                    );
                  } else if (snapshot.hasError) {
                    return SizedBox(
                      height: 200.h,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: customTheme.downColor,
                              size: 60,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Text(snapshot.error.toString()),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                  return SizedBox(
                    height: 200.h,
                    child: Center(child: WidgetUtil.getLoadingIcon(context)),
                  );
                },
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: IconButton.styleFrom(
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                ),
                iconSize: 26.w,
                icon: Icon(Icons.close_rounded, color: customTheme.textColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
