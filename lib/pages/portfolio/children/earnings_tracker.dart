import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/models/user_node_level_list_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class EarningsTrackerPage extends StatefulWidget {
  const EarningsTrackerPage({super.key});

  @override
  _EarningsTrackerPageState createState() => _EarningsTrackerPageState();
}

class _EarningsTrackerPageState extends State<EarningsTrackerPage> {
  bool loading = true;
  ScrollController scrollController = ScrollController();
  double sliderValue = 0.0;
  List<UserNodeLevelListRes> earningList = [];
  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    getEarningList();
    scrollController.addListener(() {
      setState(() {
        sliderValue =
            scrollController.position.pixels /
            scrollController.position.maxScrollExtent;
      });
    });
  }

  getEarningList() async {
    try {
      final res = await UserApi.getEarningUserNodeLevelList();
      earningList = res;
      isEmpty = earningList.isEmpty;
    } catch (e) {
      e.printError();
    }
    loading = false;
    if (!mounted) {
      return;
    }
    setState(() {});
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: Text('earnings_tracker'.tr)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
        child: Container(
          decoration: BoxDecoration(
            color: customTheme.inputBgColor,
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(color: customTheme.dividerColor, width: 0.5),
          ),
          margin: EdgeInsets.only(bottom: 40.h),
          child: loading
              ? Padding(
                  padding: EdgeInsets.symmetric(vertical: 40.h),
                  child: Center(
                    child: CircularProgressIndicator(
                      color: customTheme.textColor,
                    ),
                  ),
                )
              : Column(
                  children: [
                    SingleChildScrollView(
                      controller: scrollController,
                      scrollDirection: Axis.horizontal,
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        children: [
                          _tableHead(),
                          if (isEmpty)
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 40.h),
                              child: Center(
                                child: Text(
                                  'no_record_found'.tr,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: customTheme.subTextColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            )
                          else
                            ..._tableBody(),
                        ],
                      ),
                    ),
                    Slider(
                      thumbColor: customTheme.primaryColor,
                      activeColor: customTheme.primaryColor,
                      inactiveColor: customTheme.dividerColor,
                      value: sliderValue,
                      onChanged: (value) {
                        setState(() {
                          sliderValue = value;
                        });
                        scrollController.jumpTo(
                          value * scrollController.position.maxScrollExtent,
                        );
                      },
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  _tableHeadCell(
    double width, {
    String? title,
    Widget? child,
    bool isSmall = false,
    bool isLast = false,
  }) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    var textStyle = TextStyle(
      fontSize: 12.sp,
      color: customTheme.inputHintColor,
      fontWeight: FontWeight.w600,
    );
    var smallTextStyle = textStyle.copyWith(fontSize: 10.sp);

    return Container(
      decoration: isLast
          ? null
          : BoxDecoration(
              border: Border(
                right: BorderSide(color: customTheme.dividerColor, width: 0.5),
              ),
            ),
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      width: width,
      height: 53.h,
      child: Center(
        child:
            child ??
            Text(
              title ?? '',
              style: isSmall ? smallTextStyle : textStyle,
              textAlign: TextAlign.center,
            ),
      ),
    );
  }

  _tableHead() {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    List<double> widthList = [54.w, 74.w, 74.w, 92.w, 92.w, 156.w, 84.w];

    return Container(
      decoration: BoxDecoration(color: customTheme.ff1d1d24),
      child: Row(
        children: [
          _tableHeadCell(widthList[0], title: 'S/N'),
          _tableHeadCell(widthList[1], title: 'Full name'),
          _tableHeadCell(widthList[2], title: 'Level'),
          _tableHeadCell(widthList[3], title: 'Invited by'),
          _tableHeadCell(widthList[4], title: "Invitation Code"),
          _tableHeadCell(widthList[5], title: 'Investment Amount (\$)'),
          _tableHeadCell(widthList[6], title: 'Range'),
        ],
      ),
    );
  }

  _tableBody() {
    return List.generate(earningList.length, (index) {
      return _tableRow(index);
    });
  }

  _tableRowCell(
    double width, {
    double? height,
    String? title,
    bool isLast = false,
    bool highlight = false,
    bool isRowLast = false,
    Widget? child,
    VoidCallback? onTap,
  }) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    var textStyle = TextStyle(
      fontSize: 12.sp,
      color: highlight ? customTheme.primaryColor : customTheme.textColor,
    );

    Widget textWidget =
        child ??
        Text(title ?? '', style: textStyle, textAlign: TextAlign.center);

    if (onTap != null) {
      textWidget = GestureDetector(onTap: onTap, child: textWidget);
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: isLast
              ? BorderSide.none
              : BorderSide(color: customTheme.dividerColor, width: 0.5),
          bottom: isRowLast
              ? BorderSide.none
              : BorderSide(color: customTheme.dividerColor, width: 0.5),
        ),
      ),
      constraints: BoxConstraints(minHeight: height ?? 46.5.h),
      padding: EdgeInsets.symmetric(horizontal: child != null ? 0 : 5.w),
      width: width,
      child: Center(child: textWidget),
    );
  }

  _tableRow(int index, {bool isLast = false}) {
    List<double> widthList = [54.w, 74.w, 74.w, 92.w, 92.w, 156.w, 84.w];

    final item = earningList[index];
    double height = 46.5.h;

    return Row(
      children: [
        _tableRowCell(
          widthList[0],
          height: height,
          title: (index + 1).toString(),
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[1],
          height: height,
          title: item.userName,
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[2],
          height: height,
          title: item.nodeName,
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[3],
          height: height,
          title: item.userRefereeName,
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[4],
          height: height,
          title: item.userRefereeInviteNo,
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[5],
          height: height,
          title: item.performance.toString().fmt,
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[6],
          height: height,
          title: '${(item.diffRate * 100).toStringAsFixed(2).removeZero}%',
          isRowLast: isLast,
        ),
      ],
    );
  }
}
