import 'package:json_annotation/json_annotation.dart';

part 'fund_product_total_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class FundProductTotalRes {
  FundProductTotalRes();

  late num cash;
  num? investment;
  num? commission;

  factory FundProductTotalRes.fromJson(Map<String, dynamic> json) =>
      _$FundProductTotalResFromJson(json);
  Map<String, dynamic> toJson() => _$FundProductTotalResToJson(this);
}
