// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WebSocketRes _$WebSocketResFromJson(
  Map<String, dynamic> json,
  dynamic Function(Map<String, dynamic> json) fromJson,
) => WebSocketRes()
  ..ch = json['ch'] as String
  ..status = json['status'] as String
  ..data = _$nullableGenericFromJson(json['data'], fromJson);

Map<String, dynamic> _$WebSocketResToJson(
  WebSocketRes instance,
  Map<String, dynamic> Function(dynamic value) toJson,
) => <String, dynamic>{
  'ch': instance.ch,
  'status': instance.status,
  'data': _$nullableGenericToJson(instance.data, toJson),
};

Object? _$nullableGenericFromJson(
  Map<String, dynamic>? input,
  dynamic Function(Map<String, dynamic> json) fromJson,
) => input == null ? null : fromJson(input);

Map<String, dynamic>? _$nullableGenericToJson(
  dynamic input,
  Map<String, dynamic> Function(dynamic value) toJson,
) => input == null ? null : toJson(input);
