import 'package:json_annotation/json_annotation.dart';

part 'investment_log_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class InvestmentLogRes {
  InvestmentLogRes();

  late num eventDate;
  late String eventType;
  late String eventDescription;
  num? amount;
  String? remark;

  factory InvestmentLogRes.fromJson(Map<String, dynamic> json) =>
      _$InvestmentLogResFromJson(json);
  Map<String, dynamic> toJson() => _$InvestmentLogResToJson(this);
}
