import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/fund_product_res.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class InvestmentPage extends StatefulWidget {
  const InvestmentPage({super.key});

  @override
  _InvestmentPageState createState() => _InvestmentPageState();
}

class _InvestmentPageState extends State<InvestmentPage> {
  FundProductRes? investment;
  String productId = '';
  String productName = '';

  @override
  void initState() {
    super.initState();
    productId = Get.parameters['productId'] ?? '';
    productName = Get.parameters['name'] ?? '';
    getInvestment();
  }

  Future<void> getInvestment() async {
    try {
      ToastUtil.showLoading();
      final res = await InvestmentApi.getFundProduct(productId);
      setState(() {
        investment = res;
      });
    } catch (e) {
      e.printError();
    } finally {
      ToastUtil.dismiss();
    }
  }

  String getCycleText(num interestCycleDays) {
    if (interestCycleDays == 1) {
      return 'daily'.tr;
    } else if (interestCycleDays == 7) {
      return 'weekly'.tr;
    } else if (interestCycleDays == 30) {
      return 'monthly'.tr;
    } else {
      return 'per_xx_days'.trParams({'days': interestCycleDays.toString()});
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    var headStyle = TextStyle(
      fontSize: 20.sp,
      color: customTheme.textColor,
      fontWeight: FontWeight.w600,
    );
    var contentStyle = TextStyle(fontSize: 13.sp, color: customTheme.textColor);
    var titleStyle = TextStyle(
      fontSize: 15.sp,
      color: customTheme.textColor,
      fontWeight: FontWeight.w600,
    );
    var labelStyle = TextStyle(
      fontSize: 13.sp,
      color: customTheme.labelColor,
      fontWeight: FontWeight.w600,
    );
    var valueStyle = TextStyle(
      fontSize: 15.sp,
      color: customTheme.textColor,
      fontWeight: FontWeight.bold,
    );

    final List<String> introduceList = investment?.introduce?.split('\n') ?? [];
    final List<String> structureList = investment?.structure?.split('\n') ?? [];

    return Scaffold(
      appBar: AppBar(title: Text(productName)),
      body: investment == null
          ? SizedBox.shrink()
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(productName, style: headStyle),
                        SizedBox(height: 13.h),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 8.h,
                          children: List.generate(
                            introduceList.length,
                            (index) => Text(
                              introduceList[index],
                              style: contentStyle,
                              textAlign: TextAlign.justify,
                            ),
                          ),
                        ),
                        SizedBox(height: 19.5.h),
                        Divider(height: 0.5),
                        SizedBox(height: 19.5.h),
                        Row(
                          spacing: 6.w,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("projected_returns".tr, style: labelStyle),
                            // 1、1天展示daily；2、7天展示weekly; 3、30天展示monthly；4、其它的就展示 per xx days
                            Text(
                              '~0–${investment!.interestRatePerCycle.toString().removeZero}% ${getCycleText(investment!.interestCycleDays)}*',
                              style: valueStyle,
                            ),
                          ],
                        ),
                        SizedBox(height: 11.h),
                        Row(
                          spacing: 6.w,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("minimum_investment".tr, style: labelStyle),
                            SizedBox(width: 6.w),
                            Text(
                              'USD \$${investment!.minAmount.toString().fmt}',
                              style: valueStyle,
                            ),
                          ],
                        ),
                        SizedBox(height: 11.h),
                        Row(
                          spacing: 6.w,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("lock_in_period".tr, style: labelStyle),
                            // 1天显示1 day，其他显示xx days
                            Text(
                              '${investment!.lockinPeriodDays} ${investment!.lockinPeriodDays > 1 ? 'days'.tr : 'day'.tr}',
                              style: valueStyle,
                            ),
                          ],
                        ),
                        SizedBox(height: 11.h),
                        Row(
                          spacing: 6.w,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("redemption_fee".tr, style: labelStyle),
                            // 1天显示1 day，其他显示xx days
                            Text('10%', style: valueStyle),
                          ],
                        ),
                        SizedBox(height: 19.5.h),
                        Divider(height: 0.5),
                        SizedBox(height: 18.h),
                        Text('structure'.tr, style: titleStyle),
                        SizedBox(height: 10.h),
                        Column(
                          spacing: 8.h,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: List.generate(
                            structureList.length,
                            (index) =>
                                Text(structureList[index], style: contentStyle),
                          ),
                        ),
                        SizedBox(height: 14.5.h),
                        Text(
                          investment!.description!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: customTheme.subTextColor,
                          ),
                        ),
                        SizedBox(height: 22.h),
                      ],
                    ),
                  ),
                ),
                WidgetUtil.getBottomButton(
                  context: context,
                  title: 'Activate Strategy',
                  onSubmit: () {
                    final isKyc = context.read<AuthProvider>().isKyc;
                    if (!isKyc) {
                      Get.toNamed(Routes.kyc);
                      return;
                    }
                    Get.toNamed(
                      Routes.innvestmentAmount,
                      parameters: {
                        'productId': productId,
                        'name': productName,
                        'interestRatePerCycle': investment!.interestRatePerCycle
                            .toString(),
                        'interestCycleDays': investment!.interestCycleDays
                            .toString(),
                        'lockinPeriodDays': investment!.lockinPeriodDays
                            .toString(),
                        'minAmount': investment!.minAmount.toString(),
                      },
                    );
                  },
                ),
              ],
            ),
    );
  }
}
