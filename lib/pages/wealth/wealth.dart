import 'package:aurenixai_app/api/investment_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/fund_product_res.dart';
import 'package:aurenixai_app/models/fund_product_total_res.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/pie_card.dart';

class WealthPage extends StatefulWidget {
  const WealthPage({super.key});

  @override
  _WealthPageState createState() => _WealthPageState();
}

class _WealthPageState extends State<WealthPage> {
  late EasyRefreshController _controller;
  List<FundProductRes> investmentList = [];
  bool isEmpty = false;
  FundProductTotalRes? totalRes;

  @override
  void initState() {
    super.initState();
    // 直接加载列表数据就可以，因为投资记录是按照标的来分的，所以不需要分页
    _controller = EasyRefreshController(controlFinishRefresh: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    try {
      final res = await InvestmentApi.getFundProductList();
      await getFundProductTotal();
      investmentList = res;
      isEmpty = investmentList.isEmpty;
    } catch (e) {
      e.printError();
    }
    if (!mounted) {
      return;
    }
    setState(() {});
    _controller.finishRefresh();
  }

  Future<void> getFundProductTotal() async {
    try {
      final res = await InvestmentApi.getFundProductTotal();
      totalRes = res;
    } catch (e) {
      e.printError();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: Text('wealth'.tr)),
      body: EasyRefresh(
        controller: _controller,
        onRefresh: _onRefresh,
        refreshOnStart: true,
        header: WidgetUtil.getRefreshOnStartHeader(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PieCard(totalRes: totalRes),
              SizedBox(height: 20.h),
              Text(
                'investment'.tr,
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.w600,
                  color: customTheme.textColor,
                ),
              ),
              SizedBox(height: 15.h),
              ...List.generate(
                investmentList.length,
                (index) => _investmentItem(index),
              ),
              if (isEmpty) _empty(),
            ],
          ),
        ),
      ),
    );
  }

  String getCycleText(num interestCycleDays) {
    if (interestCycleDays == 1) {
      return 'daily'.tr;
    } else if (interestCycleDays == 7) {
      return 'weekly'.tr;
    } else if (interestCycleDays == 30) {
      return 'monthly'.tr;
    } else {
      return 'per_xx_days'.trParams({'days': interestCycleDays.toString()});
    }
  }

  Widget _investmentItem(int index) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    final investment = investmentList[index];

    return Container(
      margin: EdgeInsets.only(bottom: 12.w),
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.5.w, vertical: 14.5.h),
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(3.w),
        gradient: LinearGradient(
          colors: [customTheme.ff363C64, customTheme.inputBgColor],
          stops: [0.0, 0.23],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        image: DecorationImage(
          image: AssetImage(customThemeImage.portfolioCardBg),
          alignment: Alignment.topCenter,
          fit: BoxFit.fitWidth,
        ),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          Get.toNamed(
            Routes.investment,
            parameters: {'name': investment.name, 'productId': investment.id},
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 3.5.h),
            Text(
              investment.name,
              style: TextStyle(
                fontSize: 15.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (investment.text != null)
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 10.5.h),
                decoration: BoxDecoration(
                  color: customTheme.inputBgColor,
                  borderRadius: BorderRadius.circular(9.5.r),
                ),
                padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 4.h),
                child: Text(
                  investment.text!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: customTheme.subTextColor,
                  ),
                ),
              ),
            SizedBox(height: 14.h),
            // 1、1天展示daily；2、7天展示weekly; 3、30天展示monthly；4、其它的就展示 per xx days
            _line(
              'projected_returns'.tr,
              '~0–${investment.interestRatePerCycle.toString().removeZero}% ${getCycleText(investment.interestCycleDays)}*',
            ),
            SizedBox(height: 11.h),
            _line(
              'minimum_investment'.tr,
              'USD \$${investment.minAmount.toString().fmt}',
            ),
            SizedBox(height: 11.h),
            _line(
              'lock_in_period'.tr,
              ' ${investment.lockinPeriodDays} ${investment.lockinPeriodDays > 1 ? 'days'.tr : 'day'.tr}',
            ),
            SizedBox(height: 17.5.h),
            Divider(color: customTheme.dividerColor, height: 0.5),
            SizedBox(height: 8.h),
            Text(
              'return_description'.tr,
              style: TextStyle(fontSize: 12.sp, color: customTheme.ffd8f2c0),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _line(String title, String value) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: 6.w,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 13.sp,
            color: customTheme.labelColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(width: 10.w),
        Text(
          value,
          style: TextStyle(
            fontSize: 15.sp,
            color: customTheme.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _empty() {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Column(
      children: [
        SizedBox(height: 60.w),
        Image.asset(customThemeImage.commonEmpty1, width: 330.w),
        SizedBox(height: 12.w),
        Text(
          'no_record_found'.tr,
          style: TextStyle(
            fontSize: 13.sp,
            color: customTheme.subTextColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 60.w),
      ],
    );
  }
}
