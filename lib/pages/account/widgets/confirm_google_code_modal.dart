import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class ConfirmGoogleCodeModal extends StatefulWidget {
  final String title;
  final String tips;

  /// 确认回调
  final void Function({required String emailCode, required String googleCode})
  onConfirm;

  const ConfirmGoogleCodeModal({
    super.key,
    required this.title,
    required this.tips,
    required this.onConfirm,
  });

  static Future<void> showModal(
    BuildContext context, {
    required String title,
    required void Function({
      required String emailCode,
      required String googleCode,
    })
    onConfirm,
    required String tips,
  }) async {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return ConfirmGoogleCodeModal(
          title: title,
          tips: tips,
          onConfirm: onConfirm,
        );
      },
    );
  }

  @override
  State<ConfirmGoogleCodeModal> createState() => _ConfirmGoogleCodeModalState();
}

class _ConfirmGoogleCodeModalState extends State<ConfirmGoogleCodeModal> {
  final TextEditingController _emailCodeController = TextEditingController();
  final TextEditingController _googleCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _emailCodeController.dispose();
    _googleCodeController.dispose();
    super.dispose();
  }

  void onSubmit() async {
    if (_emailCodeController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_nickname'.tr);
      return;
    }

    if (_googleCodeController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_nickname'.tr);
      return;
    }

    widget.onConfirm(
      emailCode: _emailCodeController.text,
      googleCode: _googleCodeController.text,
    );
    Navigator.of(context).pop();
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextButton(
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Icon(
                        Icons.close_rounded,
                        size: 26.w,
                        color: customTheme.textColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 18.h),
                Text(
                  widget.tips,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return SafeArea(
      top: false,
      bottom: false,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTopBar(context),
            SizedBox(height: 22.5.h),
            Padding(
              padding: EdgeInsetsGeometry.symmetric(horizontal: 15.w),
              child: Stack(
                children: [
                  NormalInput(
                    label: "email_verification_code".tr,
                    controller: _emailCodeController,
                    padding: EdgeInsets.only(left: 15.w, right: 80.w),
                  ),
                  Positioned(
                    bottom: 15.w,
                    right: 15.w,
                    child: PrimaryButton(
                      title: 'send_code'.tr,
                      height: 28.w,
                      isInline: true,
                      padding: EdgeInsets.symmetric(horizontal: 9.w),
                      onPress: () {},
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 18.h),
            Padding(
              padding: EdgeInsetsGeometry.symmetric(horizontal: 15.w),
              child: Stack(
                children: [
                  NormalInput(
                    label: "google_verification_code".tr,
                    controller: _googleCodeController,
                    padding: EdgeInsets.only(left: 15.w, right: 80.w),
                  ),
                  Positioned(
                    bottom: 15.w,
                    right: 15.w,
                    child: PrimaryButton(
                      title: 'paste'.tr,
                      height: 28.w,
                      isInline: true,
                      padding: EdgeInsets.symmetric(horizontal: 9.w),
                      onPress: () {
                        Clipboard.getData(Clipboard.kTextPlain).then((value) {
                          if (value != null) {
                            setState(() {
                              _googleCodeController.text = value.text ?? '';
                            });
                          }
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),
            Padding(
              padding: EdgeInsetsGeometry.symmetric(horizontal: 15.w),
              child: Text(
                'google_verification_code_tips'.tr,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  color: customTheme.hintTextColor,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            SizedBox(height: 40.h),
            WidgetUtil.getBottomWrapper(
              context: context,
              child: Row(
                spacing: 9.w,
                children: [
                  Expanded(
                    child: OutlineButton(
                      title: 'cancel'.tr,
                      fontSize: 13.sp,
                      onPress: () {
                        Navigator.pop(context);
                      },
                    ),
                  ),
                  Expanded(
                    child: PrimaryButton(
                      title: 'confirm'.tr,
                      fontSize: 13.sp,
                      onPress: onSubmit,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
