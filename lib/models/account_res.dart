import 'package:json_annotation/json_annotation.dart';

part 'account_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class AccountRes {
  AccountRes();

  late String amount;
  late String availableAmount;
  late String rewardAmount;
  late String rewardAvailableAmount;
  late String rewardFlag;

  factory AccountRes.fromJson(Map<String, dynamic> json) =>
      _$AccountResFromJson(json);
  Map<String, dynamic> toJson() => _$AccountResToJson(this);
}
