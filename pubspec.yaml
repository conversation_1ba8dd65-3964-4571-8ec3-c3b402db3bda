name: aurenixai_app
description: "Aurenix AI App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1
  

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: 1.0.8
  lazy_load_indexed_stack: 1.2.1
  dio: 5.8.0+1
  flutter_screenutil: 5.9.3
  provider: 6.1.5
  flutter_easyloading: 3.0.5
  flutter_svg: 2.2.0
  get: 4.7.2
  flutter_native_splash: 2.4.6
  shared_preferences: 2.5.3
  dart_date: 1.5.3
  json_annotation: 4.9.0
  event_bus_plus: 0.7.0
  package_info_plus: 8.3.0
  recaptcha_enterprise_flutter: 18.7.1
  carousel_slider: 5.1.1
  dotted_border: 3.1.0
  qr_flutter: 4.1.0
  intl: 0.20.2
  easy_refresh: 3.4.0
  loading_animation_widget: 1.3.0
  graphic: 2.6.0
  image_picker: 1.1.2
  cached_network_image: 3.4.1
  chart_sparkline: 1.1.1
  fl_chart: 1.0.0
  web_socket_channel: 3.0.3
  signature: 6.3.0
  webview_flutter: 4.13.0
  flutter_widget_from_html: 0.16.0
  crisp_chat: 2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/css/
    - assets/images/common/dark/
    - assets/images/common/light/
    - assets/images/auth/dark/
    - assets/images/auth/light/
    - assets/images/kyc/dark/
    - assets/images/kyc/light/
    - assets/images/tabs/dark/
    - assets/images/tabs/light/
    - assets/images/home/<USER>/
    - assets/images/home/<USER>/
    - assets/images/portfolio/dark/
    - assets/images/portfolio/light/
    - assets/images/services/dark/
    - assets/images/services/light/
    - assets/images/google/dark/
    - assets/images/google/light/
    - assets/images/account/dark/
    - assets/images/account/light/
    - assets/images/wealth/dark/
    - assets/images/wealth/light/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
