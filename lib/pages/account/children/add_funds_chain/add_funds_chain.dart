import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/widgets/common_picker.dart';
import 'package:aurenixai_app/widgets/normal_select.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddFundsChainPage extends StatefulWidget {
  const AddFundsChainPage({super.key});

  @override
  _AddFundsChainPageState createState() => _AddFundsChainPageState();
}

class _AddFundsChainPageState extends State<AddFundsChainPage>
    with SingleTickerProviderStateMixin {
  List<String> currencyList = ['USDT', 'USDC'];
  int currencyIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    List<String> currencyImage = [
      customThemeImage.accountCurrencyUsdt,
      customThemeImage.accountCurrencyUsdc,
    ];

    return Scaffold(
      appBar: AppBar(title: Text('Add_Funds'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 6.h),
                  NormalSelect(
                    label: 'Currency'.tr,
                    hintText: 'select_currency'.tr,
                    content: currencyList[currencyIndex],
                    prefix: Image.asset(
                      currencyImage[currencyIndex],
                      width: 22.w,
                      height: 22.w,
                      fit: BoxFit.fill,
                    ),
                    onTap: () {
                      CommonPicker.showModal(
                            context,
                            title: 'select_currency'.tr,
                            list: currencyList,
                            contentBuilder: (context, index) => Row(
                              children: [
                                Image.asset(
                                  currencyImage[index],
                                  width: 22.w,
                                  height: 22.w,
                                  fit: BoxFit.fill,
                                ),
                                SizedBox(width: 7.w),
                                Text(
                                  currencyList[index],
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    color: customTheme.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          )
                          .then((index) {
                            if (index != null) {
                              setState(() {
                                currencyIndex = index;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                  ),
                  SizedBox(height: 36.h),
                  Text(
                    'select_network'.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 19.h),
                  Divider(color: customTheme.dividerColor, height: 0.5),
                  SizedBox(height: 23.h),
                  _buildItem(
                    customTheme: customTheme,
                    name: 'Tron (TRC20)',
                    description:
                        'Expected arrival: ~1 min | Min. deposit: 0.01 USDT',
                    onTap: () {
                      Get.toNamed(Routes.addFundsChainRecharge);
                    },
                  ),
                  SizedBox(height: 12.h),
                  _buildItem(
                    customTheme: customTheme,
                    name: 'Ethereum (ERC20)',
                    description:
                        'Expected arrival: ~1 min | Min. deposit: 0.01 USDT',
                    onTap: () {
                      Get.toNamed(Routes.addFundsChainRecharge);
                    },
                  ),
                  SizedBox(height: 12.h),
                  _buildItem(
                    customTheme: customTheme,
                    name: 'BNB Smart Chain (BEP20)',
                    description:
                        'Expected arrival: ~1 min | Min. deposit: 0.01 USDT',
                    onTap: () {
                      Get.toNamed(Routes.addFundsChainRecharge);
                    },
                  ),
                  SizedBox(height: 12.h),
                  _buildItem(
                    customTheme: customTheme,
                    name: 'Arbitrum (Layer 2 Ethereum)',
                    description:
                        'Expected arrival: ~1 min | Min. deposit: 0.01 USDT',
                    onTap: () {
                      Get.toNamed(Routes.addFundsChainRecharge);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItem({
    required CustomThemeSwatch customTheme,
    required String name,
    required String description,
    required void Function() onTap,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: customTheme.inputBgColor,
          borderRadius: BorderRadius.circular(3.w),
        ),
        child: Row(
          children: [
            Container(
              width: 2.w,
              height: 27.h,
              decoration: BoxDecoration(
                color: customTheme.primaryColor,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(1.w),
                  bottomRight: Radius.circular(1.w),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        color: customTheme.textColor,
                        fontSize: 13.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 3.h),
                    Text(
                      description,
                      style: TextStyle(
                        color: customTheme.subTextColor,
                        fontSize: 11.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
