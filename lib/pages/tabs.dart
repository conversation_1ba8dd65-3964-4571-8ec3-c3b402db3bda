import 'dart:async';

import 'package:aurenixai_app/pages/account/account.dart';
import 'package:aurenixai_app/pages/home/<USER>';
import 'package:aurenixai_app/pages/portfolio/portfolio.dart';
import 'package:aurenixai_app/pages/services/services.dart';
import 'package:aurenixai_app/pages/wealth/wealth.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/local_util.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';
import 'package:provider/provider.dart';

class TabItem {
  TabItem({required this.label, required this.icon, required this.activeIcon});

  final String label;
  final String icon;
  final String activeIcon;
}

class TabsPage extends StatefulWidget {
  const TabsPage({super.key});

  @override
  _TabsState createState() => _TabsState();
}

class _TabsState extends State<TabsPage> {
  int selectedIndex = 0;
  ThemeType? themeType;
  late StreamSubscription<SwitchTabEvent> switchTabSubscription;

  @override
  void initState() {
    super.initState();
    getTheme();
    // 解决在initState()初始化方法时使用包含context的Widget导致报错问题
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      var tabIndex = int.tryParse(Get.parameters["tabIndex"] ?? "0");
      _onItemTapped(tabIndex ?? 0);
    });
    switchTabSubscription = EventBusUtil.listenSwitchTab((event) {
      _onItemTapped(event.tabIndex);
    });
  }

  @override
  void dispose() {
    switchTabSubscription.cancel();
    super.dispose();
  }

  getTheme() async {
    try {
      final typeStr = await LocalUtil.getThemeType();
      setState(() {
        themeType = typeStr;
      });
    } catch (e) {}
  }

  void _onItemTapped(int index) async {
    setState(() {
      selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;

    List<TabItem> tabs = [
      TabItem(
        label: 'home'.tr,
        icon: customThemeImage.tabsHome,
        activeIcon: customThemeImage.tabsHomeIn,
      ),
      TabItem(
        label: 'portfolio'.tr,
        icon: customThemeImage.tabsPortfolio,
        activeIcon: customThemeImage.tabsPortfolioIn,
      ),
      TabItem(
        label: 'wealth'.tr,
        icon: customThemeImage.tabsWealth,
        activeIcon: customThemeImage.tabsWealthIn,
      ),
      TabItem(
        label: 'account'.tr,
        icon: customThemeImage.tabsAccount,
        activeIcon: customThemeImage.tabsAccountIn,
      ),
      TabItem(
        label: 'services'.tr,
        icon: customThemeImage.tabsServices,
        activeIcon: customThemeImage.tabsServicesIn,
      ),
    ];

    List<BottomNavigationBarItem> btmNavs = List.generate(
      tabs.length,
      (index) => BottomNavigationBarItem(
        icon: Padding(
          padding: EdgeInsets.only(bottom: 3.5.w, top: 5.w),
          child: Image.asset(
            tabs[index].icon,
            width: 25.w,
            height: 25.w,
            fit: BoxFit.fill,
          ),
        ),
        activeIcon: Padding(
          padding: EdgeInsets.only(bottom: 3.5.w, top: 5.w),
          child: Image.asset(
            tabs[index].activeIcon,
            width: 25.w,
            height: 25.w,
            fit: BoxFit.fill,
          ),
        ),
        label: tabs[index].label,
      ),
    );

    return Scaffold(
      body: LazyLoadIndexedStack(
        index: selectedIndex,
        children: const [
          HomePage(),
          PortfolioPage(),
          WealthPage(),
          AccountPage(),
          ServicesPage(),
        ],
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(height: 1.w, color: Colors.white.withValues(alpha: 0.2)),
          BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            backgroundColor: customTheme.inputBgColor,
            selectedItemColor: customTheme.textColor,
            selectedLabelStyle: TextStyle(
              fontSize: 12.sp,
              color: customTheme.textColor,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 12.sp,
              color: Theme.of(context).unselectedWidgetColor,
            ),
            items: btmNavs,
            currentIndex: selectedIndex,
            onTap: (index) {
              _onItemTapped(index);
            },
          ),
        ],
      ),
    );
  }
}
