// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionRes _$QuestionResFromJson(Map<String, dynamic> json) => QuestionRes()
  ..id = json['id'] as String
  ..title = json['title'] as String
  ..type = json['type'] as String
  ..content = json['content'] as String?
  ..questions = (jsonDecode(json['questions']) as List<dynamic>)
      .map((e) => QuestionOptionRes.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$QuestionResToJson(QuestionRes instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'type': instance.type,
      'content': instance.content,
      'questions': instance.questions.map((e) => e.toJson()).toList(),
    };
