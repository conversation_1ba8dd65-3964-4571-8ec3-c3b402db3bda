import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

const primaryColor = Color(0xFF5F7EFC);
final primarySwatch = CommonUtils.createMaterialColor(primaryColor);

CustomThemeSwatch getCustomThemeSwatch() {
  return CustomThemeSwatch(
    pageBgColor: const Color(0xFFFFFFFF),
    primaryColor: primaryColor,
    buttonBgColor: const Color(0xFF5F7EFC),
    buttonTextColor: const Color(0xFFFFFFFF),
    textColor: const Color(0xFFFFFFFF),
    labelColor: const Color(0xFFFFFFFF).withValues(alpha: 0.8),
    subTextColor: const Color(0xFFFFFFFF).withValues(alpha: 0.7),
    lightTextColor: const Color(0xFFFFFFFF).withValues(alpha: 0.6),
    hintTextColor: const Color(0xFFFFFFFF).withValues(alpha: 0.5),
    inputBgColor: const Color(0xFF282835),
    inputHintColor: const Color(0xFFFFFFFF).withValues(alpha: 0.5),
    dividerColor: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
    modalBarColor: const Color(0xFFFFFFFF).withValues(alpha: 0.23),
    notificationBgColor: const Color(0xFF282835).withValues(alpha: 0.6),
    upColor: const Color(0xFF36E399),
    downColor: const Color(0xFFD74545),
    portfolioRedeemedBgColor: Colors.black.withValues(alpha: 0.4),
    proStartColor: const Color(0xFFCDC8EB),
    proEndColor: const Color(0xFFEBEDFF),
    proColor: const Color(0xFF7876E9),
    preferredStartColor: const Color(0xFF94E3B6),
    preferredEndColor: const Color(0xFFD6F5E1),
    preferredColor: const Color(0xFF009765),
    eliteStartColor: const Color(0xFF9FD4FD),
    eliteEndColor: const Color(0xFFD2EFFE),
    eliteColor: const Color(0xFF3B71D2),
    premierStartColor: const Color(0xFFF5D8AB),
    premierEndColor: const Color(0xFFFFF5CE),
    premierColor: const Color(0xFFB76719),
    signatureStartColor: const Color(0xFFC8D6EB),
    signatureEndColor: const Color(0xFFF6FCFF),
    signatureColor: const Color(0xFF646D95),
    platinumStartColor: const Color(0xFFFBEF7C),
    platinumMidColor: const Color(0xFFFFF7A0),
    platinumEndColor: const Color(0xFFFFAB28),
    platinumColor: const Color(0xFFB5750B),
    ff7ed032: const Color(0xFF7ED032),
    ff40e2fe: const Color(0xFF40E2FE),
    ff363C64: const Color(0xFF363C64),
    ff515165: const Color(0xFF515165),
    ff696978: const Color(0xFF696978),
    ff878C9f: const Color(0xFF878C9F),
    ff99adff: const Color(0xFF99ADFF),
    ffd8f2c0: const Color(0xFFD8F2C0),
    fffce412: const Color(0xFFFCE412),
    ff1f1f2a: const Color(0xFF1F1F2A),
    ff1d1d24: const Color(0xFF1D1D24),
    addFundsRechargeTipsBg: Colors.black,
    addFundsRechargeAddressBg1: const Color(0x002B2B38),
    addFundsRechargeAddressBg2: const Color(0xFF282835),
  );
}

CustomThemeImage getCustomThemeImage() {
  return CustomThemeImage(
    defaultSquareImg: 'assets/images/common/light/s_defaultImg.png',
    defaultHRectangleImg: 'assets/images/common/light/h_defaultImg.png',
    defaultVRectangleImg: 'assets/images/common/light/v_defaultImg.png',
    tabsHome: 'assets/images/tabs/light/home.png',
    tabsHomeIn: 'assets/images/tabs/light/home_in.png',
    tabsPortfolio: 'assets/images/tabs/light/portfolio.png',
    tabsPortfolioIn: 'assets/images/tabs/light/portfolio_in.png',
    tabsWealth: 'assets/images/tabs/light/wealth.png',
    tabsWealthIn: 'assets/images/tabs/light/wealth_in.png',
    tabsAccount: 'assets/images/tabs/light/account.png',
    tabsAccountIn: 'assets/images/tabs/light/account_in.png',
    tabsServices: 'assets/images/tabs/light/services.png',
    tabsServicesIn: 'assets/images/tabs/light/services_in.png',
    commonEmpty: 'assets/images/common/light/empty.png',
    commonEmpty1: 'assets/images/common/light/empty1.png',
    commonEmptyCoupon: 'assets/images/common/light/empty_coupon.png',
    commonDown: 'assets/images/common/light/down.png',
    commonIconRight: 'assets/images/common/light/icon_right.png',
    commonSuccessTip: 'assets/images/common/light/success_tip.png',
    commonErrorTip: 'assets/images/common/light/error_tip.png',
    commonWarnTip: 'assets/images/common/light/warn_tip.png',
    commonIconScreen: 'assets/images/common/light/icon_screen.png',
    authLogo: 'assets/images/auth/light/logo.png',
    authBgTop: 'assets/images/auth/light/bg_top.png',
    authBgBottom: 'assets/images/auth/light/bg_bottom.png',
    authSignInLogo: 'assets/images/auth/light/sign_in_logo.png',
    authUnchoose: 'assets/images/auth/light/unchoose.png',
    authChoose: 'assets/images/auth/light/choose.png',
    authSignUpLogo: 'assets/images/auth/light/sign_up_logo.png',
    authVerified: 'assets/images/auth/light/verified.png',
    authSucTip: 'assets/images/auth/light/suc_tip.png',
    authLoading: 'assets/images/auth/light/loading.png',
    authVerification: 'assets/images/auth/light/verification.png',
    authSelect: 'assets/images/auth/light/select.png',
    authNight: 'assets/images/auth/light/night.png',
    authSunny: 'assets/images/auth/light/sunny.png',
    kycIcon: 'assets/images/kyc/light/icon.png',
    kycClose: 'assets/images/kyc/light/close.png',
    kycPlaceholder: 'assets/images/kyc/light/placeholder.png',
    kycVerification: 'assets/images/kyc/light/verification.png',
    kycWarn: 'assets/images/kyc/light/warn.png',
    kycSelect: 'assets/images/kyc/light/select.png',
    kycUnselect: 'assets/images/kyc/light/unselect.png',
    homeMessage: 'assets/images/home/<USER>/message.png',
    homeLogo: 'assets/images/home/<USER>/logo.png',
    homeClose: 'assets/images/home/<USER>/close.png',
    homeShare: 'assets/images/home/<USER>/share.png',
    homeAdd: 'assets/images/home/<USER>/add.png',
    homeReduce: 'assets/images/home/<USER>/reduce.png',
    homeChat: 'assets/images/home/<USER>/chat.png',
    homeBalanceBg: 'assets/images/home/<USER>/balance_bg.png',
    homeUp: 'assets/images/home/<USER>/up.png',
    homeDown: 'assets/images/home/<USER>/down.png',
    homeFail: 'assets/images/home/<USER>/fail.png',
    homeWarn: 'assets/images/home/<USER>/warn.png',
    homeCopy: 'assets/images/home/<USER>/copy.png',
    homeLink: 'assets/images/home/<USER>/link.png',
    homeCalendar: 'assets/images/home/<USER>/calendar.png',
    homeEnvelope: 'assets/images/home/<USER>/envelope.png',
    portfolioCardBg: 'assets/images/portfolio/light/card_bg.png',
    portfolioArrowRight: 'assets/images/portfolio/light/arrow_right.png',
    portfolioTip: 'assets/images/portfolio/light/tip.png',
    servicesEdit: 'assets/images/services/light/edit.png',
    servicesScan: 'assets/images/services/light/scan.png',
    servicesCoupon: 'assets/images/services/light/coupon.png',
    servicesPersonal: 'assets/images/services/light/personal.png',
    servicesAuthenticator: 'assets/images/services/light/authenticator.png',
    servicesService: 'assets/images/services/light/service.png',
    servicesAP: 'assets/images/services/light/ap.png',
    servicesCP: 'assets/images/services/light/cp.png',
    servicesSL: 'assets/images/services/light/sl.png',
    servicesRD: 'assets/images/services/light/rd.png',
    servicesED: 'assets/images/services/light/ed.png',
    servicesGD: 'assets/images/services/light/gd.png',
    servicesPro: 'assets/images/services/light/pro.png',
    servicesPreferred: 'assets/images/services/light/preferred.png',
    servicesElite: 'assets/images/services/light/elite.png',
    servicesPremier: 'assets/images/services/light/premier.png',
    servicesSignature: 'assets/images/services/light/signature.png',
    servicesPlatinum: 'assets/images/services/light/platinum.png',
    servicesCouponBg: 'assets/images/services/light/coupon_bg.png',
    servicesPhone: 'assets/images/services/light/phone.png',
    servicesLock: 'assets/images/services/light/lock.png',
    wealthMoney: 'assets/images/wealth/light/money.png',
    googleSecure: 'assets/images/google/light/secure.png',
    googleSecurity: 'assets/images/google/light/security.png',
    googleSuccess: 'assets/images/google/light/success.png',
    googleSuccess1: 'assets/images/google/light/success1.png',
    googleUnbind: 'assets/images/google/light/unbind.png',
    accountListBg1: 'assets/images/account/light/funding_bg.png',
    accountListBg2: 'assets/images/account/light/commission_bg.png',
    accountDetailTips: 'assets/images/account/light/icon_tips.png',

    accountRedeemSb: 'assets/images/account/light/redeem_sb.png',
    accountRedeemWc: 'assets/images/account/light/redeem_wc.png',
    accountRedeemDd: 'assets/images/account/light/redeem_dd.png',
    accountTransferUsdtcg: 'assets/images/account/light/transfer_usdtcg.png',
    accountTransferUsdtddz: 'assets/images/account/light/transfer_usdtddz.png',
    accountTransferUsdtsb: 'assets/images/account/light/transfer_usdtsb.png',
    accountTransferEmail: 'assets/images/account/light/transfer_email.png',
    accountRechargeUid: 'assets/images/account/light/recharge_uid.png',
    accountRechargeEmail: 'assets/images/account/light/recharge_email.png',
    accountRechargeUsdt: 'assets/images/account/light/recharge_usdt.png',
    accountRechargeYhk: 'assets/images/account/light/recharge_yhk.png',
    accountCommissionZq: 'assets/images/account/light/commission_zq.png',
    accountCommissionYqjlsb:
        'assets/images/account/light/commission_yqjlsb.png',
    accountCommissionYqjl: 'assets/images/account/light/commission_yqjl.png',
    accountCommissionZz: 'assets/images/account/light/commission_zz.png',
    accountCurrencyUsdt: 'assets/images/account/light/usdt.png',
    accountCurrencyUsdc: 'assets/images/account/light/usdc.png',
    addFundsChainRechargeTips: 'assets/images/account/light/icon_tips_blue.png',
    addFundsChainRechargeCopy: 'assets/images/account/light/icon_copy.png',

    sendChainIconCopy: 'assets/images/account/light/icon_copy_white.png',
    sendChainIconScan: 'assets/images/account/light/icon_scan.png',
    sendChainIconAddress: 'assets/images/account/light/icon_address.png',
    sendChainIconEdit: 'assets/images/account/light/icon_edit.png',
    sendChainIconAddressNoRecord:
        'assets/images/account/light/icon_address_no_record.png',
    sendChainIconAddressCheck:
        'assets/images/account/light/icon_address_check.png',
  );
}

ThemeData getThemeData() {
  return ThemeData(
    primaryColor: primaryColor,
    primarySwatch: primarySwatch,
    platform: TargetPlatform.iOS,
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
      },
    ),
    tabBarTheme: TabBarThemeData(
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white.withValues(alpha: 0.6),
      indicatorColor: primaryColor,
    ),
    unselectedWidgetColor: Colors.white.withValues(alpha: 0.6),
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: Color(0xFF333333),
      elevation: 0,
      titleTextStyle: TextStyle(
        fontSize: kIsWeb ? 18 : 18.sp,
        color: Colors.white,
        fontWeight: FontWeight.w500,
      ),
    ),
    textTheme: const TextTheme(
      titleMedium: TextStyle(color: Color(0xFF323233)),
    ),
    scaffoldBackgroundColor: const Color(0xFFF6F6F8),
    hintColor: const Color(0xFF999999),
    dividerTheme: DividerThemeData(
      color: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
    ),
    dividerColor: const Color(0xFFE3E3E3),
  );
}

class ThemeLight {
  static final Map<String, dynamic> theme = {
    "app": getThemeData(),
    "custom": getCustomThemeSwatch(),
    'image': getCustomThemeImage(),
  };
}
