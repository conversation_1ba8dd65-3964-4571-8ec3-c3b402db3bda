// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fund_investment_record_res.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FundInvestmentRecordRes _$FundInvestmentRecordResFromJson(
  Map<String, dynamic> json,
) => FundInvestmentRecordRes()
  ..id = json['id'] as String
  ..productId = json['productId'] as String
  ..initialAmount = json['initialAmount'] as num
  ..totalInvested = json['totalInvested'] as num
  ..totalRedeemed = json['totalRedeemed'] as num
  ..returnsToWallet = json['returnsToWallet'] as num
  ..returnsReinvested = json['returnsReinvested'] as num
  ..totalReturns = json['totalReturns'] as num
  ..currentBalance = json['currentBalance'] as num
  ..status = json['status'] as String
  ..fundingDate = json['fundingDate'] as num
  ..interestStartDate = json['interestStartDate'] as num
  ..firstInterestPaidDate = json['firstInterestPaidDate'] as num
  ..nextInterestDate = json['nextInterestDate'] as num
  ..isReinvestEnabled = json['isReinvestEnabled'] as String?
  ..reinvestEnabledDate = json['reinvestEnabledDate'] as num?
  ..pendingRedeemAmount = json['pendingRedeemAmount'] as num
  ..statusName = json['statusName'] as String
  ..canRedeem = json['canRedeem'] as String
  ..canReinvest = json['canReinvest'] as String
  ..redemptionPeriodDays = json['redemptionPeriodDays'] as num
  ..lockinPeriodDays = json['lockinPeriodDays'] as num
  ..fee = json['fee'] as num
  ..stopLossEvent = json['stopLossEvent'] as num;

Map<String, dynamic> _$FundInvestmentRecordResToJson(
  FundInvestmentRecordRes instance,
) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'initialAmount': instance.initialAmount,
  'totalInvested': instance.totalInvested,
  'totalRedeemed': instance.totalRedeemed,
  'returnsToWallet': instance.returnsToWallet,
  'returnsReinvested': instance.returnsReinvested,
  'totalReturns': instance.totalReturns,
  'currentBalance': instance.currentBalance,
  'status': instance.status,
  'fundingDate': instance.fundingDate,
  'interestStartDate': instance.interestStartDate,
  'firstInterestPaidDate': instance.firstInterestPaidDate,
  'nextInterestDate': instance.nextInterestDate,
  'isReinvestEnabled': instance.isReinvestEnabled,
  'reinvestEnabledDate': instance.reinvestEnabledDate,
  'pendingRedeemAmount': instance.pendingRedeemAmount,
  'statusName': instance.statusName,
  'canRedeem': instance.canRedeem,
  'canReinvest': instance.canReinvest,
  'redemptionPeriodDays': instance.redemptionPeriodDays,
  'lockinPeriodDays': instance.lockinPeriodDays,
  'fee': instance.fee,
  'stopLossEvent': instance.stopLossEvent,
};
