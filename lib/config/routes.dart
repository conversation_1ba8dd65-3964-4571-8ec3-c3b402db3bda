import 'package:aurenixai_app/pages/account/children/add_funds_chain/add_funds_chain_recharge.dart';
import 'package:aurenixai_app/pages/account/children/add_funds_crypto/add_funds_crypto_confirm.dart';
import 'package:aurenixai_app/pages/account/children/add_funds_uid/add_funds_uid.dart';
import 'package:aurenixai_app/pages/account/children/add_funds_chain/add_funds_chain.dart';
import 'package:aurenixai_app/pages/account/children/add_funds_crypto/add_funds_crypto.dart';
import 'package:aurenixai_app/pages/account/children/address_book/address_book.dart';
import 'package:aurenixai_app/pages/account/children/address_book/children/address_book_add.dart';
import 'package:aurenixai_app/pages/account/children/address_book/children/address_book_add_uid_email.dart';
import 'package:aurenixai_app/pages/account/children/address_book/children/address_set_trusted_payee.dart';
import 'package:aurenixai_app/pages/account/children/commission/commission_Transfer.dart';
import 'package:aurenixai_app/pages/account/children/commission/commission.dart';
import 'package:aurenixai_app/pages/account/children/commission/transaction_history.dart';
import 'package:aurenixai_app/pages/account/children/funding/funding.dart';
import 'package:aurenixai_app/pages/account/children/funding/transaction_history.dart';
import 'package:aurenixai_app/pages/account/children/send_chain/send_chain.dart';
import 'package:aurenixai_app/pages/account/children/send_chain/send_chain_confirm.dart';
import 'package:aurenixai_app/pages/account/children/send_uid_email/send_uid_email.dart';
import 'package:aurenixai_app/pages/account/children/send_uid_email/send_uid_email_confirm.dart';
import 'package:aurenixai_app/pages/account/children/transaction_details.dart';
import 'package:aurenixai_app/pages/home/<USER>/notification.dart';
import 'package:aurenixai_app/pages/home/<USER>/notification_detail.dart';
import 'package:aurenixai_app/pages/portfolio/children/earnings_tracker.dart';
import 'package:aurenixai_app/pages/portfolio/children/my_investment.dart';
import 'package:aurenixai_app/pages/portfolio/children/redeem_successfully.dart';
import 'package:aurenixai_app/pages/portfolio/children/team_action_successfully.dart';
import 'package:aurenixai_app/pages/public/auth.dart';
import 'package:aurenixai_app/pages/public/empty.dart';
import 'package:aurenixai_app/pages/public/sign_in.dart';
import 'package:aurenixai_app/pages/public/sign_up/sign_up.dart';
import 'package:aurenixai_app/pages/public/reset_password.dart';
import 'package:aurenixai_app/pages/public/sign_up/sign_up_successfully.dart';
import 'package:aurenixai_app/pages/public/sign_up/vierfy_email.dart';
import 'package:aurenixai_app/pages/services/children/coupon/coupon.dart';
import 'package:aurenixai_app/pages/services/children/google/bind_google.dart';
import 'package:aurenixai_app/pages/services/children/google/google.dart';
import 'package:aurenixai_app/pages/services/children/google/unbind_google_successfully.dart';
import 'package:aurenixai_app/pages/services/children/kyc/kyc.dart';
import 'package:aurenixai_app/pages/services/children/kyc/kyc_question.dart';
import 'package:aurenixai_app/pages/services/children/kyc/kyc_verify.dart';
import 'package:aurenixai_app/pages/services/children/personal_info/personal_info.dart';
import 'package:aurenixai_app/pages/tabs.dart';
import 'package:aurenixai_app/pages/wealth/children/innvestment_amount.dart';
import 'package:aurenixai_app/pages/wealth/children/investment.dart';
import 'package:aurenixai_app/pages/wealth/children/investment_signature.dart';
import 'package:aurenixai_app/pages/wealth/children/investment_successfully.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class Routes {
  /// 404
  static const empty = '/empty';

  /// 登录注册首页
  static const auth = '/auth';

  /// 登录
  static const signIn = '/signIn';

  /// 注册
  static const signUp = '/signUp';

  /// 验证邮箱
  static const verifyEmail = '/verifyEmail';

  /// 注册成功
  static const signUpSuccessfully = '/signUpSuccessfully';

  /// 重置密码
  static const resetPassword = '/resetPassword';

  /// 主页Tabs
  static const home = '/';

  /// 通知
  static const notification = '/notification';

  /// 通知详情
  static const notificationDetail = '/notificationDetail';

  /// funding account
  static const funding = '/funding';

  /// add funds
  static const addFundsChain = '/addFundsChain';
  static const addFundsChainRecharge = '/addFundsChainRecharge';
  static const addFundsUid = '/addFundsUid';
  static const addFundsCrypto = '/addFundsCrypto';
  static const addFundsCryptoConfirm = '/addFundsCryptoConfirm';

  /// send
  static const sendChain = '/sendChain';
  static const sendChainConfirm = '/sendChainConfirm';
  static const sendUidEmail = '/sendUidEmail';
  static const sendUidEmailConfirm = '/sendUidEmailConfirm';

  /// 地址簿
  static const addressBook = '/addressBook';
  static const addressBookAdd = '/addressBookAdd';
  static const addressBookAddUidEmail = '/addressBookAddUidEmail';
  static const addressSetTrustedPayee = '/addressSetTrustedPayee';

  /// 交易记录
  static const transactionHistoryFunding = '/transactionHistoryFunding';

  /// 奖金钱包
  static const commission = '/commission';

  /// 奖金钱包 转账
  static const commissionTransfer = '/commissionTransfer';

  /// 交易记录
  static const transactionHistoryCommission = '/transactionHistoryCommission';

  /// 交易记录详情
  static const transactionDetail = '/transactionDetail';

  /// KYC
  static const kyc = '/kyc';

  /// KYC Step1
  static const kycVerify = '/kycVerify';

  /// KYC Step2
  static const kycQuestion = '/kycQuestion';

  /// Google
  static const google = '/google';

  /// Bind Google
  static const bindGoogle = '/bindGoogle';

  /// Unbind Successfully
  static const unbindGoogleSuccessfully = '/unbindGoogleSuccessfully';

  /// Coupon
  static const coupon = '/coupon';

  /// Personal Info
  static const personalInfo = '/personalInfo';

  /// My Investment
  static const myInvestment = '/myInvestment';

  /// Redeem Successfully
  static const redeemSuccessfully = '/redeemSuccessfully';

  /// Investment
  static const investment = '/investment';

  /// Investment Amount
  static const innvestmentAmount = '/innvestmentAmount';

  /// Investment Signature
  static const investmentSignature = '/investmentSignature';

  /// Investment Successfully
  static const investmentSuccessfully = '/investmentSuccessfully';

  /// Earnings Tracker
  static const earningsTracker = '/earningsTracker';

  /// Team Action Successfully
  static const teamActionSuccessfully = '/teamActionSuccessfully';

  static final getPages = [
    GetPage(name: empty, page: () => const EmptyPage()),
    GetPage(name: auth, page: () => const AuthPage()),
    GetPage(name: signIn, page: () => const SignInPage()),
    GetPage(name: signUp, page: () => const SignUpPage()),
    GetPage(name: verifyEmail, page: () => const VerifyEmailPage()),
    GetPage(
      name: signUpSuccessfully,
      page: () => const SignUpSuccessfullyPage(),
    ),
    GetPage(name: resetPassword, page: () => const ResetPasswordPage()),
    GetPage(name: home, page: () => const TabsPage()),
    GetPage(name: notification, page: () => const NotificationPage()),
    GetPage(
      name: notificationDetail,
      page: () => const NotificationDetailPage(),
    ),
    GetPage(name: kyc, page: () => const KycPage()),
    GetPage(name: kycVerify, page: () => const KycVerifyPage()),
    GetPage(name: kycQuestion, page: () => const KycQuestionPage()),
    GetPage(name: google, page: () => const GooglePage()),
    GetPage(name: bindGoogle, page: () => const BindGooglePage()),
    GetPage(
      name: unbindGoogleSuccessfully,
      page: () => const UnbindGoogleSuccessfullyPage(),
    ),
    GetPage(name: coupon, page: () => const CouponPage()),
    GetPage(name: personalInfo, page: () => const PersonalInfoPage()),
    GetPage(name: myInvestment, page: () => const MyInvestmentPage()),
    GetPage(
      name: redeemSuccessfully,
      page: () => const RedeemSuccessfullyPage(),
    ),
    GetPage(name: funding, page: () => const FundingAccountPage()),
    GetPage(name: addFundsChain, page: () => const AddFundsChainPage()),
    GetPage(
      name: addFundsChainRecharge,
      page: () => const AddFundsChainRechargePage(),
    ),
    GetPage(name: addFundsUid, page: () => const AddFundsUidPage()),
    GetPage(name: addFundsCrypto, page: () => const AddFundsCryptoPage()),
    GetPage(
      name: addFundsCryptoConfirm,
      page: () => const AddFundsCryptoConfirmPage(),
    ),

    GetPage(name: sendChain, page: () => const SendChainPage()),
    GetPage(name: sendChainConfirm, page: () => const SendChainConfirmPage()),
    GetPage(name: sendUidEmail, page: () => const SendUidEmailPage()),
    GetPage(
      name: sendUidEmailConfirm,
      page: () => const SendUidEmailConfirmPage(),
    ),

    GetPage(name: addressBook, page: () => const AddressBookPage()),
    GetPage(name: addressBookAdd, page: () => const AddressBookAddPage()),
    GetPage(
      name: addressBookAddUidEmail,
      page: () => const AddressBookAddUidEmailPage(),
    ),

    GetPage(
      name: addressSetTrustedPayee,
      page: () => const AddressSetTrustedPayeePage(),
    ),

    GetPage(
      name: transactionHistoryFunding,
      page: () => const TransactionHistoryFundingPage(),
    ),
    GetPage(name: transactionDetail, page: () => const TransactionDetailPage()),

    GetPage(name: commission, page: () => const CommissionAccountPage()),
    GetPage(
      name: commissionTransfer,
      page: () => const CommissionTransferPage(),
    ),

    GetPage(
      name: transactionHistoryCommission,
      page: () => const TransactionHistoryCommissionPage(),
    ),
    GetPage(name: investment, page: () => const InvestmentPage()),
    GetPage(name: innvestmentAmount, page: () => const InnvestmentAmountPage()),
    GetPage(
      name: investmentSignature,
      page: () => const InvestmentSignaturePage(),
    ),
    GetPage(
      name: investmentSuccessfully,
      page: () => const InvestmentSuccessfullyPage(),
    ),
    GetPage(name: earningsTracker, page: () => const EarningsTrackerPage()),
    GetPage(
      name: teamActionSuccessfully,
      page: () => const TeamActionSuccessfullyPage(),
    ),
  ];
}
