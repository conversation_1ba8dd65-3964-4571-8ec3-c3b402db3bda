import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class UnbindGoogleVerification extends StatefulWidget {
  const UnbindGoogleVerification({super.key});

  static Future<void> showModal(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          bottom: false,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: UnbindGoogleVerification(),
          ),
        );
      },
    );
  }

  @override
  State<UnbindGoogleVerification> createState() =>
      _UnbindGoogleVerificationState();
}

class _UnbindGoogleVerificationState extends State<UnbindGoogleVerification> {
  String digitCode = '';
  bool isCodeError = false;
  void onSubmit() {
    if (digitCode.isEmpty) {
      setState(() {
        isCodeError = true;
      });
      return;
    }

    Get.back();
    Get.toNamed(Routes.unbindGoogleSuccessfully);
    EventBusUtil.fireUnbindGoogleSuccess(const UnbindGoogleSuccessEvent());
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'unbind_google_verification'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final titleStyle = TextStyle(
      fontSize: 14.sp,
      color: customTheme.subTextColor,
    );

    return SizedBox(
      height: 320.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 28.h),
              child: Column(
                children: [
                  SizedBox(height: 3.h),
                  Text(
                    'unbind_google_verification_description'.tr,
                    style: titleStyle.copyWith(fontSize: 13.5.sp),
                  ),
                  SizedBox(height: 12.5.h),
                  _pasteCode(),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'confirm'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }

  _pasteCode() {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 7.h,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 11.w, vertical: 12.5.h),
          decoration: BoxDecoration(
            color: customTheme.inputBgColor,
            borderRadius: BorderRadius.circular(3.w),
            border: Border.all(
              color: isCodeError ? customTheme.downColor : Colors.transparent,
            ),
          ),
          child: Row(
            spacing: 8.w,
            children: [
              Expanded(
                child: Text(
                  digitCode,
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: customTheme.primaryColor,
                  borderRadius: BorderRadius.circular(3.r),
                ),
                constraints: BoxConstraints(minWidth: 72.w),
                height: 24.h,
                child: TextButton(
                  onPressed: () {
                    Clipboard.getData(Clipboard.kTextPlain).then((value) {
                      if (value != null) {
                        setState(() {
                          digitCode = value.text ?? '';
                          isCodeError = false;
                        });
                      }
                    });
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    minimumSize: Size(0, 24.h),
                  ),
                  child: Text(
                    'paste'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (isCodeError)
          Text(
            'please_enter_a_valid_verification_code'.tr,
            style: TextStyle(fontSize: 12.sp, color: customTheme.downColor),
          ),
      ],
    );
  }
}
