import 'package:flutter/material.dart';

enum ThemeType { light, dark }

class CustomThemeSwatch {
  /// 页面背景
  final Color pageBgColor;

  /// 主颜色
  final Color primaryColor;

  /// 按钮背景颜色
  final Color buttonBgColor;

  /// 按钮文字颜色
  final Color buttonTextColor;

  /// 文字颜色
  final Color textColor;

  /// 标签颜色
  final Color labelColor;

  /// 副文本颜色
  final Color subTextColor;

  /// 较浅的文本颜色
  final Color lightTextColor;

  /// 提示文本颜色
  final Color hintTextColor;

  /// 输入框背景颜色
  final Color inputBgColor;

  /// 输入框提示文字颜色
  final Color inputHintColor;

  /// 分割线颜色
  final Color dividerColor;

  /// 模态框顶部条颜色
  final Color modalBarColor;

  /// 通知背景颜色
  final Color notificationBgColor;

  /// 上涨颜色
  final Color upColor;

  /// 下跌颜色
  final Color downColor;

  /// portfolioRedeemedBgColor
  final Color portfolioRedeemedBgColor;

  /// 身份颜色
  final Color proStartColor;
  final Color proEndColor;
  final Color proColor;
  final Color preferredStartColor;
  final Color preferredEndColor;
  final Color preferredColor;
  final Color eliteStartColor;
  final Color eliteEndColor;
  final Color eliteColor;
  final Color premierStartColor;
  final Color premierEndColor;
  final Color premierColor;
  final Color signatureStartColor;
  final Color signatureEndColor;
  final Color signatureColor;
  final Color platinumStartColor;
  final Color platinumMidColor;
  final Color platinumEndColor;
  final Color platinumColor;

  /// 其它
  final Color ff7ed032;
  final Color ff40e2fe;
  final Color ff363C64;
  final Color ff515165;
  final Color ff696978;
  final Color ff878C9f;
  final Color ff99adff;
  final Color ffd8f2c0;
  final Color fffce412;
  final Color ff1f1f2a;
  final Color ff1d1d24;

  // usdt充值页提示框背景色
  final Color addFundsRechargeTipsBg;
  final Color addFundsRechargeAddressBg1;
  final Color addFundsRechargeAddressBg2;

  CustomThemeSwatch({
    required this.pageBgColor,
    required this.primaryColor,
    required this.buttonBgColor,
    required this.buttonTextColor,
    required this.textColor,
    required this.labelColor,
    required this.subTextColor,
    required this.lightTextColor,
    required this.hintTextColor,
    required this.inputBgColor,
    required this.inputHintColor,
    required this.dividerColor,
    required this.modalBarColor,
    required this.notificationBgColor,
    required this.upColor,
    required this.downColor,
    required this.portfolioRedeemedBgColor,
    required this.proStartColor,
    required this.proEndColor,
    required this.proColor,
    required this.preferredStartColor,
    required this.preferredEndColor,
    required this.preferredColor,
    required this.eliteStartColor,
    required this.eliteEndColor,
    required this.eliteColor,
    required this.premierStartColor,
    required this.premierEndColor,
    required this.premierColor,
    required this.signatureStartColor,
    required this.signatureEndColor,
    required this.signatureColor,
    required this.platinumStartColor,
    required this.platinumMidColor,
    required this.platinumEndColor,
    required this.platinumColor,
    required this.ff7ed032,
    required this.ff40e2fe,
    required this.ff363C64,
    required this.ff515165,
    required this.ff696978,
    required this.ff878C9f,
    required this.ff99adff,
    required this.ffd8f2c0,
    required this.fffce412,
    required this.ff1f1f2a,
    required this.ff1d1d24,
    required this.addFundsRechargeTipsBg,
    required this.addFundsRechargeAddressBg1,
    required this.addFundsRechargeAddressBg2,
  });
}
