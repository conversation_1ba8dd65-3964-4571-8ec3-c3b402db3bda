import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class CountryRegionPicker extends StatefulWidget {
  const CountryRegionPicker({super.key, this.currentRegion});
  final Region? currentRegion;

  static Future<Region?> showModal(
    BuildContext context, {
    Region? currentRegion,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<Region?>(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.inputBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          child: CountryRegionPicker(currentRegion: currentRegion),
        );
      },
    );
  }

  @override
  State<CountryRegionPicker> createState() => _CountryRegionPickerState();
}

class _CountryRegionPickerState extends State<CountryRegionPicker> {
  late ScrollController scrollController;
  late TextEditingController searchController;

  List<Region> regions = [];
  int regionIndex = 0;

  String searchText = '';

  @override
  void initState() {
    super.initState();
    // 根据_regions的interName的首字母分组，并排序
    List<Region> _regions = context.read<LanguageProvider>().regionList;
    final groupedRegions = <String, List<Region>>{};
    for (final region in _regions) {
      final key = region.interName[0];
      groupedRegions.putIfAbsent(key, () => []).add(region);
    }
    final entries = groupedRegions.entries.toList();
    entries.sort((a, b) => a.key.compareTo(b.key));
    final sortedRegions = entries.map((entry) => entry.value).toList();
    for (var element in sortedRegions) {
      regions.addAll(element);
    }
    scrollController = ScrollController();
    searchController = TextEditingController();
    final initialRegion = widget.currentRegion;
    int initialIndex = 0;
    if (initialRegion != null) {
      initialIndex = regions.indexWhere(
        (region) => region.id == initialRegion.id,
      );
    } else {
      initialIndex = 0;
    }
    regionIndex = initialIndex;
    searchController.text = '';
    // 滚动到初始位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.jumpTo(regionIndex * (20.w + 12.w) + 16.w);
      }
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    searchController.dispose();
    super.dispose();
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 10.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'country_region'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w, bottom: 14.5.w),
            child: NormalInput(
              controller: searchController,
              hintText: 'please_enter_keywords_to_search'.tr,
              onChanged: (value) {
                setState(() {
                  searchText = value;
                });
              },
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(7.5.w),
                color: customTheme.pageBgColor,
              ),
              padding: EdgeInsets.symmetric(horizontal: 11.5.w),
              fontSize: 13.sp,
              height: 40.w,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  List<Region> get filterRegions {
    if (searchText.isEmpty) {
      return regions;
    }
    return regions
        .where(
          (region) =>
              region.interName.tr.toLowerCase().contains(
                searchText.toLowerCase(),
              ) ||
              region.interCode.toLowerCase().contains(searchText.toLowerCase()),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(
                  filterRegions.length,
                  (index) => Padding(
                    padding: EdgeInsets.only(bottom: 12.w),
                    child: GestureDetector(
                      onTap: () {
                        Get.back(result: filterRegions[index]);
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        spacing: 10.w,
                        children: [
                          Expanded(
                            child: Text(
                              filterRegions[index].interName.tr,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color:
                                    widget.currentRegion?.id ==
                                        filterRegions[index].id
                                    ? customTheme.primaryColor
                                    : customTheme.textColor,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          ConstrainedBox(
                            constraints: BoxConstraints(minWidth: 54.w),
                            child: Text(
                              filterRegions[index].interCode,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color:
                                    widget.currentRegion?.id ==
                                        filterRegions[index].id
                                    ? customTheme.primaryColor
                                    : customTheme.textColor,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
