import 'dart:io';

import 'package:aurenixai_app/application.dart';
import 'package:aurenixai_app/config/index.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_client.dart';

void main() async {
  ToastUtil.init();
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  try {
    final siteKey = Platform.isAndroid
        ? AppConfig.recaptchaAndroidKey
        : AppConfig.recaptchaIOSKey;

    RecaptchaClient client = await Recaptcha.fetchClient(siteKey);
    runApp(Application(client: client));
  } catch (e) {
    runApp(Application());
  }

  /// 强制竖屏
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}
