import 'package:json_annotation/json_annotation.dart';

part 'region.g.dart';

@JsonSerializable()
class Region {
  Region();

  late String id;
  late String interCode;
  late String interName;
  late String chineseName;
  late String interSimpleCode;
  late String pic;
  late String continent;

  factory Region.fromJson(Map<String, dynamic> json) => _$RegionFromJson(json);
  Map<String, dynamic> toJson() => _$RegionToJson(this);
}
