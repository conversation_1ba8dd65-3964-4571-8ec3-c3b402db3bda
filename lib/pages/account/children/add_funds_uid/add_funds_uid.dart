import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';

class AddFundsUidPage extends StatefulWidget {
  const AddFundsUidPage({super.key});

  @override
  _AddFundsUidPageState createState() => _AddFundsUidPageState();
}

class _AddFundsUidPageState extends State<AddFundsUidPage>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void onSaveCode() async {
    Get.back();
  }

  void onShareCode() async {
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    return Scaffold(
      appBar: AppBar(title: Text('Add_Funds'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                children: [
                  SizedBox(height: 19.h),
                  Text(
                    'Scan to Receive via AURENIX AI',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 39.h),
                  Container(
                    padding: EdgeInsets.all(2.w), // 边框厚度
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFF5F7EFC), Color(0xFF40E2FE)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15.w),
                    ),
                    child: Container(
                      width: 205.w,
                      height: 205.w,
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(15.w),
                      ),
                      child: Center(
                        child: QrImageView(
                          data: 'https://www.google.com',
                          version: QrVersions.auto,
                          size: 166.w,
                          padding: EdgeInsets.all(11.w),
                          backgroundColor: customTheme.textColor,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 39.h),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 27.w,
                    ),
                    decoration: BoxDecoration(
                      color: customTheme.inputBgColor,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(15.w),
                        bottomRight: Radius.circular(15.w),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          customTheme
                              .addFundsRechargeAddressBg1, // rgba(43,43,56,0)
                          customTheme.addFundsRechargeAddressBg2, // #282835
                        ],
                        stops: const [0.0, 1.0],
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              width: 2.w,
                              height: 13.h,
                              decoration: BoxDecoration(
                                color: customTheme.primaryColor,
                                borderRadius: BorderRadius.circular(1.w),
                              ),
                            ),
                            SizedBox(width: 5.w),
                            Text(
                              'UID'.tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: customTheme.subTextColor,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                'TDSb9RM79JVnQg',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: customTheme.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                            SizedBox(width: 5.w),
                            Image.asset(
                              customThemeImage.addFundsChainRechargeCopy,
                              width: 22.w,
                              height: 22.w,
                              fit: BoxFit.fill,
                            ),
                          ],
                        ),

                        SizedBox(height: 23.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              width: 2.w,
                              height: 13.h,
                              decoration: BoxDecoration(
                                color: customTheme.primaryColor,
                                borderRadius: BorderRadius.circular(1.w),
                              ),
                            ),
                            SizedBox(width: 5.w),
                            Text(
                              'Email'.tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: customTheme.subTextColor,
                              ),
                            ),
                            SizedBox(width: 5.w),
                            Expanded(
                              child: Text(
                                'lee***gmail.com',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: customTheme.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                            SizedBox(width: 5.w),
                            Image.asset(
                              customThemeImage.addFundsChainRechargeCopy,
                              width: 22.w,
                              height: 22.w,
                              fit: BoxFit.fill,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildItem({
    required String label,
    required String value,
    required CustomThemeSwatch customTheme,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.w,
              fontWeight: FontWeight.w400,
              color: customTheme.hintTextColor,
            ),
          ),
          SizedBox(width: 40.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12.w,
                fontWeight: FontWeight.w600,
                color: customTheme.textColor,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
