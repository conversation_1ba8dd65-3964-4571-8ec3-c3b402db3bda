import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class CommonPicker extends StatelessWidget {
  final List<String> list;
  final String? title;
  final Widget Function(BuildContext context, int index)? contentBuilder;

  const CommonPicker({
    super.key,
    this.list = const [],
    this.title,
    this.contentBuilder,
  });

  static Future<int?> showModal(
    BuildContext context, {
    List<String> list = const [],
    String? title,
    Widget Function(BuildContext context, int index)? contentBuilder,
  }) async {
    final customTheme = context.read<ThemeProvider>().customTheme;

    int? index = await showModalBottomSheet<int>(
      context: context,
      enableDrag: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return CommonPicker(
          list: list,
          title: title,
          contentBuilder: contentBuilder,
        );
      },
    );
    return index;
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title ?? 'please_select'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  Widget _item(BuildContext context, int index) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return GestureDetector(
      onTap: () => Navigator.of(context).pop(index),
      child: Container(
        height: 55.w,
        decoration: BoxDecoration(
          color: customTheme.inputBgColor,
          borderRadius: BorderRadius.circular(3.w),
        ),
        padding: EdgeInsets.symmetric(horizontal: 17.w),
        margin: EdgeInsets.only(bottom: 12.h),
        alignment: Alignment.centerLeft,
        child: contentBuilder != null
            ? contentBuilder!(context, index)
            : Text(
                list[index],
                style: TextStyle(
                  fontSize: 15.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = [];
    for (int i = 0; i < list.length; i++) {
      widgets.add(
        ListTile(
          title: Center(child: Text(list[i].tr)),
          onTap: () => Navigator.of(context).pop(i),
        ),
      );
    }

    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          SizedBox(height: 19.h),
          ...List.generate(list.length, (index) => _item(context, index)),
        ],
      ),
    );
  }
}
