import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/config/index.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/models/navigate.dart';
import 'package:aurenixai_app/pages/home/<USER>/market_card.dart';
import 'package:aurenixai_app/pages/home/<USER>/rencent_activity.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/pages/home/<USER>/notification_bar.dart';
import 'package:aurenixai_app/pages/home/<USER>/balance_card.dart';
import 'package:aurenixai_app/pages/home/<USER>/insights_card.dart';
import 'package:aurenixai_app/pages/home/<USER>/user_card.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/language_pick.dart';
import 'package:aurenixai_app/widgets/theme_pick.dart';
import 'package:crisp_chat/crisp_chat.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final GlobalKey _menuKey = GlobalKey();
  bool showWelcome = true;
  late EasyRefreshController _controller;
  List<Navigate> bannerList = [];
  bool isOpening = false;

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(controlFinishRefresh: true);
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          showWelcome = false;
        });
      }
    });
    _onRefresh();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    await Future.wait([_getBannerList()]);
    _controller.finishRefresh(IndicatorResult.success);
  }

  Future<void> _getBannerList() async {
    try {
      final res = await CommonApi.getNavigateList('app_home');
      setState(() {
        bannerList = res;
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    final customeThemeImage = context.watch<ThemeProvider>().customThemeImage;
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final languageProvider = context.watch<LanguageProvider>();
    final region = languageProvider.region;
    final languageCode = languageProvider.locale?.languageCode.toUpperCase();

    return Scaffold(
      appBar: AppBar(
        title: Image.asset(
          customeThemeImage.homeLogo,
          width: 140.w,
          height: 27.w,
        ),
        actions: [
          IconButton(
            onPressed: () {
              Get.toNamed(Routes.notification);
            },
            icon: Image.asset(
              customeThemeImage.homeMessage,
              width: 22.w,
              height: 22.w,
              fit: BoxFit.fill,
            ),
          ),
          TextButton(
            onPressed: () {
              LanguagePickModal.showModal(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              padding: EdgeInsets.symmetric(horizontal: 4.w),
            ),
            child: region != null && languageCode != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    spacing: 5.w,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(11.w),
                        child: Image.network(
                          region.pic,
                          height: 22.w,
                          width: 22.w,
                          fit: BoxFit.fill,
                        ),
                      ),
                      Text(
                        languageCode,
                        style: TextStyle(
                          color: customTheme.textColor,
                          fontSize: 15.sp,
                        ),
                      ),
                    ],
                  )
                : Text(
                    '-',
                    style: TextStyle(
                      color: customTheme.textColor,
                      fontSize: 15.sp,
                    ),
                  ),
          ),
          IconButton(
            key: _menuKey,
            onPressed: () async {
              ThemePickModal.showModal(context, _menuKey);
            },
            icon: Icon(
              Icons.more_horiz,
              color: customTheme.textColor,
              size: 22.w,
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: _onRefresh,
              header: WidgetUtil.getRefreshOnStartHeader(context),
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  children: [
                    NotificationBar(),
                    SizedBox(height: 19.5.w),
                    UserCard(),
                    SizedBox(height: 14.w),
                    BalanceCard(),
                    SizedBox(height: 20.w),
                    InsightsCard(bannerList: bannerList),
                    RencentActivity(),
                    SizedBox(height: 20.w),
                    MarketCard(),
                    SizedBox(height: 40.w),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 20.w,
            right: 15.w,
            child: IconButton(
              onPressed: () async {
                if (isOpening) {
                  return;
                }
                final userInfo = context.read<AuthProvider>().user;
                if (userInfo == null) {
                  return;
                }
                try {
                  final crispUser = User(
                    email: userInfo.email,
                    nickName: userInfo.email,
                    phone: userInfo.mobile ?? '',
                    avatar: userInfo.photo,
                  );
                  final _crispConfig = CrispConfig(
                    websiteID: AppConfig.crispWebsiteID,
                    user: crispUser,
                    enableNotifications: false,
                  );
                  await FlutterCrispChat.openCrispChat(config: _crispConfig);
                } catch (e) {
                } finally {
                  isOpening = false;
                }
              },
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              icon: Image.asset(
                customeThemeImage.homeChat,
                width: 45.w,
                height: 39.5.w,
                fit: BoxFit.fill,
              ),
            ),
          ),
          Positioned(
            right: 15.w,
            bottom: 64.5.w,
            child: AnimatedSwitcher(
              duration: Duration(milliseconds: 300),
              child: showWelcome
                  ? Column(
                      key: ValueKey('bubble'),
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 17.w,
                            vertical: 13.5.w,
                          ),
                          decoration: BoxDecoration(
                            color: customTheme.primaryColor,
                            borderRadius: BorderRadius.circular(8.w),
                          ),
                          child: Text(
                            "Welcome! How can I help you today?",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 20.w),
                          child: CustomPaint(
                            size: Size(10.w, 6.w),
                            painter: TrianglePainter(
                              color: customTheme.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    )
                  : SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;
  TrianglePainter({required this.color});
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;
    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width / 2, size.height)
      ..lineTo(size.width, 0)
      ..close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
