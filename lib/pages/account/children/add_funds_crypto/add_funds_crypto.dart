import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/common_picker.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/normal_select.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddFundsCryptoPage extends StatefulWidget {
  const AddFundsCryptoPage({super.key});

  @override
  _AddFundsCryptoPageState createState() => _AddFundsCryptoPageState();
}

class _AddFundsCryptoPageState extends State<AddFundsCryptoPage>
    with SingleTickerProviderStateMixin {
  List<String> currencyList = ['USDT', 'USDC'];
  int currencyIndex = 0;
  TextEditingController accountController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    accountController.dispose();
    super.dispose();
  }

  void onConfirm() async {
    Get.toNamed(Routes.addFundsCryptoConfirm);
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    List<String> currencyImage = [
      customThemeImage.accountCurrencyUsdt,
      customThemeImage.accountCurrencyUsdc,
    ];
    return Scaffold(
      appBar: AppBar(title: Text('Add_Funds'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 8.h,
                children: [
                  SizedBox(height: 27.h),
                  Text(
                    "Buy_Crypto".tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 19.w,
                      vertical: 10.w,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3.w),
                      color: customTheme.inputBgColor,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NormalInput(
                          height: 47.h,
                          hintText: '0'.tr,
                          fontSize: 23.sp,
                          controller: accountController,
                          padding: EdgeInsets.zero,
                          contentPadding: EdgeInsets.only(left: 5.w),
                          prefix: Text(
                            '\$',
                            style: TextStyle(
                              fontSize: 23.sp,
                              color: customTheme.hintTextColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Text(
                          'Minimum amount: \$ 1',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: customTheme.hintTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20.h),
                  Text(
                    "Receive_USDT".tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  Stack(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 19.w,
                          vertical: 10.w,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3.w),
                          color: customTheme.inputBgColor,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NormalInput(
                              height: 47.h,
                              hintText: '0'.tr,
                              fontSize: 23.sp,
                              controller: accountController,
                              padding: EdgeInsets.zero,
                              contentPadding: EdgeInsets.only(left: 5.w),
                              prefix: Text(
                                '\$',
                                style: TextStyle(
                                  fontSize: 23.sp,
                                  color: customTheme.hintTextColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Text(
                              '1 USDT ≈ 1 USD ',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: customTheme.hintTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 12.w,
                        width: 95.w,
                        child: NormalSelect(
                          hintText: 'select_currency'.tr,
                          content: currencyList[currencyIndex],
                          padding: EdgeInsets.zero,
                          rowSpacing: 0,
                          rowMainAxisSizeMin: true,
                          prefix: Padding(
                            padding: EdgeInsetsGeometry.only(right: 7.w),
                            child: Image.asset(
                              currencyImage[currencyIndex],
                              width: 22.w,
                              height: 22.w,
                              fit: BoxFit.fill,
                            ),
                          ),
                          onTap: () {
                            CommonPicker.showModal(
                                  context,
                                  title: 'select_currency'.tr,
                                  list: currencyList,
                                  contentBuilder: (context, index) => Row(
                                    children: [
                                      Image.asset(
                                        currencyImage[index],
                                        width: 22.w,
                                        height: 22.w,
                                        fit: BoxFit.fill,
                                      ),
                                      SizedBox(width: 10.w),
                                      Text(
                                        currencyList[index],
                                        style: TextStyle(
                                          fontSize: 15.sp,
                                          color: customTheme.textColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                                .then((index) {
                                  if (index != null) {
                                    setState(() {
                                      currencyIndex = index;
                                    });
                                  }
                                })
                                .catchError((e) {});
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.h),
                  NormalSelect(
                    label: 'Pay_with_Visa_Master_Card'.tr,
                    hintText: 'select_currency'.tr,
                    content: currencyList[currencyIndex],
                    prefix: Image.asset(
                      currencyImage[currencyIndex],
                      width: 22.w,
                      height: 22.w,
                      fit: BoxFit.fill,
                    ),
                    onTap: () {
                      CommonPicker.showModal(
                            context,
                            title: 'select_currency'.tr,
                            list: currencyList,
                            contentBuilder: (context, index) => Row(
                              children: [
                                Image.asset(
                                  currencyImage[index],
                                  width: 22.w,
                                  height: 22.w,
                                  fit: BoxFit.fill,
                                ),
                                SizedBox(width: 7.w),
                                Text(
                                  currencyList[index],
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    color: customTheme.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          )
                          .then((index) {
                            if (index != null) {
                              setState(() {
                                currencyIndex = index;
                              });
                            }
                          })
                          .catchError((e) {});
                    },
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'confirm'.tr,
            onSubmit: onConfirm,
          ),
        ],
      ),
    );
  }
}
