import 'package:aurenixai_app/widgets/custom_animation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

/// 提示框
class ToastUtil {
  static void init() {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.dark
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.yellow
      ..backgroundColor = Colors.green
      ..indicatorColor = Colors.yellow
      ..textColor = Colors.yellow
      ..maskColor = Colors.blue.withValues(alpha: 0.5)
      ..userInteractions = false
      ..dismissOnTap = false
      ..customAnimation = CustomAnimation();
  }

  static Future<void> showToast(String status, {Duration? duration}) async {
    await EasyLoading.showToast(status, duration: duration, dismissOnTap: true);
  }

  static Future<void> dismiss() async {
    await EasyLoading.dismiss();
  }

  static Future<void> showLoading({String? status}) async {
    await EasyLoading.show(status: status);
  }

  static Future<void> showSuccess(String status, {Duration? duration}) async {
    await EasyLoading.showSuccess(
      status,
      duration: duration,
      dismissOnTap: true,
    );
  }

  static Future<void> showError(String status, {Duration? duration}) async {
    await EasyLoading.showError(status, duration: duration, dismissOnTap: true);
  }

  static Future<void> showInfo(String status, {Duration? duration}) async {
    await EasyLoading.showInfo(status, duration: duration, dismissOnTap: true);
  }

  static Future<void> showProgress(double value, {String? status}) async {
    await EasyLoading.showProgress(value, status: status);
  }
}
