import 'package:aurenixai_app/models/fund_investment_record_res.dart';
import 'package:aurenixai_app/pages/portfolio/children/redeem_confirm.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class RedeemAmountPage extends StatefulWidget {
  const RedeemAmountPage({
    super.key,
    required this.investment,
    required this.productName,
  });

  final FundInvestmentRecordRes investment;
  final String productName;

  @override
  _RedeemAmountPageState createState() => _RedeemAmountPageState();
}

class _RedeemAmountPageState extends State<RedeemAmountPage> {
  final TextEditingController _amountController = TextEditingController();

  bool isError = false;
  String errorMessage = '';
  double maxReedemAmount = 0;
  double redeemFeeRate = 0;
  double receiveAmount = 0;
  double redeemFee = 0;

  @override
  void initState() {
    super.initState();
    maxReedemAmount = widget.investment.currentBalance.toDouble();
    redeemFeeRate = (widget.investment.fee / 100).toDouble();
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void onConfirm() async {
    // 检查是否有错误或输入为空
    if (isError || _amountController.text.isEmpty || receiveAmount <= 0) {
      return;
    }
    Get.to(
      () => RedeemConfirmPage(
        investment: widget.investment,
        productName: widget.productName,
        redeemFeeRate: widget.investment.fee.toString(),
        receiveAmount: receiveAmount.toString(),
        redeemFee: redeemFee.toString(),
        redeemAmount: _amountController.text,
      ),
    );
  }

  bool get isConfirmEnabled {
    return !isError && _amountController.text.isNotEmpty && receiveAmount > 0;
  }

  /// 安全的乘法运算，避免浮点数精度问题
  double _safeMultiply(double a, double b) {
    // 将小数转换为整数进行计算，然后再转回小数
    // 假设最多保留4位小数精度
    const int precision = 10000;
    int intA = (a * precision).round();
    int intB = (b * precision).round();
    double result = (intA * intB) / (precision * precision);
    // 保留2位小数
    return double.parse(result.toStringAsFixed(2));
  }

  /// 安全的减法运算，避免浮点数精度问题
  double _safeSubtract(double a, double b) {
    // 将小数转换为整数进行计算，然后再转回小数
    const int precision = 10000;
    int intA = (a * precision).round();
    int intB = (b * precision).round();
    double result = (intA - intB) / precision;
    // 保留2位小数
    return double.parse(result.toStringAsFixed(2));
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final currentThemeType = context.read<ThemeProvider>().currentThemeType;

    return Scaffold(
      appBar: AppBar(title: Text('redemption'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 8.h,
                children: [
                  Text(
                    'enter_redemption_amount'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: customTheme.textColor,
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: customTheme.inputBgColor,
                      borderRadius: BorderRadius.circular(3.w),
                      border: Border.all(
                        color: isError
                            ? customTheme.downColor
                            : Colors.transparent,
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 19.w,
                      vertical: 20.h,
                    ),
                    child: Row(
                      spacing: 4.w,
                      children: [
                        Text(
                          '\$',
                          style: TextStyle(
                            fontSize: 23.sp,
                            color: customTheme.textColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _amountController,
                            keyboardType: TextInputType.number,
                            keyboardAppearance:
                                currentThemeType == ThemeType.dark
                                ? Brightness.dark
                                : Brightness.light,
                            style: TextStyle(
                              fontSize: 23.sp,
                              color: customTheme.textColor,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: '0.00',
                              hintStyle: TextStyle(
                                fontSize: 23.sp,
                                color: customTheme.hintTextColor,
                                fontWeight: FontWeight.w600,
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                            cursorColor: customTheme.textColor,
                            onChanged: (value) {
                              setState(() {
                                // 重置状态
                                isError = false;
                                errorMessage = '';
                                redeemFee = 0;
                                receiveAmount = 0;

                                // 如果输入为空，直接返回
                                if (value.isEmpty) {
                                  return;
                                }

                                // 判断输入的金额是否为数字
                                final double? inputAmount = double.tryParse(
                                  value,
                                );
                                if (inputAmount == null) {
                                  isError = true;
                                  errorMessage =
                                      'please_enter_correct_redemption_amount'
                                          .tr;
                                  return;
                                }

                                // 如果输入的金额小于等于0，显示错误提示
                                if (inputAmount <= 0) {
                                  isError = true;
                                  errorMessage =
                                      'please_enter_correct_redemption_amount'
                                          .tr;
                                  return;
                                }

                                // 如果输入的金额大于最大可赎回金额，显示错误提示
                                if (inputAmount > maxReedemAmount) {
                                  isError = true;
                                  errorMessage =
                                      'amount_exceeds_maximum_redeemable'.tr;
                                  return;
                                }

                                // 计算赎回费和实际收到的金额（使用安全的小数运算）
                                // 赎回费 = 输入的金额 * 赎回费率
                                redeemFee = _safeMultiply(
                                  inputAmount,
                                  redeemFeeRate,
                                );
                                // 实际收到的金额 = 输入的金额 - 赎回费
                                receiveAmount = _safeSubtract(
                                  inputAmount,
                                  redeemFee,
                                );
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isError)
                    Padding(
                      padding: EdgeInsets.only(top: 3.h, bottom: 10.h),
                      child: Text(
                        errorMessage.isNotEmpty
                            ? errorMessage
                            : 'please_enter_correct_redemption_amount'.tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: customTheme.downColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomWrapper(
            context: context,
            child: Column(
              children: [
                SizedBox(height: 6.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'receive_amount'.tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                    Text(
                      '\$ ${receiveAmount.toString().fmt}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10.5.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${'redemption_fees'.tr} (${(redeemFeeRate * 100).toStringAsFixed(2).removeZero}%)',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: customTheme.lightTextColor,
                      ),
                    ),
                    Text(
                      '\$ ${redeemFee.toString().fmt}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 19.5.h),
                PrimaryButton(
                  title: 'confirm'.tr,
                  onPress: isConfirmEnabled ? onConfirm : null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
