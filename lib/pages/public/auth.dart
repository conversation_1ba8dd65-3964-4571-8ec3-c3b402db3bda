import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/widgets/language_pick.dart';
import 'package:aurenixai_app/widgets/outline_button.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:aurenixai_app/widgets/theme_pick.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AuthPage extends StatefulWidget {
  const AuthPage({super.key});

  @override
  _AuthState createState() => _AuthState();
}

class _AuthState extends State<AuthPage> {
  final GlobalKey _menuKey = GlobalKey();
  @override
  Widget build(BuildContext context) {
    final customeThemeImage = context.watch<ThemeProvider>().customThemeImage;
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final languageProvider = context.watch<LanguageProvider>();
    final region = languageProvider.region;
    final languageCode = languageProvider.locale?.languageCode.toUpperCase();

    // 使用工具类获取AppBar高度
    final appBarHeight = CommonUtils.getAppBarHeight(context);

    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          actions: [
            TextButton(
              onPressed: () {
                LanguagePickModal.showModal(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                padding: EdgeInsets.symmetric(horizontal: 4.w),
              ),
              child: region != null && languageCode != null
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 5.w,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(11.w),
                          child: Image.network(
                            region.pic,
                            height: 22.w,
                            width: 22.w,
                            fit: BoxFit.fill,
                          ),
                        ),
                        Text(
                          languageCode,
                          style: TextStyle(
                            color: customTheme.textColor,
                            fontSize: 15.sp,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      '-',
                      style: TextStyle(
                        color: customTheme.textColor,
                        fontSize: 15.sp,
                      ),
                    ),
            ),
            IconButton(
              key: _menuKey,
              onPressed: () async {
                ThemePickModal.showModal(context, _menuKey);
              },
              icon: Icon(
                Icons.more_horiz,
                color: customTheme.textColor,
                size: 22.w,
              ),
            ),
          ],
        ),
        extendBodyBehindAppBar: true,
        body: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Image.asset(
                customeThemeImage.authBgBottom,
                width: MediaQuery.of(context).size.width,
                height: 313.h,
                fit: BoxFit.cover,
                alignment: Alignment.topCenter,
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Image.asset(
                customeThemeImage.authBgTop,
                width: MediaQuery.of(context).size.width,
                height: 313.h,
                fit: BoxFit.cover,
                alignment: Alignment.bottomCenter,
              ),
            ),
            Positioned.fill(
              left: 30.w,
              right: 30.w,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: appBarHeight + 151.h),
                    Image.asset(
                      customeThemeImage.authLogo,
                      width: 179.w,
                      height: 120.w,
                    ),
                    SizedBox(height: 100.h),
                    OutlineButton(
                      title: 'create_your_account'.tr,
                      onPress: () {
                        Get.toNamed(Routes.signUp);
                      },
                    ),
                    SizedBox(height: 22.h),
                    PrimaryButton(
                      title: 'sign_in'.tr,
                      onPress: () {
                        Get.toNamed(Routes.signIn);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
