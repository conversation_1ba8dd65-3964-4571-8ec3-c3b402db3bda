import 'package:aurenixai_app/pages/services/utils/identity_util.dart';
import 'package:aurenixai_app/pages/services/widgets/edit_nickname.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/widgets/common_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class UserCard extends StatelessWidget {
  const UserCard({super.key});

  @override
  Widget build(BuildContext context) {
    final authInfo = context.watch<AuthProvider>();
    final user = authInfo.user;
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    final customThemeImage = Provider.of<ThemeProvider>(
      context,
    ).customThemeImage;

    if (user == null) {
      return SizedBox.shrink();
    }

    final level = user.levelName;
    final identity = user.nodeLevelName;

    return Row(
      spacing: 12.5.w,
      children: [
        Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: 1.h),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [customTheme.ff515165, customTheme.inputBgColor],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  shape: BoxShape.circle,
                ),
                width: 80.w,
                height: 80.w,
                alignment: Alignment.center,
                child: CommonImage(
                  user.levelPic,
                  width: 56.w,
                  height: 54.w,
                  type: CommonImageType.square,
                  fit: BoxFit.fill,
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              child: SizedBox(
                width: 80.w,
                child: Center(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: IdentityUtil.getLevelGradient(level, context),
                      borderRadius: BorderRadius.circular(8.5.w),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.5.w,
                      vertical: 0.5.w,
                    ),
                    child: Text(
                      level,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: IdentityUtil.getLevelColor(level, context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    user.nickname,
                    style: TextStyle(
                      fontSize: 22.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      EditNickname.showModal(context, nickname: user.nickname);
                    },
                    child: Image.asset(
                      customThemeImage.servicesEdit,
                      width: 22.w,
                      height: 22.w,
                      fit: BoxFit.fill,
                    ),
                  ),
                  SizedBox(width: 9.w),
                  if (user.nodeLevel > 1)
                    Stack(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            top: 2.5.h,
                            left: 2.w,
                            bottom: 2.5.h,
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: IdentityUtil.getIdentityGradient(
                                identity,
                                context,
                              ),
                              borderRadius: BorderRadius.circular(8.5.w),
                            ),
                            height: 16.5.w,
                            padding: EdgeInsets.only(
                              left: 15.5.w,
                              right: 5.5.w,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              identity,
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: IdentityUtil.getIdentityColor(
                                  identity,
                                  context,
                                )!,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 0,
                          top: 0,
                          child: CommonImage(
                            user.nodePic ?? '',
                            height: 22.w,
                            type: CommonImageType.square,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              SizedBox(height: 6.w),
              Row(
                spacing: 8.5.w,
                children: [
                  Text(
                    'UID: ${user.id}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: customTheme.subTextColor,
                    ),
                  ),
                  Text(
                    authInfo.isKyc ? 'verified'.tr : 'unverified'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: authInfo.isKyc
                          ? customTheme.ff7ed032
                          : customTheme.downColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 5.w),
              Text(
                '${'email'.tr}: ${user.email.maskEmail}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: customTheme.subTextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
