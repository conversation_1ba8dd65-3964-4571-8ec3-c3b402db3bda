import 'package:json_annotation/json_annotation.dart';

part 'fund_product_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class FundProductRes {
  FundProductRes();

  late String id;
  late String name;
  late num lockinPeriodDays;
  late num interestCycleDays;
  late num interestRatePerCycle;
  late num redemptionPeriodDays;
  late num minAmount;
  late String allowReinvest;
  String? text;
  String? introduce;
  String? structure;
  String? description;

  factory FundProductRes.fromJson(Map<String, dynamic> json) =>
      _$FundProductResFromJson(json);
  Map<String, dynamic> toJson() => _$FundProductResToJson(this);
}
