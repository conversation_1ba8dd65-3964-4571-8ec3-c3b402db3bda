import 'dart:async';

import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/models/user_info.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/common_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'widgets/edit_mobile.dart';
import 'widgets/edit_password.dart';

class PersonalInfoPage extends StatefulWidget {
  const PersonalInfoPage({super.key});

  @override
  _PersonalInfoPageState createState() => _PersonalInfoPageState();
}

class _PersonalInfoPageState extends State<PersonalInfoPage> {
  UserInfo? userInfo;
  List<String> idPhotoList = [];
  late StreamSubscription<ModifyMobileEvent> _modifyMobileSubscription;

  @override
  void initState() {
    super.initState();
    _modifyMobileSubscription = EventBusUtil.listenModifyMobile((event) {
      setState(() {
        userInfo?.phoneNumber = event.mobile;
        userInfo?.countryCode = event.countryCode;
      });
    });
    getPersonalInfo();
  }

  @override
  void dispose() {
    _modifyMobileSubscription.cancel();
    super.dispose();
  }

  getPersonalInfo() async {
    try {
      ToastUtil.showLoading();
      userInfo = await UserApi.getPersonalInfo();
      idPhotoList = userInfo!.idPhoto?.split(',') ?? [];
      setState(() {});
      ToastUtil.dismiss();
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('personal_info_h'.tr)),
      body: userInfo != null
          ? SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 14.h),
              child: Column(
                children: [
                  _title(
                    'basic_info'.tr,
                    canEdit: true,
                    onTap: () {
                      EditMobile.showModal(context);
                    },
                  ),
                  _basicInfo(userInfo!),
                  SizedBox(height: 2.h),
                  _title('kyc_verification'.tr),
                  _kycVerification(userInfo!),
                  SizedBox(height: 2.h),
                  _title('account_status'.tr),
                  _accountStatus(userInfo!),
                  _title(
                    'security_setting_access'.tr,
                    canEdit: true,
                    onTap: () {
                      EditPassword.showModal(context);
                    },
                  ),
                  _securitySettingAccess(),
                  SizedBox(height: 20.h),
                ],
              ),
            )
          : SizedBox.shrink(),
    );
  }

  _line(String title, {String? value, bool highlight = false}) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final labelStyle = TextStyle(
      fontSize: 13.sp,
      color: customTheme.subTextColor,
      fontWeight: FontWeight.w600,
    );

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
      color: highlight ? customTheme.inputBgColor : Colors.transparent,
      child: Row(
        spacing: 5.w,
        children: [
          Expanded(flex: 149, child: Text(title, style: labelStyle)),
          Expanded(
            flex: 161,
            child: Text(
              value != null && value.isNotEmpty ? value : 'N/A',
              style: labelStyle.copyWith(color: customTheme.textColor),
            ),
          ),
        ],
      ),
    );
  }

  _photoLine(
    String title, {
    required List<String> photos,
    bool highlight = false,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final labelStyle = TextStyle(
      fontSize: 13.sp,
      color: customTheme.subTextColor,
      fontWeight: FontWeight.w600,
    );

    bool isEmpty = photos.isEmpty;

    if (isEmpty) {
      return _line(title, highlight: highlight, value: null);
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
      color: highlight ? customTheme.inputBgColor : Colors.transparent,
      child: Row(
        spacing: 5.w,
        children: [
          Expanded(flex: 149, child: Text(title, style: labelStyle)),
          Expanded(
            flex: 161,
            child: Row(
              spacing: 7.5.w,
              children: List.generate(
                photos.length,
                (index) => Expanded(
                  child: CommonImage(
                    photos[index],
                    height: 48.w,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _basicInfo(UserInfo userInfo) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    String? phoneNumber;
    if (userInfo.countryCode != null && userInfo.phoneNumber != null) {
      phoneNumber = '${userInfo.countryCode}-${userInfo.phoneNumber}';
    }
    return Container(
      margin: EdgeInsets.only(top: 15.5.h, bottom: 24.h),
      decoration: BoxDecoration(
        border: Border.all(color: customTheme.inputBgColor, width: 0.5),
      ),
      child: Column(
        children: [
          _line('full_name'.tr, value: userInfo.fullName, highlight: true),
          _line('phone_number'.tr, value: phoneNumber),
          _line(
            'country_region'.tr,
            value: userInfo.countryName,
            highlight: true,
          ),
          _line('email'.tr, value: userInfo.email),
        ],
      ),
    );
  }

  _kycVerification(UserInfo userInfo) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    String? documentType;
    if (userInfo.documentType != null &&
        userInfo.documentNumber != null &&
        userInfo.verStatus == '1') {
      documentType = userInfo.documentType == '0'
          ? 'id_card'.tr
          : 'passport'.tr;
    }

    return Container(
      margin: EdgeInsets.only(top: 15.5.h, bottom: 24.h),
      decoration: BoxDecoration(
        border: Border.all(color: customTheme.inputBgColor, width: 0.5),
      ),
      child: Column(
        children: [
          _line('document_type'.tr, value: documentType, highlight: true),
          _line(
            'document_number'.tr,
            value: userInfo.verStatus == '1' ? userInfo.documentNumber : null,
          ),
          _photoLine(
            'id_photo_upload'.tr,
            photos: idPhotoList,
            highlight: true,
          ),
          _line(
            'verification_status'.tr,
            value: userInfo.verStatus == '1' || userInfo.verStatus == '2'
                ? 'verified'.tr
                : 'unverified'.tr,
          ),
        ],
      ),
    );
  }

  _accountStatus(UserInfo userInfo) {
    final nodeLevel = context.read<AuthProvider>().user?.nodeLevel;
    final customTheme = context.read<ThemeProvider>().customTheme;
    String accountLevel = userInfo.accountLevel;
    if (userInfo.nameLevel != null && nodeLevel != null && nodeLevel > 1) {
      accountLevel = '$accountLevel | ${userInfo.nameLevel}';
    }

    return Container(
      margin: EdgeInsets.only(top: 15.5.h, bottom: 24.h),
      decoration: BoxDecoration(
        border: Border.all(color: customTheme.inputBgColor, width: 0.5),
      ),
      child: Column(
        children: [
          _line(
            'kyc_status'.tr,
            value: userInfo.verStatus == '1' ? 'completed'.tr : null,
            highlight: true,
          ),
          _line(
            'withdrawal'.tr,
            value: userInfo.verStatus == '1' || userInfo.verStatus == '2'
                ? 'enabled'.tr
                : null,
          ),
          _line('account_level'.tr, value: accountLevel, highlight: true),
          _line(
            'account_status'.tr,
            value: userInfo.accountStatus == '1'
                ? 'account_status_active'.tr
                : null,
          ),
        ],
      ),
    );
  }

  _securitySettingAccess() {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Container(
      margin: EdgeInsets.only(top: 15.5.h, bottom: 24.h),
      decoration: BoxDecoration(
        border: Border.all(color: customTheme.inputBgColor, width: 0.5),
      ),
      child: Column(
        children: [_line('password'.tr, value: '********', highlight: true)],
      ),
    );
  }

  _title(String title, {bool canEdit = false, VoidCallback? onTap}) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          spacing: 6.w,
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: customTheme.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (canEdit)
              Container(
                decoration: BoxDecoration(
                  color: customTheme.primaryColor,
                  borderRadius: BorderRadius.circular(3.r),
                ),
                constraints: BoxConstraints(minWidth: 62.w),
                height: 24.h,
                child: TextButton(
                  onPressed: onTap,
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    minimumSize: Size(0, 24.h),
                  ),
                  child: Text(
                    'edit'.tr,
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: canEdit ? 13.5.h : 15.5.h),
        Divider(height: 0.5),
      ],
    );
  }
}
