import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:provider/provider.dart';

class ThemePickModal {
  static void showModal(BuildContext context, GlobalKey menuKey) {
    final customeThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;
    final RenderBox button =
        menuKey.currentContext!.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final Offset position = button.localToGlobal(
      Offset.zero,
      ancestor: overlay,
    );
    final themeProvider = context.read<ThemeProvider>();
    bool isDark = themeProvider.currentThemeType == ThemeType.dark;
    final isLogin = context.read<AuthProvider>().isLogin;

    showMenu(
      context: context,
      menuPadding: EdgeInsets.zero,
      color: customTheme.inputBgColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(14.5.w),
      ),
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy + button.size.height,
        overlay.size.width - position.dx - button.size.width + 10.w,
        overlay.size.height - position.dy,
      ),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: Container(
            decoration: BoxDecoration(
              color: customTheme.inputBgColor,
              borderRadius: BorderRadius.circular(14.5.w),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16.w),
                Text(
                  'theme'.tr,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: customTheme.textColor,
                  ),
                ),
                SizedBox(height: 9.5.w),
                Container(
                  decoration: BoxDecoration(
                    color: customTheme.pageBgColor,
                    borderRadius: BorderRadius.circular(5.w),
                  ),
                  padding: EdgeInsets.all(2.w),
                  margin: EdgeInsets.only(bottom: 22.w),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            themeProvider.currentThemeType = ThemeType.dark;
                            Navigator.pop(context);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isDark
                                  ? customTheme.primaryColor
                                  : Colors.transparent,
                              borderRadius: BorderRadius.horizontal(
                                left: Radius.circular(3.w),
                              ),
                            ),
                            height: 25.w,
                            padding: EdgeInsets.symmetric(horizontal: 15.w),
                            alignment: Alignment.center,
                            child: Image.asset(
                              customeThemeImage.authNight,
                              width: 17.w,
                              height: 17.w,
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            themeProvider.currentThemeType = ThemeType.light;
                            Navigator.pop(context);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isDark
                                  ? Colors.transparent
                                  : customTheme.primaryColor,
                              borderRadius: BorderRadius.horizontal(
                                right: Radius.circular(3.w),
                              ),
                            ),
                            height: 25.w,
                            padding: EdgeInsets.symmetric(horizontal: 15.w),
                            alignment: Alignment.center,
                            child: Image.asset(
                              customeThemeImage.authSunny,
                              width: 17.w,
                              height: 17.w,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (isLogin)
                  Divider(color: customTheme.dividerColor, height: 0.5),
                if (isLogin)
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      context.read<AuthProvider>().logout();
                    },
                    child: Center(
                      child: Text(
                        'logout'.tr,
                        style: TextStyle(
                          color: customTheme.lightTextColor,
                          fontSize: 12.sp,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
