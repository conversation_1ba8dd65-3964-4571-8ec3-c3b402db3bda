import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/providers/recaptcha_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:provider/provider.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_action.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  _ResetPasswordState createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPasswordPage>
    with SingleTickerProviderStateMixin {
  String captchaStatus = "verify"; // verify, loading, success
  String captchaToken = "";
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;
  TextEditingController emailController = TextEditingController();
  bool showTip = false;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    emailController.dispose();
    super.dispose();
  }

  String getCaptchaStatusText() {
    switch (captchaStatus) {
      case "verify":
        return "verify".tr;
      case "loading":
        return "loading".tr;
      case "success":
        return "passed".tr;
      default:
        return "verify".tr;
    }
  }

  void verifyCaptcha() async {
    if (captchaStatus != "verify") return;
    setState(() {
      captchaStatus = "loading";
      captchaToken = "";
      _rotationController.repeat();
    });
    try {
      final recaptchaProvider = context.read<RecaptchaProvider>();
      final token = await recaptchaProvider.client?.execute(
        RecaptchaAction.custom("RESET_PASSWORD"),
      );
      setState(() {
        if (token != null) {
          captchaStatus = "success";
          captchaToken = token;
        } else {
          captchaStatus = "verify";
          captchaToken = "";
        }
        _rotationController.stop();
      });
    } catch (e) {
      setState(() {
        captchaStatus = "verify";
        captchaToken = "";
        _rotationController.stop();
      });
    }
  }

  void onSubmit() async {
    if (emailController.text.isEmpty) {
      ToastUtil.showToast('please_enter_your_email'.tr);
      return;
    }
    if (!emailController.text.isEmail) {
      ToastUtil.showToast('please_enter_a_valid_email'.tr);
      return;
    }
    if (captchaStatus == "success") {
      ToastUtil.showLoading();
      try {
        await CommonApi.sendEmailCode(
          email: emailController.text,
          bizType: SmsBizType.forgetPassword,
          googleToken: captchaToken,
        );
        setState(() {
          showTip = true;
        });
      } catch (e) {
      } finally {
        ToastUtil.dismiss();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Reset your password')),
      body: SingleChildScrollView(
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 27.h),
            Text(
              "enter_your_email_address_and_we_ll_send_you_a_password_reset_link"
                  .tr,
              style: TextStyle(
                fontSize: 15.sp,
                color: customTheme.subTextColor,
              ),
            ),
            SizedBox(height: 64.h),
            NormalInput(
              label: 'email'.tr,
              hintText: 'enter_your_email'.tr,
              controller: emailController,
            ),
            SizedBox(height: 20.5.h),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: verifyCaptcha,
              child: Container(
                height: 63.w,
                decoration: BoxDecoration(
                  color: customTheme.inputBgColor,
                  borderRadius: BorderRadius.circular(3.w),
                ),
                padding: EdgeInsets.only(left: 17.5.w, right: 20.5.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "i_m_not_a_robot".tr,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: customTheme.textColor,
                      ),
                    ),
                    Wrap(
                      spacing: 5.5.w,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        if (captchaStatus == "loading" ||
                            captchaStatus == 'success')
                          RotationTransition(
                            turns: captchaStatus == "loading"
                                ? _rotationAnimation
                                : const AlwaysStoppedAnimation(0.0),
                            child: Image.asset(
                              captchaStatus == "loading"
                                  ? customThemeImage.authLoading
                                  : customThemeImage.authVerification,
                              width: 20.w,
                            ),
                          ),
                        Text(
                          getCaptchaStatusText(),
                          style: TextStyle(
                            fontSize: 16.5.sp,
                            color: captchaStatus == "verify"
                                ? customTheme.primaryColor
                                : customTheme.textColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 100.h),
            PrimaryButton(title: 'submit'.tr, onPress: onSubmit),
            SizedBox(height: 21.h),
            if (showTip)
              Container(
                decoration: BoxDecoration(
                  color: customTheme.inputBgColor,
                  borderRadius: BorderRadius.circular(10.w),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 13.w,
                  vertical: 9.5.w,
                ),
                child: Row(
                  spacing: 6.5.w,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(customThemeImage.authSucTip, width: 18.w),
                    Expanded(
                      child: Text(
                        'a_password_reset_link_has_been_sent_to_your_email_please_check_your_inbox'
                            .tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: customTheme.textColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
