import 'package:aurenixai_app/api/user_api.dart';
import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/event_bus.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';

class BindGooglePage extends StatefulWidget {
  const BindGooglePage({super.key});

  @override
  _BindGoogleState createState() => _BindGoogleState();
}

class _BindGoogleState extends State<BindGooglePage> {
  bool isCopied = false;
  String digitCode = '';
  bool isError = false;
  String googleSecret = '';

  @override
  void initState() {
    super.initState();
    getInit();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> getInit() async {
    try {
      final res = await UserApi.getGoogleSecret();
      setState(() {
        googleSecret = res;
      });
    } catch (e) {}
  }

  Future<void> onSubmit() async {
    if (digitCode.isEmpty) {
      setState(() {
        isError = true;
      });
      return;
    } else {
      setState(() {
        isError = false;
      });
    }
    try {
      ToastUtil.showLoading();
      await UserApi.bindGoogleSecret(digitCode, googleSecret);
      ToastUtil.dismiss();
      EventBusUtil.fireBindGoogleSuccess(BindGoogleSuccessEvent());
      Navigator.of(context).popUntil((route) {
        return !route.willHandlePopInternally &&
            route is ModalRoute &&
            (route.settings.name != Routes.google &&
                route.settings.name != Routes.bindGoogle);
      });
    } catch (e) {
      ToastUtil.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('google_authenticator_h'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(left: 15.w, top: 21.h, bottom: 21.h),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 8.w,
                    children: [
                      _step('1'),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: 15.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _title(
                                'download_and_install_google_authenticator'.tr,
                              ),
                              SizedBox(height: 7.5.h),
                              _content(
                                'download_and_install_google_authenticator_description'
                                    .tr,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  // 实现一个横线，虚线分割线
                  SizedBox(height: 23.5.h),
                  _divider(),
                  SizedBox(height: 28.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 9.w,
                    children: [
                      _step('2'),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: 15.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _title(
                                'add_your_account_in_google_authenticator'.tr,
                              ),
                              SizedBox(height: 7.5.h),
                              _content(
                                "(1) ${'add_your_account_in_google_authenticator_description'.tr}",
                              ),
                              SizedBox(height: 7.5.h),
                              _content(
                                "(2) ${'add_your_account_in_google_authenticator_description_2'.tr}",
                              ),
                              SizedBox(height: 7.5.h),
                              _content(
                                "(3) ${'add_your_account_in_google_authenticator_description_3'.tr}",
                              ),
                              SizedBox(height: 7.5.h),
                              _content(
                                "(4) ${'add_your_account_in_google_authenticator_description_4'.tr}",
                              ),
                              _copyKey(),
                              SizedBox(height: 13.h),
                              _qrCode(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 22.h),
                  _divider(),
                  SizedBox(height: 28.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 9.w,
                    children: [
                      _step('3'),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: 15.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _title('get_your_6_digit_google_code'.tr),
                              SizedBox(height: 7.5.h),
                              _content(
                                "get_your_6_digit_google_code_description".tr,
                              ),
                              _pasteCode(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 22.h),
                  _divider(),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'enable_now'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }

  _step(String index) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Container(
      decoration: BoxDecoration(
        color: customTheme.primaryColor,
        borderRadius: BorderRadius.circular(3.w),
      ),
      width: 21.w,
      height: 21.w,
      alignment: Alignment.center,
      child: Text(
        index,
        style: TextStyle(
          fontSize: 14.sp,
          color: customTheme.textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  _title(String title) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Text(
      title,
      style: TextStyle(
        fontSize: 15.sp,
        color: customTheme.textColor,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  _content(String content) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Text(
      content,
      style: TextStyle(fontSize: 13.sp, color: customTheme.subTextColor),
    );
  }

  _divider() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    return SizedBox(
      height: 0.5,
      width: double.infinity,
      child: CustomPaint(
        painter: HorizontalDashedLinePainter(
          color: customTheme.dividerColor,
          strokeWidth: 0.5,
          dashWidth: 5,
          dashSpace: 5,
        ),
      ),
    );
  }

  _copyKey() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return Container(
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.5.h),
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(3.w),
      ),
      child: Row(
        spacing: 8.w,
        children: [
          Expanded(
            child: Text(
              googleSecret,
              style: TextStyle(
                fontSize: 15.sp,
                color: customTheme.textColor,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: customTheme.primaryColor,
              borderRadius: BorderRadius.circular(3.r),
            ),
            constraints: BoxConstraints(minWidth: 62.w),
            height: 24.h,
            child: TextButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: googleSecret)).then((
                  value,
                ) {
                  setState(() {
                    isCopied = true;
                  });
                  Future.delayed(Duration(seconds: 2), () {
                    isCopied = false;
                    if (mounted) {
                      setState(() {});
                    }
                  });
                });
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                minimumSize: Size(0, 24.h),
              ),
              child: isCopied
                  ? Image.asset(
                      customThemeImage.authVerification,
                      width: 18.w,
                      height: 18.w,
                      fit: BoxFit.contain,
                    )
                  : Text(
                      'copy_key'.tr,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  _pasteCode() {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 7.h,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.5.h),
            decoration: BoxDecoration(
              color: customTheme.inputBgColor,
              borderRadius: BorderRadius.circular(3.w),
              border: Border.all(
                color: isError ? customTheme.downColor : Colors.transparent,
              ),
            ),
            child: Row(
              spacing: 8.w,
              children: [
                Expanded(
                  child: Text(
                    digitCode,
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: customTheme.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: customTheme.primaryColor,
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                  constraints: BoxConstraints(minWidth: 62.w),
                  height: 24.h,
                  child: TextButton(
                    onPressed: () {
                      Clipboard.getData(Clipboard.kTextPlain).then((value) {
                        if (value != null) {
                          setState(() {
                            digitCode = value.text ?? '';
                            isError = false;
                          });
                        }
                      });
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      minimumSize: Size(0, 24.h),
                    ),
                    child: Text(
                      'paste'.tr,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: customTheme.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (isError)
            Text(
              'please_enter_a_valid_verification_code'.tr,
              style: TextStyle(fontSize: 12.sp, color: customTheme.downColor),
            ),
        ],
      ),
    );
  }

  _qrCode() {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: 100.w,
          height: 100.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(7.w),
            gradient: LinearGradient(
              colors: [customTheme.ff40e2fe, customTheme.primaryColor],
              begin: Alignment.bottomRight,
              end: Alignment.topLeft,
            ),
            boxShadow: [
              BoxShadow(
                color: customTheme.primaryColor,
                blurRadius: 2.5.w,
                offset: Offset(0, 0),
                blurStyle: BlurStyle.outer,
              ),
            ],
          ),
        ),
        Container(
          width: 100.w - 2,
          height: 100.w - 2,
          decoration: BoxDecoration(
            color: customTheme.inputBgColor,
            borderRadius: BorderRadius.circular(7.w),
          ),
          padding: EdgeInsets.all(10.w),
          child: QrImageView(
            data: 'https://www.google.com',
            version: QrVersions.auto,
            size: 80.w,
            padding: EdgeInsets.all(5.w),
            backgroundColor: customTheme.textColor,
          ),
        ),
      ],
    );
  }
}

// 横线虚线绘制器
class HorizontalDashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  HorizontalDashedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 5.0,
    this.dashSpace = 5.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final double dashLength = dashWidth;
    final double spaceLength = dashSpace;
    final double totalLength = dashLength + spaceLength;

    double currentX = 0;
    while (currentX < size.width) {
      final double endX = (currentX + dashLength).clamp(0.0, size.width);
      canvas.drawLine(Offset(currentX, 0), Offset(endX, 0), paint);
      currentX += totalLength;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
