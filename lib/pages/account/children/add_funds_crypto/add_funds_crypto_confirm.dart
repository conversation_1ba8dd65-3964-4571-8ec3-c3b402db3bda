import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AddFundsCryptoConfirmPage extends StatefulWidget {
  const AddFundsCryptoConfirmPage({super.key});

  @override
  _AddFundsCryptoConfirmPageState createState() =>
      _AddFundsCryptoConfirmPageState();
}

class _AddFundsCryptoConfirmPageState extends State<AddFundsCryptoConfirmPage> {
  bool isAgree = false;

  void onConfirm() async {
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return Scaffold(
      appBar: AppBar(title: Text('Confirm_Order'.tr)),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                children: [
                  SizedBox(height: 25.w),
                  Image.asset(
                    customThemeImage.accountRechargeUsdt,
                    width: 60.w,
                    height: 60.w,
                    fit: BoxFit.fill,
                  ),
                  SizedBox(height: 18.w),
                  Text(
                    'Buy_crypto_amount'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: customTheme.lightTextColor,
                    ),
                  ),
                  SizedBox(height: 12.w),
                  Text(
                    'XXXX USDT',
                    style: TextStyle(
                      fontSize: 23.w,
                      fontWeight: FontWeight.w600,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 10.w),
                  Text(
                    'You will pay xxx USD',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: customTheme.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 25.h),
                  Container(
                    decoration: BoxDecoration(
                      color: customTheme.ff1f1f2a,
                      borderRadius: BorderRadius.circular(7.w),
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: 6.w,
                      horizontal: 13.w,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        buildItem(
                          label: 'Currency'.tr,
                          value: 'USDT',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Price'.tr,
                          value: '1 USDT ≈ 1 USD',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Fees'.tr,
                          value: '1 USD',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Date'.tr,
                          value: '10/5/2025 17:45',
                          customTheme: customTheme,
                        ),
                        buildItem(
                          label: 'Pay_with'.tr,
                          customTheme: customTheme,
                          valueWidget: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            spacing: 10.w,
                            children: [
                              Image.asset(
                                customThemeImage.accountCurrencyUsdt,
                                width: 21.w,
                                height: 21.w,
                                fit: BoxFit.fill,
                              ),
                              Text(
                                '****1234',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w600,
                                  color: customTheme.textColor,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 17.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            isAgree = !isAgree;
                          });
                        },
                        child: Container(
                          margin: EdgeInsets.only(top: 10.w),
                          child: Image.asset(
                            isAgree
                                ? customThemeImage.authChoose
                                : customThemeImage.authUnchoose,
                            width: 18.w,
                          ),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: RichText(
                          text: TextSpan(
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: customTheme.textColor,
                              height: 1.5,
                            ),
                            children: [
                              TextSpan(text: 'I_have_read_and_agree_to_the'.tr),
                              TextSpan(
                                text: 'Terms of Use'.tr,
                                style: TextStyle(
                                  color: customTheme.primaryColor,
                                  decoration: TextDecoration.underline,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 点击 "Terms of Use" 的事件
                                  },
                              ),
                              TextSpan(text: ' ${'and'.tr} '),
                              TextSpan(
                                text: 'privacy_policy'.tr,
                                style: TextStyle(
                                  color: customTheme.primaryColor,
                                  decoration: TextDecoration.underline,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 点击 "Privacy Policy" 的事件
                                  },
                              ),
                              TextSpan(
                                text:
                                    ' ${'add_funds_crypto_agree_content_last'.tr}',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'confirm'.tr,
            onSubmit: onConfirm,
          ),
        ],
      ),
    );
  }

  Widget buildItem({
    required String label,
    String? value,
    Widget? valueWidget,
    required CustomThemeSwatch customTheme,
  }) {
    return Container(
      padding: EdgeInsets.only(top: 11.w, bottom: 11.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: customTheme.hintTextColor,
            ),
          ),
          SizedBox(width: 40.w),
          if (value != null)
            Expanded(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: customTheme.textColor,
                ),
                textAlign: TextAlign.right,
              ),
            ),
          if (valueWidget != null) Expanded(child: valueWidget),
        ],
      ),
    );
  }
}
