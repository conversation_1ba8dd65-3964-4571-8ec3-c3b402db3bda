import 'package:aurenixai_app/api/common_api.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class NotificationDetailPage extends StatefulWidget {
  const NotificationDetailPage({super.key});

  @override
  _NotificationDetailState createState() => _NotificationDetailState();
}

class _NotificationDetailState extends State<NotificationDetailPage> {
  DateTime? date;
  String title = '';
  String content = '';

  @override
  void initState() {
    super.initState();
    final args = Get.parameters;
    title = args['title'] ?? '';
    if (args['date'] != null) {
      final dateTime = int.tryParse(args['date']!);
      date = dateTime != null
          ? DateTime.fromMillisecondsSinceEpoch(dateTime)
          : null;
    }
    String id = args['id'] ?? '';
    if (id.isNotEmpty) {
      getDetail(id);
    }
  }

  Future<void> getDetail(String id) async {
    final res = await CommonApi.getNoticeDetail(id);
    setState(() {
      date = DateTime.fromMillisecondsSinceEpoch(res.updateDatetime as int);
      title = res.title;
      content = res.content;
    });
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Scaffold(
      appBar: AppBar(title: Text('notification'.tr)),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: customTheme.textColor,
              ),
            ),
            SizedBox(height: 10.5.w),
            Text(
              date?.yyyyMMddHHmmEEE ?? '',
              style: TextStyle(
                fontSize: 13.sp,
                color: customTheme.subTextColor,
              ),
            ),
            SizedBox(height: 22.w),
            Divider(color: customTheme.dividerColor, height: 0.5),
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(vertical: 22.5.w),
                child: Text(
                  content,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: customTheme.textColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
