import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebviewPage extends StatefulWidget {
  final String? title;
  final String url;

  const WebviewPage({super.key, this.title, required this.url});

  @override
  _WebviewPageState createState() => _WebviewPageState();
}

class _WebviewPageState extends State<WebviewPage> {
  WebViewController? controller;

  @override
  void initState() {
    super.initState();
    final customTheme = context.read<ThemeProvider>().customTheme;

    if (!kIsWeb) {
      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(customTheme.pageBgColor)
        ..loadRequest(Uri.parse(widget.url));
    }
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;
    final textDirection = Directionality.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? ''),
        backgroundColor: customTheme.pageBgColor,
        foregroundColor: customTheme.textColor,
      ),
      backgroundColor: customTheme.pageBgColor,
      body: WebViewWidget(
        controller: controller!,
        layoutDirection: textDirection,
      ),
    );
  }
}
