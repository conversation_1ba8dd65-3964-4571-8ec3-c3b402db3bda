import 'package:json_annotation/json_annotation.dart';

part 'fund_investment_record_user_level_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class FundInvestmentRecordUserLevelRes {
  FundInvestmentRecordUserLevelRes();

  late String id;
  late String productName;
  late num totalInvested;
  late num createDatetime;

  factory FundInvestmentRecordUserLevelRes.fromJson(
    Map<String, dynamic> json,
  ) => _$FundInvestmentRecordUserLevelResFromJson(json);
  Map<String, dynamic> toJson() =>
      _$FundInvestmentRecordUserLevelResToJson(this);
}
