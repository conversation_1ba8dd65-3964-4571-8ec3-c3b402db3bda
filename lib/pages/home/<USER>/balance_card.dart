import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:aurenixai_app/widgets/kyc_failed_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class BalanceCard extends StatefulWidget {
  const BalanceCard({super.key});

  @override
  _BalanceCardState createState() => _BalanceCardState();
}

class _BalanceCardState extends State<BalanceCard> {
  bool isBalanceVisible = false;
  String balance = '76149.1362';

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;
    final user = context.watch<AuthProvider>().user;
    final identifyStatus = user?.identifyStatus;
    final failRemark = user?.failRemark;
    final isKyc = identifyStatus == '1' || identifyStatus == '2';

    return Container(
      height: 124.5.w,
      padding: EdgeInsets.all(17.w),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(customThemeImage.homeBalanceBg),
          fit: BoxFit.fill,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: 4.w,
        children: [
          Expanded(
            child: Column(
              spacing: 12.w,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  spacing: 5.w,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 4.5.w),
                      child: Text(
                        'available_balance'.tr,
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: customTheme.textColor,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          isBalanceVisible = !isBalanceVisible;
                        });
                      },
                      child: Icon(
                        isBalanceVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                        size: 18.w,
                        color: customTheme.textColor,
                      ),
                    ),
                  ],
                ),
                Text(
                  isBalanceVisible
                      ? '\$ ${(user?.availableAmount ?? '0.00').fmt}'
                      : '\$**.**',
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (!isKyc)
                GestureDetector(
                  onTap: () {
                    if (identifyStatus == '0') {
                      if (failRemark?.isNotEmpty ?? false) {
                        KycFailedModal.showModal(context: context);
                      } else {
                        Get.toNamed(Routes.kyc);
                      }
                    } else if (identifyStatus == '4') {
                      Get.toNamed(Routes.kyc);
                    }
                  },
                  child: Container(
                    height: 31.w,
                    padding: EdgeInsets.symmetric(horizontal: 5.5.w),
                    decoration: BoxDecoration(
                      color: customTheme.inputBgColor,
                      borderRadius: BorderRadius.circular(10.w),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.81),
                          blurRadius: 8.w,
                          offset: Offset(0, 0),
                          blurStyle: BlurStyle.outer,
                        ),
                      ],
                    ),
                    alignment: Alignment.center,
                    child: Wrap(
                      spacing: 3.w,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Image.asset(
                          identifyStatus == '4'
                              ? customThemeImage.authLoading
                              : failRemark?.isNotEmpty ?? false
                              ? customThemeImage.homeFail
                              : customThemeImage.homeWarn,
                          width: 18.w,
                          height: 18.w,
                        ),
                        Text(
                          identifyStatus == '4'
                              ? 'kyc_in_progress'.tr
                              : failRemark?.isNotEmpty ?? false
                              ? 'kyc_fail'.tr
                              : 'kyc'.tr,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: customTheme.textColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                SizedBox.shrink(),
              TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 9.5.w),
                  backgroundColor: customTheme.textColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(3.w),
                  ),
                ),
                child: Text(
                  'Add_Funds'.tr,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: customTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
