import 'package:aurenixai_app/models/fund_product_total_res.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'pie_chart/pie_chart.dart';

class PieCard extends StatelessWidget {
  const PieCard({super.key, this.totalRes});

  final FundProductTotalRes? totalRes;

  @override
  Widget build(BuildContext context) {
    return totalRes != null
        ? Column(
            children: [
              SizedBox(height: 34.h),
              _pie(context),
              SizedBox(height: 34.h),
              _amountBox(context),
            ],
          )
        : SizedBox.shrink();
  }

  Widget _pie(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    final dataMap = <String, double>{
      "Cash": totalRes!.cash.toDouble(),
      "Investment": (totalRes!.investment ?? 0).toDouble(),
      "Commission": (totalRes!.commission ?? 0).toDouble(),
    };
    final legendColorMap = <String, Color>{
      "Cash": customTheme.fffce412,
      "Investment": customTheme.primaryColor,
      "Commission": customTheme.upColor,
    };
    final colorList = dataMap.keys
        .map((e) => legendColorMap[e]!)
        .toList(growable: false);

    return PieChart(
      dataMap: dataMap,
      animationDuration: const Duration(milliseconds: 800),
      chartRadius: 140.w,
      colorList: colorList,
      chartType: ChartType.ring,
      centerWidget: SizedBox(
        width: 82.w,
        child: Text(
          "wealth_allocation_composition".tr,
          style: TextStyle(
            fontSize: 13.sp,
            color: customTheme.textColor,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
      legendOptions: LegendOptions(showLegends: false),
      chartValuesOptions: ChartValuesOptions(
        showChartValueBackground: false,
        showChartValues: true,
        showChartValuesInPercentage: true,
        showChartValuesOutside: true,
        chartValueRadius: 30.w,
        chartValueStyle: TextStyle(
          fontSize: 15.sp,
          color: customTheme.textColor,
        ),
      ),
      ringStrokeWidth: 8,
      emptyColor: customTheme.inputBgColor,
      baseChartColor: Colors.transparent,
    );
  }

  Widget _amountItem(
    BuildContext context, {
    required String title,
    required String amount,
    required Color color,
  }) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    return Expanded(
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 12.sp, color: customTheme.subTextColor),
          ),
          SizedBox(height: 5.h),
          Text(
            '\$${amount.fmt}',
            style: TextStyle(
              fontSize: 15.sp,
              color: customTheme.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Container(
            width: 27.w,
            height: 2.h,
            margin: EdgeInsets.only(top: 18.h),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(1.r),
            ),
          ),
        ],
      ),
    );
  }

  Widget _amountBox(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    final nodeLevel = context.watch<AuthProvider>().user?.nodeLevel ?? 1;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w).copyWith(top: 10.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.transparent, customTheme.inputBgColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(15.r)),
      ),
      child: Row(
        spacing: 5.w,
        children: [
          _amountItem(
            context,
            title: 'cash'.tr,
            amount: totalRes!.cash.toString(),
            color: customTheme.fffce412,
          ),
          _amountItem(
            context,
            title: 'investment'.tr,
            amount: (totalRes!.investment ?? 0).toString(),
            color: customTheme.primaryColor,
          ),
          if (nodeLevel > 1)
            _amountItem(
              context,
              title: 'commission'.tr,
              amount: (totalRes!.commission ?? 0).toString(),
              color: customTheme.upColor,
            ),
        ],
      ),
    );
  }
}
