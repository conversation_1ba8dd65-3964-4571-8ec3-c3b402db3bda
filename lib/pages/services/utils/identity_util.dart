import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:flutter/material.dart'
    show BuildContext, Color, Gradient, LinearGradient, Alignment;
import 'package:provider/provider.dart';

class IdentityUtil {
  static Color? getIdentityColor(String identity, BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    switch (identity.toUpperCase()) {
      case 'AP':
        return customTheme.proColor;
      case 'CP':
        return customTheme.preferredColor;
      case 'SL':
        return customTheme.eliteColor;
      case 'RD':
        return customTheme.premierColor;
      case 'ED':
        return customTheme.signatureColor;
      case 'GD':
        return customTheme.platinumColor;
      default:
        return null;
    }
  }

  static Gradient? getIdentityGradient(String identity, BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    switch (identity.toUpperCase()) {
      case 'AP':
        return LinearGradient(
          colors: [customTheme.proStartColor, customTheme.proEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'CP':
        return LinearGradient(
          colors: [
            customTheme.preferredStartColor,
            customTheme.preferredEndColor,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'SL':
        return LinearGradient(
          colors: [customTheme.eliteStartColor, customTheme.eliteEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'RD':
        return LinearGradient(
          colors: [customTheme.premierStartColor, customTheme.premierEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'ED':
        return LinearGradient(
          colors: [
            customTheme.signatureStartColor,
            customTheme.signatureEndColor,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'GD':
        return LinearGradient(
          colors: [
            customTheme.platinumStartColor,
            customTheme.platinumMidColor,
            customTheme.platinumEndColor,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.3, 1.0],
        );
      default:
        return null;
    }
  }

  static Color getLevelColor(String level, BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    switch (level.toLowerCase()) {
      case 'pro':
        return customTheme.proColor;
      case 'preferred':
        return customTheme.preferredColor;
      case 'elite':
        return customTheme.eliteColor;
      case 'premier':
        return customTheme.premierColor;
      case 'signature':
        return customTheme.signatureColor;
      case 'platinum':
        return customTheme.platinumColor;
      default:
        return customTheme.proColor;
    }
  }

  static Gradient getLevelGradient(String level, BuildContext context) {
    final customTheme = Provider.of<ThemeProvider>(context).customTheme;

    switch (level.toLowerCase()) {
      case 'pro':
        return LinearGradient(
          colors: [customTheme.proStartColor, customTheme.proEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'preferred':
        return LinearGradient(
          colors: [
            customTheme.preferredStartColor,
            customTheme.preferredEndColor,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'elite':
        return LinearGradient(
          colors: [customTheme.eliteStartColor, customTheme.eliteEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'premier':
        return LinearGradient(
          colors: [customTheme.premierStartColor, customTheme.premierEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'signature':
        return LinearGradient(
          colors: [
            customTheme.signatureStartColor,
            customTheme.signatureEndColor,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
      case 'platinum':
        return LinearGradient(
          colors: [
            customTheme.platinumStartColor,
            customTheme.platinumMidColor,
            customTheme.platinumEndColor,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.3, 1.0],
        );
      default:
        return LinearGradient(
          colors: [customTheme.proStartColor, customTheme.proEndColor],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        );
    }
  }
}
