import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class DatePickModal extends StatefulWidget {
  final DateTime? initialDate;
  const DatePickModal({super.key, this.initialDate});

  static Future<DateTime?> showModal(
    BuildContext context, {
    DateTime? initialDate,
  }) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<DateTime?>(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          child: DatePickModal(initialDate: initialDate),
        );
      },
    );
  }

  @override
  State<DatePickModal> createState() => _DatePickModalState();
}

class _DatePickModalState extends State<DatePickModal> {
  late List<DateTime> dateList;
  final ScrollController scrollController = ScrollController();
  int datePickIndex = 0;

  @override
  void initState() {
    super.initState();
    // 帮我生成日期列表，从当前月往前推12个月
    dateList = List.generate(12, (i) {
      final now = DateTime.now();
      final year = now.year;
      final month = now.month - i;
      final d = DateTime(year, month, 1);
      return DateTime(d.year, d.month, 1);
    });

    if (widget.initialDate != null) {
      datePickIndex = dateList.indexOf(widget.initialDate!);
    }

    // 滚动到初始位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollController.jumpTo(datePickIndex * (23.w + 20.w));
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'filter_date'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;

    return SizedBox(
      height: 370.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.w),
              child: Column(
                children: List<Widget>.generate(dateList.length, (int index) {
                  return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      setState(() {
                        datePickIndex = index;
                      });
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 11.5.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            dateList[index].yMMMM,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: datePickIndex == index
                                  ? customTheme.primaryColor
                                  : customTheme.textColor,
                              fontWeight: datePickIndex == index
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                            ),
                          ),
                          if (datePickIndex == index)
                            Image.asset(
                              customThemeImage.authSelect,
                              width: 14.w,
                            ),
                        ],
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'Submit',
            height: 45.w,
            onSubmit: () {
              Get.back(result: dateList[datePickIndex]);
            },
          ),
        ],
      ),
    );
  }
}
