import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SendingModal extends StatefulWidget {
  const SendingModal({super.key});

  static Future<void> showModal(BuildContext context) async {
    final customTheme = context.read<ThemeProvider>().customTheme;
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          bottom: false,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SendingModal(),
          ),
        );
      },
    );
  }

  @override
  State<SendingModal> createState() => _SendingModalState();
}

class _SendingModalState extends State<SendingModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget buildItem({
    required CustomThemeSwatch customTheme,
    required String imageUrl,
    required String label,
    required String describe,
    required void Function() onTap,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.fromLTRB(17.w, 20.w, 17.w, 20.w),
        decoration: BoxDecoration(
          color: customTheme.inputBgColor,
          borderRadius: BorderRadius.circular(3.w),
        ),
        child: Row(
          children: [
            Image.asset(imageUrl, width: 45.w, height: 45.w, fit: BoxFit.fill),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 13.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.textColor,
                    ),
                  ),
                  SizedBox(height: 3.w),
                  Text(
                    describe,
                    style: TextStyle(
                      fontSize: 11.w,
                      fontWeight: FontWeight.w400,
                      color: customTheme.hintTextColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'select_sending_method'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.watch<ThemeProvider>().customThemeImage;

    return SizedBox(
      height: 300.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 28.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  buildItem(
                    customTheme: customTheme,
                    imageUrl: customThemeImage.accountTransferUsdtddz,
                    label: 'on_chain_transfer'.tr,
                    describe: 'Use_blockchain_networks_transfer'.tr,
                    onTap: () {
                      Get.toNamed(Routes.sendChain);
                    },
                  ),
                  SizedBox(height: 10.w),
                  buildItem(
                    customTheme: customTheme,
                    imageUrl: customThemeImage.accountTransferEmail,
                    label: 'Transfer_to_AURENIX_AI_users'.tr,
                    describe: 'Transfer_funds_by_using_UID_and_Email'.tr,
                    onTap: () {
                      Get.toNamed(Routes.sendUidEmail);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
