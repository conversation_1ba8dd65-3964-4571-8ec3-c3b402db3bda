import 'dart:async';

import 'package:aurenixai_app/models/check_google_secret_res.dart';
import 'package:aurenixai_app/models/kyc_record_res.dart';
import 'package:aurenixai_app/models/login_res.dart';
import 'package:aurenixai_app/models/page_info.dart';
import 'package:aurenixai_app/models/question_res.dart';
import 'package:aurenixai_app/models/user.dart';
import 'package:aurenixai_app/models/user_info.dart';
import 'package:aurenixai_app/models/user_node_level_list_res.dart';
import 'package:aurenixai_app/models/user_node_level_res.dart';
import 'package:aurenixai_app/utils/http_util.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:get/get_utils/get_utils.dart';

/// 用户相关接口
class UserApi {
  /// 用户登录
  static Future<LoginRes> login({
    required String loginName,
    required String loginPwd,
  }) async {
    try {
      final res = await HttpUtil.post('/core/v1/cuser/public/login', {
        'loginName': loginName,
        'loginPwd': loginPwd,
      });
      return LoginRes.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 用户邮箱注册
  static Future<LoginRes> registerEmail({
    required String email,
    required String smsCode,
    required String loginPwd,
    required String countryCode,
    String? inviteCode,
  }) async {
    try {
      final res = await HttpUtil.post('/core/v1/cuser/public/email_register', {
        'email': email,
        'smsCode': smsCode,
        'loginPwd': loginPwd,
        'inviteCode': inviteCode,
        'countryCode': countryCode,
      });
      return LoginRes.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取用户详情
  static Future<User> getUserInfo() async {
    try {
      final res = await HttpUtil.post("/core/v1/cuser/my");
      return User.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取用户信息页详情
  static Future<UserInfo> getPersonalInfo() async {
    try {
      final res = await HttpUtil.post("/core/v1/cuser/info");
      return UserInfo.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 修改用户信息
  static Future<void> modifyUser(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/user/edit_profile", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 找回密码
  static Future<void> forgetLoginPwd({
    required String loginName,
    required String smsCaptcha,
    required String loginPwd,
  }) async {
    try {
      await HttpUtil.post("/core/v1/user/public/reset_loginPwd", {
        "loginName": loginName,
        "smsCaptcha": smsCaptcha,
        "loginPwd": loginPwd,
        "userKind": "C",
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 修改密码
  static Future<void> editLoginPwd({
    required String oldLoginPwd,
    required String newLoginPwd,
    required String smsCaptcha,
  }) async {
    try {
      await HttpUtil.post("/core/v1/user/modify_pwd", {
        "oldLoginPwd": oldLoginPwd,
        "newLoginPwd": newLoginPwd,
        "smsCaptcha": smsCaptcha,
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 绑定支付密码
  static Future<void> bindTradePwd({
    required String smsCaptcha,
    required String tradePwd,
  }) async {
    try {
      await HttpUtil.post("/core/v1/user/bind_tradePwd", {
        "smsCaptcha": smsCaptcha,
        "tradePwd": tradePwd,
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  static Future<void> modifyTradePwd({
    required String smsCaptcha,
    required String tradePwd,
  }) async {
    try {
      await HttpUtil.post("/core/v1/user/modify_tradePwd", {
        "smsCaptcha": smsCaptcha,
        "newTradePwd": tradePwd,
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  // 修改手机号
  static Future<void> modifyMobile({
    required String newMobile,
    required String countryId,
  }) async {
    try {
      await HttpUtil.post("/core/v1/user/modify_mobile", {
        "newMobile": newMobile,
        "countryId": countryId,
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 实名认证
  static Future<void> createIdentifyOrder(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/identify_order/create", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 获取google secret
  static Future<String> getGoogleSecret() async {
    try {
      final res = await HttpUtil.post('/core/v1/cuser/get_google_secret');
      return res["googleSecret"];
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 绑定googleSecret
  static Future<void> bindGoogleSecret(
    String googleCaptcha,
    String secret,
  ) async {
    try {
      await HttpUtil.post("/core/v1/cuser/bind_google_secret", {
        "googleCaptcha": googleCaptcha,
        "secret": secret,
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 关闭googleSecret
  static Future<void> closeGoogleSecret(
    String googleCaptcha,
    String smsCaptcha,
  ) async {
    try {
      await HttpUtil.post("/core/v1/user/close_google_secret", {
        "googleCaptcha": googleCaptcha,
        "smsCaptcha": smsCaptcha,
      });
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  static Future<List<QuestionRes>> getKycQuestions() async {
    final res = await HttpUtil.post('/core/v1/kyc_questions/list_front');
    return (res as List<dynamic>)
        .map((e) => QuestionRes.fromJson(CommonUtils.removeNullKeys(e)))
        .toList();
  }

  /// 申请kyc
  static Future<void> applyKyc(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/kyc_record/create_front", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  static Future<KycRecordRes?> getLastKycRecord() async {
    try {
      final res = await HttpUtil.post("/core/v1/kyc_record/recent");
      if (res != null) {
        return KycRecordRes.fromJson(CommonUtils.removeNullKeys(res));
      }
      return null;
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  // 分页条件查询节点身份
  static Future<PageInfo<UserNodeLevelRes>> getUserNodeLevelList(
    Map<String, dynamic> data,
  ) async {
    final res = await HttpUtil.post(
      '/core/v1/user_node_level/page_front',
      data,
    );
    return PageInfo.fromJson<UserNodeLevelRes>(res, UserNodeLevelRes.fromJson);
  }

  // 列表查询极差列表
  static Future<List<UserNodeLevelListRes>>
  getEarningUserNodeLevelList() async {
    final res = await HttpUtil.post('/core/v1/user_node_level/list_front');
    return (res as List<dynamic>)
        .map(
          (e) => UserNodeLevelListRes.fromJson(CommonUtils.removeNullKeys(e)),
        )
        .toList();
  }

  /// 申请升级身份
  static Future<void> applyUpgrade(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/user_node_apply/create", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 查询
  static Future<CheckGoogleSecretRes> userCheckGoogleSecret() async {
    try {
      final res = await HttpUtil.post("/core/v1/cuser/check_google_secret");
      return CheckGoogleSecretRes.fromJson(CommonUtils.removeNullKeys(res));
    } catch (e) {
      e.printError();
      rethrow;
    }
  }

  /// 重置googleSecret
  static Future<void> userResetGoogleSecret(Map<String, dynamic> data) async {
    try {
      await HttpUtil.post("/core/v1/cuser/reset_google_secret", data);
    } catch (e) {
      e.printError();
      rethrow;
    }
  }
}
