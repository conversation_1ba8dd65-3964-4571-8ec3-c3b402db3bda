// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User()
  ..id = json['id'] as String
  ..userId = json['userId'] as String
  ..photo = json['photo'] as String
  ..availableAmount = json['availableAmount'] as String
  ..identifyStatus = json['identifyStatus'] as String
  ..failRemark = json['failRemark'] as String?
  ..status = json['status'] as String
  ..registerDatetime = json['registerDatetime'] as num
  ..nickname = json['nickname'] as String
  ..inviteCode = json['inviteCode'] as String
  ..mobile = json['mobile'] as String?
  ..email = json['email'] as String
  ..levelName = json['levelName'] as String
  ..levelPic = json['levelPic'] as String
  ..level = json['level'] as num
  ..nodeType = json['nodeType'] as String
  ..nodeLevel = json['nodeLevel'] as num
  ..nodeLevelName = json['nodeLevelName'] as String
  ..nodePic = json['nodePic'] as String?
  ..interCode = json['interCode'] as String;

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  "id": instance.id,
  "userId": instance.userId,
  "photo": instance.photo,
  "availableAmount": instance.availableAmount,
  "identifyStatus": instance.identifyStatus,
  "failRemark": instance.failRemark,
  "status": instance.status,
  "registerDatetime": instance.registerDatetime,
  "nickname": instance.nickname,
  "inviteCode": instance.inviteCode,
  "mobile": instance.mobile,
  "email": instance.email,
  "levelName": instance.levelName,
  "levelPic": instance.levelPic,
  "level": instance.level,
  "nodeType": instance.nodeType,
  "nodeLevel": instance.nodeLevel,
  "nodeLevelName": instance.nodeLevelName,
  "nodePic": instance.nodePic,
  "interCode": instance.interCode,
};
