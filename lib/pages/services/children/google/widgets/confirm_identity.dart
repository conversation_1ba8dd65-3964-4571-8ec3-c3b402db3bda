import 'package:aurenixai_app/pages/services/children/google/widgets/unbind_google_verification.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/widget_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class ConfirmIdentityModal extends StatefulWidget {
  const ConfirmIdentityModal({super.key});

  static Future<void> showModal(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.w)),
      ),
      backgroundColor: customTheme.pageBgColor,
      builder: (BuildContext context) {
        return SafeArea(
          top: false,
          bottom: false,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: ConfirmIdentityModal(),
          ),
        );
      },
    );
  }

  @override
  State<ConfirmIdentityModal> createState() => _ConfirmIdentityModalState();
}

class _ConfirmIdentityModalState extends State<ConfirmIdentityModal> {
  final TextEditingController passwordController = TextEditingController();
  bool isPasswordError = false;

  @override
  void dispose() {
    passwordController.dispose();
    super.dispose();
  }

  void onSubmit() {
    String password = passwordController.text;

    if (password.isEmpty) {
      setState(() {
        isPasswordError = true;
      });
      return;
    }

    Get.back();
    UnbindGoogleVerification.showModal(context);
  }

  Widget buildTopBar(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;

    return Padding(
      padding: EdgeInsets.only(left: 15.w),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 18.w, bottom: 21.w, right: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'confirm_your_identity'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: customTheme.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.close_rounded,
                    size: 26.w,
                    color: customTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Divider(height: 0.5, color: customTheme.dividerColor),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final titleStyle = TextStyle(
      fontSize: 14.sp,
      color: customTheme.subTextColor,
    );

    return SizedBox(
      height: 400.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildTopBar(context),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 28.h),
              child: Column(
                children: [
                  SizedBox(height: 3.h),
                  Image.asset(
                    customThemeImage.googleSecurity,
                    width: 50.w,
                    height: 50.w,
                    fit: BoxFit.fill,
                  ),
                  SizedBox(height: 8.5.h),
                  Text(
                    'confirm_your_identity_description'.tr,
                    style: titleStyle,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.5.h),
                  NormalInput(
                    controller: passwordController,
                    obscureText: true,
                    decoration: BoxDecoration(
                      color: customTheme.inputBgColor,
                      borderRadius: BorderRadius.circular(3.w),
                      border: Border.all(
                        color: isPasswordError
                            ? customTheme.downColor
                            : Colors.transparent,
                      ),
                    ),
                    onChanged: (value) {
                      if (value.isNotEmpty && isPasswordError) {
                        setState(() {
                          isPasswordError = false;
                        });
                      }
                    },
                  ),
                  if (isPasswordError)
                    Padding(
                      padding: EdgeInsets.only(top: 7.h),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'please_enter_your_account_password'.tr,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: customTheme.downColor,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          WidgetUtil.getBottomButton(
            context: context,
            title: 'confirm'.tr,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }
}
