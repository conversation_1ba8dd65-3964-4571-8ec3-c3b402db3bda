import 'package:cached_network_image/cached_network_image.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_image.dart';
import 'package:flutter/material.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:provider/provider.dart';

enum CommonImageType {
  /// 正方形
  square,

  /// 横着的长方形
  hRectangle,

  /// 竖着的长方形
  vRectangle,
}

class CommonImage extends StatelessWidget {
  final CommonImageType? type;
  final String url;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget Function(BuildContext, String)? loadingBuilder;

  final Widget? errorWidgetChild;

  static String squareImg = 'assets/images/common/s_defaultImg.png';
  static String hRectangleImg = 'assets/images/common/h_defaultImg.png';
  static String vRectangleImg = 'assets/images/common/v_defaultImg.png';

  static const int imageMaxnum = 50;
  static const int imageLivenum = 50;

  const CommonImage(
    this.url, {
    super.key,
    CommonImageType? type,
    this.width,
    this.height,
    this.fit,
    this.loadingBuilder,
    this.errorWidgetChild,
  }) : type =
           type ??
           (width != null && height != null
               ? (width > height
                     ? CommonImageType.hRectangle
                     : (width == height
                           ? CommonImageType.square
                           : CommonImageType.vRectangle))
               : CommonImageType.hRectangle);

  String getDefaultImg(CustomThemeImage customThemeImage) {
    if (type == CommonImageType.square) {
      return customThemeImage.defaultSquareImg;
    }
    if (type == CommonImageType.vRectangle) {
      return customThemeImage.defaultVRectangleImg;
    }
    if (type == CommonImageType.hRectangle) {
      return customThemeImage.defaultHRectangleImg;
    }
    return customThemeImage.defaultHRectangleImg;
  }

  void _checkMemory() {
    ImageCache _imageCache = PaintingBinding.instance.imageCache;

    /// 因为图片有本地缓存机制，防止内存溢出
    if (_imageCache.currentSizeBytes >= 55 << 20 ||
        _imageCache.currentSize >= imageMaxnum ||
        _imageCache.liveImageCount >= imageLivenum) {
      _imageCache.clear();
      _imageCache.clearLiveImages();
    }
  }

  @override
  Widget build(BuildContext context) {
    final customThemeImage = Provider.of<ThemeProvider>(
      context,
    ).customThemeImage;
    final defaultImg = getDefaultImg(customThemeImage);

    if (url.isEmpty) {
      return Image.asset(
        defaultImg,
        fit: BoxFit.cover,
        width: width,
        height: height,
      );
    }
    _checkMemory();
    return CachedNetworkImage(
      imageUrl: CommonUtils.formatImg(url),
      placeholder:
          loadingBuilder ??
          (context, url) => Image.asset(
            defaultImg,
            fit: type == CommonImageType.hRectangle
                ? BoxFit.fitHeight
                : type == CommonImageType.vRectangle
                ? BoxFit.fitWidth
                : BoxFit.cover,
          ),
      errorWidget: (context, url, error) =>
          errorWidgetChild ?? Image.asset(defaultImg, fit: BoxFit.cover),
      width: width,
      height: height,
      fit: fit,
    );
  }
}
