// ignore_for_file: constant_identifier_names

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

const String _SOCKET_URL = 'ws://51.112.153.55:1793/kline';

/// WebSocket状态
enum SocketStatus {
  SocketStatusConnected, // 已连接
  SocketStatusFailed, // 失败
  SocketStatusClosed, // 连接关闭
}

class WebSocketUtil {
  WebSocketUtil._internal();

  static final WebSocketUtil _singleton = WebSocketUtil._internal();

  factory WebSocketUtil() => _singleton;

  String assetCurrency = '';

  WebSocketChannel? _webSocket; // WebSocket
  SocketStatus? _socketStatus; // socket状态
  ValueChanged<String?>? onError; // 连接错误回调
  Function? onOpen; // 连接开启回调
  Function? onMessage; // 接收消息回调

  String userId = '';

  Timer? _heartBeat; // 心跳定时器
  final int _heartTimes = 20000; // 心跳间隔(毫秒)
  final int _reconnectCount = 60; // 重连次数，默认60次
  int _reconnectTimes = 0; // 重连计数器
  Timer? _reconnectTimer; // 重连定时器

  /// 初始化WebSocket
  void initWebSocket({
    Function? onOpen,
    Function? onMessage,
    ValueChanged<String?>? onError,
  }) {
    this.onOpen = onOpen;
    this.onMessage = onMessage;
    this.onError = onError;
  }

  /// 开启WebSocket连接
  void openSocket(String _userId) async {
    if (_userId.isEmpty) {
      return;
    }
    userId = _userId;
    try {
      closeSocket(forceClose: true);
      String url = '$_SOCKET_URL/$_userId/C';
      // final wsUrl = Uri.parse(url);

      debugPrint('Attempting WebSocket connection to: $url');

      _webSocket = IOWebSocketChannel.connect(url);
      await _webSocket!.ready;

      debugPrint('WebSocket Connect Success: $url');

      // 连接成功，返回WebSocket实例
      _socketStatus = SocketStatus.SocketStatusConnected;
      // 连接成功，重置重连计数器
      _reconnectTimes = 0;
      if (_reconnectTimer != null) {
        _reconnectTimer?.cancel();
        _reconnectTimer = null;
      }
      onOpen?.call();
      // 接收消息
      _webSocket?.stream.listen(
        (data) => webSocketOnMessage(data),
        onError: webSocketOnError,
        onDone: webSocketOnDone,
      );
    } catch (e) {
      debugPrint('WebSocket Error: $e');
      // 添加更详细的错误处理
      _socketStatus = SocketStatus.SocketStatusFailed;
      onError?.call(e.toString());
    }
  }

  /// WebSocket接收消息回调
  webSocketOnMessage(data) {
    onMessage?.call(data);
  }

  /// WebSocket关闭连接回调
  webSocketOnDone() {
    debugPrint('WebSocket closed-------');
    reconnect();
  }

  /// WebSocket连接错误回调
  webSocketOnError(e) {
    WebSocketChannelException ex = e;
    _socketStatus = SocketStatus.SocketStatusFailed;
    onError?.call(ex.message);
    debugPrint('WebSocket error-------${ex.message}');
    closeSocket();
  }

  /// 关闭WebSocket
  void closeSocket({bool forceClose = false}) {
    if (_webSocket != null) {
      if (forceClose) {
        _reconnectTimes = _reconnectCount;
      }
      debugPrint('WebSocket连接关闭');
      _webSocket?.sink.close();
      destroyHeartBeat();
      _socketStatus = SocketStatus.SocketStatusClosed;
    }
  }

  /// 发送WebSocket消息
  void sendMessage(message) {
    if (_webSocket != null) {
      switch (_socketStatus) {
        case SocketStatus.SocketStatusConnected:
          debugPrint('WebSocket发送中：$message');
          _webSocket?.sink.add(message);
          break;
        case SocketStatus.SocketStatusClosed:
          debugPrint('WebSocket连接已关闭');
          break;
        case SocketStatus.SocketStatusFailed:
          debugPrint('WebSocket发送失败');
          break;
        default:
          break;
      }
    }
  }

  /// 初始化心跳
  void initHeartBeat() {
    destroyHeartBeat();
    _heartBeat = Timer.periodic(Duration(milliseconds: _heartTimes), (timer) {
      sentHeart();
    });
  }

  /// 心跳
  void sentHeart() {
    sendMessage('{"module": "HEART_CHECK", "message": "HEART_CHECK"}');
  }

  /// 销毁心跳
  void destroyHeartBeat() {
    if (_heartBeat != null) {
      _heartBeat?.cancel();
      _heartBeat = null;
    }
  }

  /// 重连机制
  void reconnect() {
    if (_reconnectTimes < _reconnectCount) {
      _reconnectTimes++;
      _reconnectTimer = Timer.periodic(Duration(milliseconds: _heartTimes), (
        timer,
      ) {
        openSocket(userId);
      });
    } else {
      if (_reconnectTimer != null) {
        // print('重连次数超过最大次数');
        _reconnectTimer?.cancel();
        _reconnectTimer = null;
      }
      return;
    }
  }
}
