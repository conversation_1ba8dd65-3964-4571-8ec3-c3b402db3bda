import 'package:aurenixai_app/models/user_node_level_res.dart';
import 'package:aurenixai_app/pages/portfolio/children/team_action.dart';
import 'package:aurenixai_app/providers/auth_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/extensions/date_extensions.dart';
import 'package:aurenixai_app/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import '../children/next_team.dart';

class TeamTable extends StatefulWidget {
  final bool hideAction;
  final bool isEmpty;
  final bool loading;
  final List<UserNodeLevelRes> teamList;

  const TeamTable({
    super.key,
    this.hideAction = false,
    this.teamList = const [],
    this.isEmpty = false,
    this.loading = true,
  });

  @override
  _TeamTableState createState() => _TeamTableState();
}

class _TeamTableState extends State<TeamTable> {
  ScrollController scrollController = ScrollController();
  double sliderValue = 0.0;

  @override
  void initState() {
    super.initState();
    scrollController.addListener(() {
      setState(() {
        sliderValue =
            scrollController.position.pixels /
            scrollController.position.maxScrollExtent;
      });
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    return Container(
      decoration: BoxDecoration(
        color: customTheme.inputBgColor,
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(color: customTheme.dividerColor, width: 0.5),
      ),
      margin: EdgeInsets.only(bottom: 40.h),
      child: widget.loading
          ? Padding(
              padding: EdgeInsets.symmetric(vertical: 40.h),
              child: Center(
                child: CircularProgressIndicator(color: customTheme.textColor),
              ),
            )
          : Column(
              children: [
                SingleChildScrollView(
                  controller: scrollController,
                  scrollDirection: Axis.horizontal,
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    children: [
                      _tableHead(),
                      if (widget.isEmpty)
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 40.h),
                          child: Center(
                            child: Text(
                              'no_record_found'.tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: customTheme.subTextColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        )
                      else
                        ..._tableBody(),
                    ],
                  ),
                ),
                Slider(
                  thumbColor: customTheme.primaryColor,
                  activeColor: customTheme.primaryColor,
                  inactiveColor: customTheme.dividerColor,
                  value: sliderValue,
                  onChanged: (value) {
                    setState(() {
                      sliderValue = value;
                    });
                    scrollController.jumpTo(
                      value * scrollController.position.maxScrollExtent,
                    );
                  },
                ),
              ],
            ),
    );
  }

  _tableHeadCell(
    double width, {
    String? title,
    Widget? child,
    bool isSmall = false,
    bool isLast = false,
  }) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    var textStyle = TextStyle(
      fontSize: 12.sp,
      color: customTheme.inputHintColor,
      fontWeight: FontWeight.w600,
    );
    var smallTextStyle = textStyle.copyWith(fontSize: 10.sp);

    return Container(
      decoration: isLast
          ? null
          : BoxDecoration(
              border: Border(
                right: BorderSide(color: customTheme.dividerColor, width: 0.5),
              ),
            ),
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      width: width,
      height: 53.h,
      child: Center(
        child:
            child ??
            Text(
              title ?? '',
              style: isSmall ? smallTextStyle : textStyle,
              textAlign: TextAlign.center,
            ),
      ),
    );
  }

  _tableHead() {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    List<double> widthList = [
      54.w,
      84.w,
      74.w,
      94.w,
      92.w,
      133.5.w,
      74.w,
      128.5.w,
    ];

    return Container(
      decoration: BoxDecoration(color: customTheme.ff1d1d24),
      child: Row(
        children: [
          _tableHeadCell(widthList[0], title: 'S/N'.tr),
          _tableHeadCell(widthList[1], title: 'UID'.tr),
          _tableHeadCell(widthList[2], title: 'full_name'.tr),
          _tableHeadCell(widthList[3], title: 'wealth_name'.tr, isSmall: true),
          _tableHeadCell(
            widthList[4],
            title: "${'overall_investment'.tr} (\$)",
            isSmall: true,
          ),
          _tableHeadCell(widthList[5], title: 'initial_funding_date'.tr),
          _tableHeadCell(widthList[6], title: 'Level'.tr),
          if (!widget.hideAction)
            _tableHeadCell(widthList[7], title: 'action'.tr, isLast: true),
        ],
      ),
    );
  }

  _tableBody() {
    return List.generate(widget.teamList.length, (index) {
      return _tableRow(index);
    });
  }

  _tableRowCell(
    double width, {
    double? height,
    String? title,
    bool isLast = false,
    bool highlight = false,
    bool isRowLast = false,
    Widget? child,
    VoidCallback? onTap,
  }) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    var textStyle = TextStyle(
      fontSize: 12.sp,
      color: highlight ? customTheme.primaryColor : customTheme.textColor,
    );

    Widget textWidget =
        child ??
        Text(title ?? '', style: textStyle, textAlign: TextAlign.center);

    if (onTap != null) {
      textWidget = GestureDetector(onTap: onTap, child: textWidget);
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: isLast
              ? BorderSide.none
              : BorderSide(color: customTheme.dividerColor, width: 0.5),
          bottom: isRowLast
              ? BorderSide.none
              : BorderSide(color: customTheme.dividerColor, width: 0.5),
        ),
      ),
      constraints: BoxConstraints(minHeight: height ?? 46.5.h),
      padding: EdgeInsets.symmetric(horizontal: child != null ? 0 : 5.w),
      width: width,
      child: Center(child: textWidget),
    );
  }

  _tableRowInnerCell(double width, {String? title, bool isLast = false}) {
    final customTheme = context.watch<ThemeProvider>().customTheme;
    var textStyle = TextStyle(fontSize: 12.sp, color: customTheme.textColor);

    return Container(
      decoration: isLast
          ? null
          : BoxDecoration(
              border: Border(
                bottom: BorderSide(color: customTheme.dividerColor, width: 0.5),
              ),
            ),
      constraints: BoxConstraints(minHeight: 46.5.h),
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      width: width,
      child: Center(
        child: Text(title ?? '', style: textStyle, textAlign: TextAlign.center),
      ),
    );
  }

  _button(int index) {
    final customTheme = context.watch<ThemeProvider>().customTheme;

    // 0:可申请,1:已拒绝,2:已通过,3:申请中
    final applyStatus = widget.teamList[index].applyStatus;

    Color btnBgColor = applyStatus == '3'
        ? customTheme.ff878C9f
        : applyStatus == '0'
        ? customTheme.buttonBgColor
        : customTheme.pageBgColor;
    Color btnTextColor = applyStatus == '1'
        ? customTheme.downColor
        : customTheme.textColor;
    String btnText = applyStatus == '3'
        ? 'requested'.tr
        : applyStatus == '0'
        ? 'advance_tier'.tr
        : 'rejected'.tr;

    return applyStatus == '0'
        ? TextButton(
            onPressed: () {
              Get.to(() => TeamActionPage(info: widget.teamList[index]));
            },
            style: TextButton.styleFrom(
              backgroundColor: btnBgColor,
              foregroundColor: btnTextColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 5.h),
              minimumSize: Size(98.5.w, 28.h),
              maximumSize: Size(98.5.w, 28.h),
              textStyle: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            child: Text(btnText),
          )
        : Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: btnBgColor,
              borderRadius: BorderRadius.circular(3.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 5.h),
            width: 98.5.w,
            height: 28.h,
            child: Text(
              btnText,
              style: TextStyle(
                fontSize: 12.sp,
                color: btnTextColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          );
  }

  _getDate(num dateTime) {
    return DateTime.fromMillisecondsSinceEpoch(dateTime.toInt()).ddMMyyyy;
  }

  _tableRow(int index, {bool isLast = false}) {
    final isUpAP = (context.watch<AuthProvider>().user?.nodeLevel ?? 1) > 2;
    List<double> widthList = [
      54.w,
      84.w,
      74.w,
      94.w,
      92.w,
      133.5.w,
      74.w,
      128.5.w,
    ];
    final item = widget.teamList[index];

    double height = 46.5.h;
    if (item.fundInvestmentRecordUserLevelPageResList.length > 1) {
      height = height * item.fundInvestmentRecordUserLevelPageResList.length;
    }
    double overallAllocation = 0;
    for (var element in item.fundInvestmentRecordUserLevelPageResList) {
      overallAllocation += element.totalInvested;
    }

    // 0:可申请,1:已拒绝,2:已通过,3:申请中
    bool hasButton = isUpAP && item.applyStatus != '2';

    return Row(
      children: [
        _tableRowCell(
          widthList[0],
          height: height,
          title: (index + 1).toString(),
          isRowLast: isLast,
        ),
        _tableRowCell(
          widthList[1],
          height: height,
          title: item.userId,
          highlight: true,
          isRowLast: isLast,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    NextTeamPage(uid: item.userId, name: item.userName),
              ),
            );
          },
        ),
        _tableRowCell(
          widthList[2],
          height: height,
          title: item.userName,
          isRowLast: isLast,
        ),
        if (item.fundInvestmentRecordUserLevelPageResList.length > 1)
          _tableRowCell(
            widthList[3],
            height: height,
            isRowLast: isLast,
            child: Column(
              children: List.generate(
                item.fundInvestmentRecordUserLevelPageResList.length,
                (index) {
                  return _tableRowInnerCell(
                    widthList[3],
                    title: item
                        .fundInvestmentRecordUserLevelPageResList[index]
                        .productName,
                    isLast:
                        index ==
                        item.fundInvestmentRecordUserLevelPageResList.length -
                            1,
                  );
                },
              ),
            ),
          )
        else
          _tableRowCell(
            widthList[3],
            height: height,
            title: item.fundInvestmentRecordUserLevelPageResList.isNotEmpty
                ? item
                      .fundInvestmentRecordUserLevelPageResList
                      .first
                      .productName
                : '-',
            isRowLast: isLast,
          ),
        _tableRowCell(
          widthList[4],
          height: height,
          title: overallAllocation.toStringAsFixed(2).removeZero.fmt,
          isRowLast: isLast,
        ),
        if (item.fundInvestmentRecordUserLevelPageResList.length > 1)
          _tableRowCell(
            widthList[5],
            height: height,
            child: Column(
              children: List.generate(
                item.fundInvestmentRecordUserLevelPageResList.length,
                (index) {
                  return _tableRowInnerCell(
                    widthList[5],
                    title: _getDate(
                      item
                          .fundInvestmentRecordUserLevelPageResList[index]
                          .createDatetime,
                    ),
                    isLast:
                        index ==
                        item.fundInvestmentRecordUserLevelPageResList.length -
                            1,
                  );
                },
              ),
            ),
            isRowLast: isLast,
          )
        else
          _tableRowCell(
            widthList[5],
            height: height,
            title: item.fundInvestmentRecordUserLevelPageResList.isNotEmpty
                ? _getDate(
                    item
                        .fundInvestmentRecordUserLevelPageResList
                        .first
                        .createDatetime,
                  )
                : '-',
            isRowLast: isLast,
          ),
        _tableRowCell(
          widthList[6],
          height: height,
          title: item.name,
          isRowLast: isLast,
        ),
        if (!widget.hideAction)
          _tableRowCell(
            widthList[7],
            height: height,
            isLast: true,
            child: hasButton ? _button(index) : SizedBox.shrink(),
            isRowLast: isLast,
          ),
      ],
    );
  }
}
