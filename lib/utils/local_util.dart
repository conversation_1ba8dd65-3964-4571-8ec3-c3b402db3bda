// ignore: depend_on_referenced_packages
import 'dart:convert';
import 'dart:ui';

import 'package:aurenixai_app/models/region.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/theme.dart';
import 'package:aurenixai_app/utils/theme/custom_theme_swatch.dart';
import 'package:shared_preferences/shared_preferences.dart';

const userToken = '_token_'; // token
const themeType = '_themeType_'; // 主题
const localeKey = '_localeKey_'; // 本地语言
const regionKey = '_regionKey_'; // 地区
const loginNameAndPwd = '_loginNameAndPwd_'; // 登录名、密码是否记住
const chooseMarketList = '_chooseMarketList_'; // 选择的市场列表

class LocalUtil {
  /// 设置用户token
  static Future<bool> setUserToken(String token) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.setString(userToken, token);
  }

  /// 获取用户token
  static Future<String?> getUserToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString(userToken);
    return token;
  }

  /// 删除用户token
  static Future<bool> removeUserToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.remove(userToken);
  }

  /// 设置主题类型
  static Future<bool> setThemeType(
    ThemeType type,
    ThemeProvider themeProvider,
  ) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    themeProvider.currentThemeType = type;
    return prefs.setString(themeType, type.toString());
  }

  /// 获取主题类型
  static Future<ThemeType> getThemeType() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? typeStr = prefs.getString(themeType);
    if (typeStr != null) {
      return ThemeUtil.themeTypeFromString(typeStr);
    }
    return ThemeType.dark;
  }

  /// 删除主题类型
  static Future<bool> removeThemeType() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.remove(themeType);
  }

  /// 设置本地语言
  static Future<bool> setLocale(Locale locale) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.setString(localeKey, locale.toString());
  }

  /// 获取本地语言
  static Future<Locale> getLocale() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? value = prefs.getString(localeKey);
    if (value != null) {
      final _list = value.split('_').toList();
      if (_list.length == 1) {
        return Locale(_list[0]);
      } else if (_list.length == 2) {
        return Locale(_list[0], _list[1]);
      }
    }
    return const Locale('en', 'US');
  }

  /// 删除本地语言
  static Future<bool> removeLocale() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.remove(localeKey);
  }

  /// 设置地区
  static Future<bool> setRegion(Region region) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.setString(regionKey, jsonEncode(region.toJson()));
  }

  /// 获取地区
  static Future<Region?> getRegion() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? value = prefs.getString(regionKey);
    if (value != null) {
      return Region.fromJson(jsonDecode(value));
    }
    return null;
  }

  /// 删除地区
  static Future<bool> removeRegion() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.remove(regionKey);
  }

  /// 设置登录名、密码是否记住
  static Future<bool> setLoginNameAndPwd(String loginName, String pwd) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.setString(
      loginNameAndPwd,
      jsonEncode({'loginName': loginName, 'pwd': pwd}),
    );
  }

  /// 获取登录名、密码是否记住
  static Future<Map<String, String>?> getLoginNameAndPwd() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? value = prefs.getString(loginNameAndPwd);
    if (value != null) {
      final Map<String, dynamic> decoded = jsonDecode(value);
      return {
        'loginName': decoded['loginName']?.toString() ?? '',
        'pwd': decoded['pwd']?.toString() ?? '',
      };
    }
    return null;
  }

  /// 删除登录名、密码是否记住
  static Future<bool> removeLoginNameAndPwd() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.remove(loginNameAndPwd);
  }

  /// 设置选择的市场列表
  static Future<bool> setChooseMarketList(List<String> symbolList) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.setStringList(chooseMarketList, symbolList);
  }

  /// 获取选择的市场列表
  static Future<List<String>?> getChooseMarketList() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final List<String>? value = prefs.getStringList(chooseMarketList);
    if (value != null) {
      return value;
    }
    return null;
  }
}
