import 'package:json_annotation/json_annotation.dart';

part 'sms_res.g.dart';

@JsonSerializable(genericArgumentFactories: false)
class SmsRes {
  SmsRes();

  late String id;
  late String type;
  late String bizType;
  late String title;
  late String content;
  late String isRead;
  late num updateDatetime;

  factory SmsRes.fromJson(Map<String, dynamic> json) => _$SmsResFromJson(json);
  Map<String, dynamic> toJson() => _$SmsResToJson(this);
}
