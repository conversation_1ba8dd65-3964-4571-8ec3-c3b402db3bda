import 'package:aurenixai_app/config/routes.dart';
import 'package:aurenixai_app/pages/public/rich_text_config.dart';
import 'package:aurenixai_app/providers/language_provider.dart';
import 'package:aurenixai_app/providers/theme_provider.dart';
import 'package:aurenixai_app/utils/index.dart';
import 'package:aurenixai_app/utils/toast_util.dart';
import 'package:aurenixai_app/widgets/normal_input.dart';
import 'package:aurenixai_app/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  _SignUpState createState() => _SignUpState();
}

class _SignUpState extends State<SignUpPage> {
  bool isPasswordVisible = false;
  bool isAgree = false;
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController invitationCodeController = TextEditingController();
  String passwordStrength = 'empty'; // empty, weak, medium, strong

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    invitationCodeController.dispose();
    super.dispose();
  }

  void _checkPasswordStrength() {
    final password = passwordController.text;
    if (password.isEmpty) {
      setState(() {
        passwordStrength = 'empty';
      });
      return;
    }

    // 检查密码长度是否至少8位
    if (password.length < 8) {
      setState(() {
        passwordStrength = 'weak';
      });
      return;
    }

    bool hasLetters = password.contains(RegExp(r'[a-zA-Z]'));
    bool hasNumbers = password.contains(RegExp(r'[0-9]'));
    bool hasSymbols = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    int typeCount = 0;
    if (hasLetters) typeCount++;
    if (hasNumbers) typeCount++;
    if (hasSymbols) typeCount++;

    String strength;
    switch (typeCount) {
      case 0:
        strength = 'empty';
        break;
      case 1:
        strength = 'weak';
        break;
      case 2:
        strength = 'medium';
        break;
      case 3:
        strength = 'strong';
        break;
      default:
        strength = 'empty';
    }

    setState(() {
      passwordStrength = strength;
    });
  }

  Color _getStrengthColor(int index) {
    final customTheme = context.read<ThemeProvider>().customTheme;
    switch (passwordStrength) {
      case 'empty':
        return customTheme.inputBgColor;
      case 'weak':
        return index == 0 ? const Color(0xFFEF5252) : customTheme.inputBgColor;
      case 'medium':
        return index < 2 ? const Color(0xFFEBAC18) : customTheme.inputBgColor;
      case 'strong':
        return const Color(0xFF36BA5D);
      default:
        return customTheme.inputBgColor;
    }
  }

  Color _getStrengthTextColor() {
    final customTheme = context.read<ThemeProvider>().customTheme;
    switch (passwordStrength) {
      case 'empty':
        return customTheme.inputBgColor;
      case 'weak':
        return const Color(0xFFEF5252);
      case 'medium':
        return const Color(0xFFEBAC18);
      case 'strong':
        return const Color(0xFF36BA5D);
      default:
        return customTheme.inputBgColor;
    }
  }

  void onSubmit() async {
    String email = emailController.text;
    String password = passwordController.text;
    String inviteCode = invitationCodeController.text;
    if (email.isEmpty) {
      ToastUtil.showToast('please_enter_your_email'.tr);
      return;
    }
    if (!email.isEmail) {
      ToastUtil.showToast('please_enter_a_valid_email'.tr);
      return;
    }
    if (password.isEmpty) {
      ToastUtil.showToast('please_enter_your_password'.tr);
      return;
    }
    if (password.length < 8) {
      ToastUtil.showToast('password_must_be_at_least_8_characters'.tr);
      return;
    }
    if (!isAgree) {
      ToastUtil.showToast(
        'please_agree_to_the_terms_and_conditions_and_privacy_policy'.tr,
      );
      return;
    }
    final languageProvider = context.read<LanguageProvider>();
    final region = languageProvider.region;
    if (region == null) {
      ToastUtil.showToast('Please select your region');
      return;
    }
    Get.toNamed(
      Routes.verifyEmail,
      arguments: {
        'email': email,
        'password': password,
        'countryCode': region.id,
        'inviteCode': inviteCode,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final customThemeImage = context.read<ThemeProvider>().customThemeImage;
    final customTheme = context.read<ThemeProvider>().customTheme;

    // 使用工具类获取AppBar高度
    final appBarHeight = CommonUtils.getAppBarHeight(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text('create_your_account_s'.tr),
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              customThemeImage.authBgBottom,
              width: MediaQuery.of(context).size.width,
              height: 313.h,
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
            ),
          ),
          Positioned.fill(
            left: 15.w,
            right: 15.w,
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: appBarHeight + 24.5.h),
                  Row(
                    spacing: 9.w,
                    children: [
                      Image.asset(customThemeImage.authSignUpLogo, width: 50.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'welcome_to'.tr,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: customTheme.textColor,
                            ),
                          ),
                          Text(
                            'AURENIXAI',
                            style: TextStyle(
                              fontSize: 26.sp,
                              color: customTheme.textColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 54.5.h),
                  NormalInput(
                    label: 'email'.tr,
                    hintText: 'enter_your_email'.tr,
                    controller: emailController,
                  ),
                  SizedBox(height: 27.h),
                  NormalInput(
                    label: 'password'.tr,
                    hintText: 'create_a_secure_password'.tr,
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: !isPasswordVisible,
                    controller: passwordController,
                    onChanged: (value) {
                      _checkPasswordStrength();
                    },
                    padding: EdgeInsets.zero,
                    contentPadding: EdgeInsets.symmetric(horizontal: 19.w),
                    suffix: GestureDetector(
                      onTap: () {
                        setState(() {
                          isPasswordVisible = !isPasswordVisible;
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.only(right: 12.w),
                        child: Icon(
                          isPasswordVisible
                              ? Icons.visibility
                              : Icons.visibility_off,
                          size: 20.w,
                          color: customTheme.textColor,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Row(
                    spacing: 7.5.w,
                    children: [
                      Expanded(
                        child: Container(
                          height: 12.w,
                          color: _getStrengthColor(0),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 12.w,
                          color: _getStrengthColor(1),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 12.w,
                          color: _getStrengthColor(2),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 5.h),
                  if (passwordStrength != 'empty')
                    Text(
                      passwordStrength.tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: _getStrengthTextColor(),
                      ),
                    ),
                  SizedBox(height: 12.h),
                  NormalInput(
                    label: 'invitation_code'.tr,
                    hintText: 'enter_your_invitation_code'.tr,
                    controller: invitationCodeController,
                  ),
                  SizedBox(height: 23.h),
                  Row(
                    spacing: 8.w,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            isAgree = !isAgree;
                          });
                        },
                        child: Image.asset(
                          isAgree
                              ? customThemeImage.authChoose
                              : customThemeImage.authUnchoose,
                          width: 18.w,
                        ),
                      ),
                      Expanded(
                        child: Wrap(
                          spacing: 3,
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            Text(
                              'by_proceeding_you_acknowledge_and_agree_to_the'
                                  .tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: customTheme.textColor,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => RichTextConfig(
                                      title: 'terms_and_conditions'.tr,
                                      configKey: 'terms_condition',
                                      configType: 'system',
                                    ),
                                  ),
                                );
                              },
                              child: RichText(
                                text: TextSpan(
                                  text: 'terms_and_conditions'.tr,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: customTheme.primaryColor,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ),
                            Text(
                              'and'.tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: customTheme.textColor,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => RichTextConfig(
                                      title: 'privacy_policy'.tr,
                                      configKey: 'privacy_policy',
                                      configType: 'system',
                                    ),
                                  ),
                                );
                              },
                              child: RichText(
                                text: TextSpan(
                                  text: 'privacy_policy'.tr,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: customTheme.primaryColor,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 62.h),
                  PrimaryButton(
                    title: 'create_your_account'.tr,
                    onPress: onSubmit,
                  ),
                  SizedBox(height: 18.5.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 3,
                    children: [
                      Text(
                        "already_have_an_account".tr,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: customTheme.textColor,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.toNamed(Routes.signIn);
                        },
                        child: RichText(
                          text: TextSpan(
                            text: 'sign_in_s'.tr,
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: customTheme.primaryColor,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 40.w),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
